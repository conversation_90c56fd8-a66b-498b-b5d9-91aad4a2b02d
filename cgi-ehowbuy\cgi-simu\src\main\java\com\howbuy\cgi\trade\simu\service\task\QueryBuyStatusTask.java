package com.howbuy.cgi.trade.simu.service.task;

import com.howbuy.cgi.common.remote.param.RemoteParametersProvider;
import com.howbuy.cgi.trade.simu.model.dto.QueryBalanceContext;
import com.howbuy.cgi.trade.simu.model.dto.QueryBalanceDetailContext;
import com.howbuy.interlayer.common.Constants;
import com.howbuy.tms.high.orders.facade.search.querybuyfundstatus.QueryBuyFundStatusFacade;
import com.howbuy.tms.high.orders.facade.search.querybuyfundstatus.QueryBuyFundStatusRequest;
import com.howbuy.tms.high.orders.facade.search.querybuyfundstatus.QueryBuyFundStatusResponse;
import org.apache.commons.collections.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description:产品购买状态
 * @Author: yun.lu
 * Date: 2025/8/4 11:22
 */
public class QueryBuyStatusTask extends HowbuyBaseTask {
    private QueryBalanceContext queryBalanceContext;
    private String custIP;
    private String custNo;
    private List<String> productCodeList;
    private QueryBuyFundStatusFacade queryBuyFundStatusFacade;

    @Override
    protected void callTask() {
        QueryBuyFundStatusRequest request = new QueryBuyFundStatusRequest();
        request.setProductCodeList(productCodeList);
        request.setDisCode(Constants.DIS_CODE_HOWBUY);
        request.setOutletCode(Constants.OUTLET_CODE_HOWBUY);
        request.setOperIp(custIP);
        request.setTxAcctNo(custNo);
        request.setTxChannel(RemoteParametersProvider.getTradeChannel());
        QueryBuyFundStatusResponse response = queryBuyFundStatusFacade.execute(request);

        Map<String, QueryBuyFundStatusResponse.BuyFundStatusBean> buyFundStatusMap = new HashMap<>();
        if (response != null && CollectionUtils.isNotEmpty(response.getBuyFundStatusList())) {
            for (QueryBuyFundStatusResponse.BuyFundStatusBean bean : response.getBuyFundStatusList()) {
                buyFundStatusMap.put(bean.getProductCode(), bean);
            }
        }

        queryBalanceContext.setBuyFundStatusMap(buyFundStatusMap);
    }

    public QueryBalanceContext getQueryBalanceContext() {
        return queryBalanceContext;
    }

    public void setQueryBalanceContext(QueryBalanceContext queryBalanceContext) {
        this.queryBalanceContext = queryBalanceContext;
    }

    public String getCustIP() {
        return custIP;
    }

    public void setCustIP(String custIP) {
        this.custIP = custIP;
    }

    public String getCustNo() {
        return custNo;
    }

    public void setCustNo(String custNo) {
        this.custNo = custNo;
    }

    public List<String> getProductCodeList() {
        return productCodeList;
    }

    public void setProductCodeList(List<String> productCodeList) {
        this.productCodeList = productCodeList;
    }

    public QueryBuyFundStatusFacade getQueryBuyFundStatusFacade() {
        return queryBuyFundStatusFacade;
    }

    public void setQueryBuyFundStatusFacade(QueryBuyFundStatusFacade queryBuyFundStatusFacade) {
        this.queryBuyFundStatusFacade = queryBuyFundStatusFacade;
    }

    public QueryBuyStatusTask(QueryBalanceContext queryBalanceContext, String custIP, String custNo, List<String> productCodeList, QueryBuyFundStatusFacade queryBuyFundStatusFacade) {
        this.queryBalanceContext = queryBalanceContext;
        this.custIP = custIP;
        this.custNo = custNo;
        this.productCodeList = productCodeList;
        this.queryBuyFundStatusFacade = queryBuyFundStatusFacade;
    }
}
