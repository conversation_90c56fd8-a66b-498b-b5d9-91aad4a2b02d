# CGI私募交易系统项目级别规范指南

## 项目概述
CGI私募交易系统(cgi-simu-server)是好买基金CGI网关接口系统的私募交易模块，负责处理私募基金交易、账户管理、资产查询等核心业务功能。项目采用Spring Boot + Spring Cloud微服务架构，基于Maven多模块管理。

项目配置:
  编程语言: Java
  JDK版本: 1.8
  框架: Spring Boot 2.3.12.RELEASE, Spring Cloud Hoxton.SR12
  数据库: MySQL, Oracle
  缓存: Redis
  数据库操作: MyBatis Plus 3.5.7
  数据库连接池: DBCP2
  服务注册发现: Nacos
  RPC框架: Dubbo 3.2.12
  日志框架: Log4j2 2.17.0

### 模块划分
- **cgi-ehowbuy**: 主业务模块
  - **cgi-container**: 部署容器模块，包含启动类和配置
    - 基础包名: `com.howbuy.cgi.trade`
    - 启动类: `com.howbuy.cgi.trade.SimuCgiMainApplication`
    - 配置目录: `src/main/resources/`
  - **cgi-simu**: 私募交易业务模块
    - 基础包名: `com.howbuy.cgi.trade.simu`
    - 控制器: `com.howbuy.cgi.trade.simu.controller`
    - 业务服务: `com.howbuy.cgi.trade.simu.service`
    - 数据传输对象: `com.howbuy.cgi.trade.simu.dto`
    - 命令对象: `com.howbuy.cgi.trade.simu.model.cmd`
    - 视图对象: `com.howbuy.cgi.trade.simu.model.vo`
    - 工具类: `com.howbuy.cgi.trade.simu.util`
    - 配置类: `com.howbuy.cgi.trade.simu.config`
    - 过滤器: `com.howbuy.cgi.trade.simu.filter`
    - 拦截器: `com.howbuy.cgi.trade.simu.interceptor`

## 命名规范

### 通用命名规则
- **类名**: 使用PascalCase（首字母大写的驼峰命名法），如`BuyController`、`AccCenterService`
- **方法名和变量名**: 使用camelCase（首字母小写的驼峰命名法），如`queryTradeDetail`、`hboneNo`
- **常量**: 使用大写字母和下划线分隔，如`SUCCESS_CODE`、`CGI_SIMU_CONSTANTS`
- **包名**: 全小写，使用点分隔，如`com.howbuy.cgi.trade.simu.service`

### 特定组件命名规则

#### 控制器和实体类
- **Controller类**: 业务功能+`Controller`，如`BuyController`、`QueryFundController`
- **Service类**: 业务功能+`Service`，如`AccCenterService`、`QueryBalanceService`
- **命令对象**: 业务功能+`Cmd`，如`BuyCmd`、`QueryRecordsCmd`
- **数据传输对象**: 业务功能+`Dto`，如`QueryTradeDetailDto`、`AcctDataAuthDto`
- **视图对象**: 业务功能+`Vo`，如`SiMuIndexVo`、`QueryFormTemplateVo`
- **模型对象**: 业务功能+`Model`，如`HighProductInfoModel`、`KycModel`

#### 工具和配置类
- **工具类**: 功能+`Util`，如`BusiUtil`、`ValidateUtil`
- **配置类**: 功能+`Config`，如`RedisConfig`、`DubboConfig`
- **过滤器**: 功能+`Filter`，如`SystemExceptionMonitorFilter`
- **拦截器**: 功能+`Interceptor`，如`LoginInterceptor`

## 控制器层规范

### Controller定义规范
1. 控制器必须继承`AbstractSimuCGIController`或`AbstractCGIController`
2. 使用`@Controller`注解标注控制器类
3. 使用`@RequestMapping`注解定义请求映射，路径以`/simu/`开头
4. 控制器必须使用APIDOC风格注释，包含以下内容：
   - API路径(`@api`)
   - API版本(`@apiVersion`)
   - API组(`@apiGroup`)
   - API名称(`@apiName`)
   - API描述(`@apiDescription`)
   - 请求参数(`@apiParam`)
   - 请求示例(`@apiParamExample`)
   - 响应结果(`@apiSuccess`)
   - 响应示例(`@apiSuccessExample`)

```java
/**
 * @api {GET} /simu/trade/buy.htm buy
 * @apiVersion 1.0.0
 * @apiGroup BuyController
 * @apiName buy
 * @apiDescription 私募基金申购
 * @apiParam (请求参数) {String} fundCode 基金代码
 * @apiParam (请求参数) {String} buyAmt 申购金额
 * @apiParamExample 请求参数示例
 * fundCode=PE0032&buyAmt=100000
 * @apiSuccess (响应结果) {String} dealNo 交易流水号
 * @apiSuccess (响应结果) {String} submitTaDt 预计上报日期
 * @apiSuccessExample 响应结果示例
 * {"dealNo":"20250122001","submitTaDt":"20250123"}
 */
@RequestMapping("/simu/trade/buy.htm")
public void buy(HttpServletRequest request, HttpServletResponse response) throws IOException {
    // 控制器实现
}
```

### 请求处理规范
1. 使用`getCustSession()`获取用户会话信息
2. 使用`getCommand(Class<T> clazz)`获取请求参数对象
3. 使用`getString(String paramName)`获取字符串参数
4. 使用`ValidateUtil.assertValid()`进行参数校验
5. 使用`write(Object data, String contentType, HttpServletResponse response)`返回响应

```java
@RequestMapping("/simu/trade/queryBalance.htm")
public void queryBalance(HttpServletRequest request, HttpServletResponse response) throws Exception {
    // 获取用户会话
    TradeSession loginInfo = this.getCustSession();

    // 获取请求参数
    QueryBalanceParamCmd cmd = getCommand(QueryBalanceParamCmd.class);
    ValidateUtil.assertValid(cmd);

    // 业务处理
    QueryBalanceDto result = queryBalanceService.queryBalance(cmd, loginInfo);

    // 返回响应
    write(result, CGIConstants.RESPONSE_CONTENT_TYPE_CIPHERTEXT, response);
}
```

## 服务层调用规范

### Service层
1. Service类应位于`com.howbuy.cgi.trade.simu.service`包下，根据业务功能划分子包
2. 使用`@Service`注解将服务注册到Spring容器
3. 使用`@Slf4j`注解添加日志支持
4. 通过`@Autowired`或`@Resource`注入其他Service类和Facade接口
5. 实现具体业务逻辑，可以调用外部Dubbo服务
6. 方法应有完整的Javadoc注释

```java
@Service
@Slf4j
public class AccCenterService {
    @Autowired
    @Qualifier("queryAccCustInfoFacade")
    private QueryAccCustInfoFacade queryAccCustInfoFacade;

    /**
     * 获取香港数据隔离状态
     *
     * @param hbOneNo 一账通
     * @return 数据授权信息
     */
    public AcctDataAuthDto getAcctDataAuthInfo(String hbOneNo) {
        QueryAccCustInfoRequest request = new QueryAccCustInfoRequest();
        request.setHboneNo(hbOneNo);
        log.info("查询用户数据权限信息,hbOneNo={}", hbOneNo);

        QueryAccCustInfoResponse response = queryAccCustInfoFacade.execute(request);
        log.info("查询用户数据权限信息结果:response={}", JSON.toJSONString(response));

        // 业务逻辑处理
        return buildAcctDataAuthDto(response);
    }
}
```

## 异常处理规范

1. 使用统一的异常处理机制
2. 业务异常使用`BizException`，继承自应用的基础异常类
3. 使用`BizErrorEnum`定义错误码和错误信息
4. 异常信息应包含足够的上下文信息，便于问题定位
5. 不要捕获异常后不处理或仅打印日志

```java
// 参数校验异常
if (StringUtils.isBlank(fundCode)) {
    log.info("getFundPlAuth-产品编码不能为空");
    throw new BizException(BizErrorEnum.REQUEST_PARAM_ERROR.getCode(),
                          BizErrorEnum.REQUEST_PARAM_ERROR.getDesc());
}

// 业务逻辑异常
if (!SUCCESS_CODE.equals(response.getReturnCode())) {
    log.error("查询用户信息失败: {}", response.getReturnMsg());
    throw new BizException(BizErrorEnum.SYSTEM_ERROR.getCode(),
                          "查询用户信息失败");
}
```

## 日志规范

1. 使用SLF4J + Log4j2进行日志记录
2. 日志级别合理使用:
   - ERROR: 系统错误，需要立即关注的问题
   - WARN: 潜在的问题，可能需要关注
   - INFO: 重要业务操作，可用于生产环境问题跟踪
   - DEBUG: 调试信息，仅在开发和测试环境使用

3. 日志内容应包含足够的上下文信息
4. 敏感信息不应记录到日志中
5. 使用占位符而非字符串拼接

```java
// 正确的做法
log.info("查询交易详情,dealNo:{}", dealNo);
log.info("查询用户数据权限信息,hbOneNo={}", hbOneNo);

// 错误的做法
log.info("查询交易详情,dealNo:" + dealNo);
```

## 注释规范

### 类注释
```java
/**
 * Copyright (c) 2025, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

/**
 * @Description: 账户中心服务
 * @Author: developer.name
 * @Date: 2025-01-22 10:00:00
 */
```

### 方法注释
```java
/**
 * 查询手写签名base64
 *
 * @param idNo 用户证件号
 * @return 手写签名base64
 */
public HandSignSealBean queryHandSignSealData(String idNo) {
    // 方法实现
}
```

## 配置规范

### Spring Boot配置
1. 主配置文件使用`application.properties`
2. 环境特定配置使用`bootstrap.properties`
3. 使用Nacos作为配置中心
4. 健康检查端口配置为39999

### Spring XML配置
1. 主配置文件为`spring-all.xml`
2. 按功能模块拆分配置文件，使用`import`导入
3. 配置文件命名规范：`context-功能模块.xml`

## 依赖注入规范

1. 优先使用`@Autowired`注解进行依赖注入
2. 当存在多个同类型Bean时，使用`@Qualifier`注解指定具体的Bean名称
3. 对于外部Dubbo服务，必须使用`@Qualifier`注解指定Bean名称

```java
@Autowired
@Qualifier("queryAccCustInfoFacade")
private QueryAccCustInfoFacade queryAccCustInfoFacade;

@Autowired
@Qualifier("simu.kycInfoFacade")
private KycInfoFacade kycInfoFacade;
```

## 安全规范

1. 敏感数据（如密码、证件号）需要加密处理
2. API调用需要进行身份验证和授权
3. 防止SQL注入、XSS等常见安全问题
4. 日志中不应包含敏感信息
5. 错误响应不应暴露系统内部信息
6. 使用`getAndCheckLogin()`方法进行登录状态校验

```java
// 登录校验
String hboneNo = getAndCheckLogin();

// 敏感信息处理
if (CgiParamUtil.isH5() && CgiParamUtil.isEncParam()) {
    dto.setMobile(getEncParamters(dto.getMobile()));
}
```

## 性能优化指南

1. 合理使用缓存减少外部服务调用
2. 避免N+1查询问题
3. 使用批量操作替代循环单条操作
4. 大数据量处理时使用分页查询
5. 合理设置连接池参数
6. 使用异步处理提高并发能力
7. 避免在循环中进行外部服务调用

```java
// 批量查询产品分类信息
Map<String, String> cpflMap = getCpflMap(productCodeList);

// 缓存使用示例
String buyPreKey = CacheKeyUtil.getBuyPreinfoCacheKey(request, user.getHboneNo());
PrebookDetailDto prebookDetailDto = getCacheBuyPre(buyPreKey);
```

## 代码审查重点

在进行代码审查时，应重点关注以下方面：

1. **命名规范**: 类名、方法名、变量名是否符合规范
2. **控制器定义**: Controller是否继承正确的基类，是否有完整的API文档注释
3. **参数处理**: 是否正确使用getCommand()和参数校验
4. **异常处理**: 是否使用统一的异常处理机制，是否合理使用自定义异常
5. **日志规范**: 是否使用了正确的日志级别，日志内容是否合适
6. **依赖注入**: 是否正确使用@Autowired和@Qualifier注解
7. **响应处理**: 是否使用统一的write()方法返回响应
8. **会话管理**: 是否正确获取和使用用户会话信息
9. **安全校验**: 是否进行必要的登录和权限校验

## 业务错误码规范

错误码应具有一定的结构，便于问题定位和排查。建议使用以下格式：

- 0000: 成功
- 1xxx: 参数错误
- 2xxx: 业务逻辑错误
- 3xxx: 权限错误
- 9xxx: 系统错误

每个错误码都应在`BizErrorEnum`中定义，包含错误码和错误描述。

```java
public enum BizErrorEnum {
    REQUEST_PARAM_ERROR("1001", "请求参数错误"),
    NEED_RELOGIN("3001", "需要重新登录"),
    SYSTEM_ERROR("9999", "系统错误");

    private String code;
    private String desc;
}
```

## 测试规范

1. 单元测试应覆盖主要业务逻辑
2. 使用MockMvc进行Controller层测试
3. 使用Mockito进行Service层测试
4. 测试类命名规范：被测试类名+Test
5. 测试方法命名规范：test+被测试方法名+测试场景

```java
@RunWith(SpringRunner.class)
@WebMvcTest(BuyController.class)
public class BuyControllerTest {

    @Test
    public void testBuy_Success() {
        // 测试申购成功场景
    }

    @Test
    public void testBuy_ParamError() {
        // 测试参数错误场景
    }
}
```

## 部署和运维规范

1. 使用Maven进行项目构建和依赖管理
2. 使用Spring Boot Actuator进行健康检查
3. 健康检查端口：39999
4. 应用名称：cgi-simu-container
5. 使用Nacos进行服务注册和配置管理
6. 日志文件统一使用Log4j2配置

## 附录

### 常用常量定义
- `CGIConstants.RESPONSE_CONTENT_TYPE_CIPHERTEXT`: 响应内容类型
- `CGISimuConstants.SUCCESS_CODE`: 成功状态码
- `CGISimuConstants.HK_SUCCESS_CODE`: 香港接口成功状态码

### 常用工具类
- `ValidateUtil`: 参数校验工具类
- `BusiUtil`: 业务工具类
- `CacheKeyUtil`: 缓存Key生成工具类
- `WebUtil`: Web相关工具类

### 外部服务接口
- 账户中心服务：`QueryAccCustInfoFacade`、`KycInfoFacade`
- 高端订单服务：`QueryHighDealOrderDtlFacade`
- 海外订单服务：`QueryBalanceDetailFacade`
- 产品中心服务：`HighProductService`

### 数据库表关系
- 主要涉及用户信息、交易记录、产品信息等相关表
- 支持MySQL和Oracle数据库
- 使用MyBatis Plus进行数据库操作