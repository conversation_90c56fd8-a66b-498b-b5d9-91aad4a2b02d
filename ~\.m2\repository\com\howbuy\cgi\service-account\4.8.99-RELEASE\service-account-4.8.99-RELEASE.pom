<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.howbuy.cgi</groupId>
		<artifactId>cgi-service</artifactId>
		<version>4.8.99-RELEASE</version>
	</parent>

	<name>service-account</name>
	<artifactId>service-account</artifactId>
	<version>4.8.99-RELEASE</version>

	<dependencies>

		<dependency>
			<groupId>com.howbuy</groupId>
			<artifactId>param-server-facade</artifactId>
		</dependency>

		<dependency>
			<groupId>com.howbuy.crm</groupId>
			<artifactId>crm-core-client</artifactId>
		</dependency>
		<dependency>
			<groupId>com.howbuy.hkacconline</groupId>
			<artifactId>hk-acc-online-facade</artifactId>
		</dependency>

		<dependency>
			<groupId>com.howbuy.fbs</groupId>
			<artifactId>fbs-online-facade</artifactId>
		</dependency>

		<dependency>
			<groupId>com.howbuy.fbs</groupId>
			<artifactId>fbs-online-search-facade</artifactId>
		</dependency>

		<dependency>
			<groupId>com.howbuy.fbs</groupId>
			<artifactId>fbs-common-facade</artifactId>
		</dependency>

		<dependency>
			<groupId>com.howbuy.finonline</groupId>
			<artifactId>fin-online-facade</artifactId>
		</dependency>

		<dependency>
			<groupId>com.howbuy.interlayer</groupId>
			<artifactId>product-center-model</artifactId>
		</dependency>

		<dependency>
			<groupId>com.howbuy.interlayer</groupId>
	    	<artifactId>product-center-client</artifactId>
		</dependency>

		<dependency>
			<groupId>com.howbuy.tms</groupId>
			<artifactId>robot-order-center-client</artifactId>
		</dependency>

		<dependency>
			<groupId>com.howbuy.crm</groupId>
			<artifactId>crm-td-client</artifactId>
		</dependency>
		
		<dependency>
			<groupId>com.howbuy.pdc</groupId>
			<artifactId>pdc-online-facade</artifactId>
		</dependency>
		
		<dependency>
			<groupId>com.howbuy</groupId>
			<artifactId>gateway-bxebank-client</artifactId>
		</dependency>
		
		<dependency>
			<groupId>com.howbuy</groupId>
			<artifactId>gateway-captcha-client</artifactId>
		</dependency>

		<dependency>
			<groupId>com.howbuy</groupId>
			<artifactId>pay-common-model</artifactId>
		</dependency>
		
		<dependency>
			<groupId>com.howbuy.cgi</groupId>
			<artifactId>service-kyc</artifactId>
		</dependency>
		
		<dependency>
			<groupId>com.howbuy.cgi</groupId>
			<artifactId>service-common</artifactId>
		</dependency>
		
		<dependency>
			<groupId>com.howbuy.acccenter</groupId>
			<artifactId>acc-center-facade</artifactId>
		</dependency>

		<dependency>
			<groupId>com.howbuy.acccenter</groupId>
			<artifactId>acc-center-common</artifactId>
		</dependency>
		
		<dependency>
			<groupId>com.howbuy.acc</groupId>
			<artifactId>acc-common-utils</artifactId>
		</dependency>
		
		<dependency>
			<groupId>com.howbuy.tms</groupId>
			<artifactId>csdc-pre-client</artifactId>
		</dependency>

		<dependency>
			<groupId>com.howbuy</groupId>
			<artifactId>howbuy-weixin-client</artifactId>
		</dependency>

		<dependency>
			   <groupId>com.howbuy.tms</groupId>
			   <artifactId>order-center-client</artifactId>
		</dependency>
		
		<dependency>
			<groupId>com.howbuy</groupId>
			<artifactId>HowbuyServiceBus</artifactId>
		</dependency>
		
		<dependency>
			<groupId>dom4j</groupId>
			<artifactId>dom4j</artifactId>
		</dependency>
		
		<dependency>
			<groupId>javax.servlet</groupId>
			<artifactId>javax.servlet-api</artifactId>
			<scope>provided</scope>
		</dependency>

<!--		<dependency>-->
<!--			<groupId>com.howbuy</groupId>-->
<!--			<artifactId>howbuyNotification</artifactId>-->
<!--		</dependency>-->

        <dependency>
            <groupId>com.howbuy.tms</groupId>
            <artifactId>high-order-center-client</artifactId>
        </dependency>
        
		<dependency>
			<groupId>com.howbuy</groupId>
			<artifactId>ftx-order-facade</artifactId>
		</dependency>

		<dependency>
			<groupId>com.howbuy.tms</groupId>
			<artifactId>elasticsearch-center-client</artifactId>
		</dependency>

		<dependency>
			<groupId>com.howbuy.tms</groupId>
			<artifactId>pension-order-client</artifactId>
		</dependency>
		<dependency>
			<groupId>com.howbuy</groupId>
			<artifactId>gateway-taxbank-client</artifactId>
		</dependency>

	</dependencies>

</project>