package com.howbuy.cgi.trade.simu.service;

import com.alibaba.fastjson.JSON;
import com.howbuy.cgi.common.enums.BizErrorEnum;
import com.howbuy.cgi.common.enums.ReturnCodeEnum;
import com.howbuy.cgi.common.exception.BizException;
import com.howbuy.cgi.common.remote.param.RemoteParametersProvider;
import com.howbuy.cgi.common.trace.RequestChainTrace;
import com.howbuy.cgi.common.trace.TraceParam;
import com.howbuy.cgi.trade.ccms.simu.SimuCcmsServiceRegister;
import com.howbuy.cgi.trade.simu.common.CGISimuConstants;
import com.howbuy.cgi.trade.simu.common.enums.CurrencyEnum;
import com.howbuy.cgi.trade.simu.common.enums.FundSetTypeEnum;
import com.howbuy.cgi.trade.simu.model.cmd.QueryBalanceParamCmd;
import com.howbuy.cgi.trade.simu.model.cmd.QueryFinReceiptParamCmd;
import com.howbuy.cgi.trade.simu.model.dto.AcctDataAuthCheckDto;
import com.howbuy.cgi.trade.simu.model.dto.AcctDataAuthDto;
import com.howbuy.cgi.trade.simu.model.vo.*;
import com.howbuy.cgi.trade.simu.service.relatedaccount.AccCenterService;
import com.howbuy.cgi.trade.simu.service.relatedaccount.InviteService;
import com.howbuy.cgi.trade.simu.service.relatedaccount.RelatedAccountService;
import com.howbuy.cgi.trade.simu.service.task.*;
import com.howbuy.dtms.order.client.domain.request.finrecept.QueryFinReciptRequest;
import com.howbuy.dtms.order.client.domain.response.Response;
import com.howbuy.dtms.order.client.facade.query.balance.QueryBalanceFacade;
import com.howbuy.dtms.order.client.facade.query.balance.QueryFinreceiptFacade;
import com.howbuy.kyc.service.KycService;
import com.howbuy.paramcenter.serverfacade.dis.queryhbdiscodelist.QueryHbDisCodeListFacade;
import com.howbuy.paramcenter.serverfacade.dis.queryhbdiscodelist.QueryHbDisCodeListRequest;
import com.howbuy.paramcenter.serverfacade.dto.DisDto;
import com.howbuy.paramcenter.vo.Result;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.enums.busi.*;
import com.howbuy.tms.common.utils.DateUtils;
import com.howbuy.tms.high.batch.facade.common.Constant;
import com.howbuy.tms.high.orders.facade.search.queryacctbalance.QueryAcctBalanceFacade;
import com.howbuy.tms.high.orders.facade.search.queryacctbalance.QueryAcctBalanceRequest;
import com.howbuy.tms.high.orders.facade.search.queryacctbalance.QueryAcctBalanceResponse;
import com.howbuy.tms.high.orders.facade.search.queryacctbalance.UnconfirmeProduct;
import com.howbuy.tms.high.orders.facade.search.queryacctbalanceNew.QueryAcctBalanceNewFacade;
import com.howbuy.tms.high.orders.facade.search.queryfinreceipt.QueryFinReceiptFacade;
import com.howbuy.tms.high.orders.facade.search.queryfinreceipt.QueryFinReceiptRequest;
import com.howbuy.tms.high.orders.facade.search.queryfinreceipt.QueryFinReceiptResponse;
import com.howbuy.tms.high.orders.facade.search.queryhighfundinvplanlist.QueryHighFundInvPlanListFacade;
import com.howbuy.tms.high.orders.facade.search.querysupsignagreementlist.QuerySupSignAgreementListFacade;
import com.howbuy.trade.account.model.hbone.MemberCustInfoModel;
import com.howbuy.trade.account.service.hbone.HboneService;
import com.howbuy.web.util.WebUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description:查询持仓份额服务
 * @Author: yun.lu
 * Date: 2023/9/27 17:42
 */
@Service
public class QueryBalanceVolService {
    private static final Logger log = LogManager.getLogger(QueryBalanceVolService.class);

    @Autowired
    @Qualifier("simu.queryHbDisCodeListFacade")
    private QueryHbDisCodeListFacade queryHbDisCodeListFacade;

    @Autowired
    @Qualifier("simu.queryAcctBalanceNewFacade")
    private QueryAcctBalanceNewFacade queryAcctBalanceNewFacade;

    @Autowired
    @Qualifier("simu.queryAcctBalanceFacade")
    private QueryAcctBalanceFacade queryAcctBalanceFacade;

    @Autowired
    @Qualifier("simu.queryFinReceiptFacade")
    private QueryFinReceiptFacade queryFinReceiptFacade;

    @Autowired
    @Qualifier("simu.querySupSignAgreementListFacade")
    private QuerySupSignAgreementListFacade querySupSignAgreementListFacade;

    @Autowired
    @Qualifier("simu.queryHighFundInvPlanListFacade")
    private QueryHighFundInvPlanListFacade queryHighFundInvPlanListFacade;

    @Autowired
    private SimuCcmsServiceRegister simuCcmsServiceRegister;
    @Autowired
    private KycService kycService;

    @Autowired
    private RelatedAccountService relatedAccountService;

    @Autowired
    private InviteService inviteService;

    @Autowired
    private QueryAcctOtherInfoService queryAcctOtherInfoService;
    @Autowired
    private HboneService hboneService;
    @Autowired
    private HowBuyRunTaskUil howBuyRunTaskUil;
    @Autowired
    @Qualifier("dtms.queryBalanceFacade")
    private QueryBalanceFacade queryBalanceFacade;

    @Autowired
    AccCenterService accCenterService;

    @Autowired
    @Qualifier("dtms.queryFinreceiptFacade")
    private QueryFinreceiptFacade hkQueryFinreceiptFacade;

    private static final String ZERO = "0";

    /**
     * 查询持仓首页信息
     *
     * @param queryBalanceParamCmd 入参
     * @return 私募首页信息
     */
    public SiMuIndexVo queryIndexBalanceInfo(QueryBalanceParamCmd queryBalanceParamCmd) {
        SiMuIndexVo siMuIndexVo = new SiMuIndexVo();
        AcctDataAuthDto acctDataAuthInfo = queryBalanceParamCmd.getAcctDataAuthInfo();
        siMuIndexVo.setIsDataAuth(acctDataAuthInfo.getIsDataAuth());
        siMuIndexVo.setIsHkDataQuarantine(acctDataAuthInfo.getIsHkDataQuarantine());
        // 2.异步查询持仓信息
        List<HowbuyBaseTask> taskArrayList = new ArrayList<>();
        List<QueryAcctBalanceResponse> responseList = new ArrayList<>(1);
        taskArrayList.add(asyncQueryAcctBalance(queryBalanceParamCmd, responseList));
        // 3.异步设置账户相关信息
        taskArrayList.add(asyncSetAccountInfo(queryBalanceParamCmd, siMuIndexVo));
        // 4.异步查询在途订单信息
        List<QueryFinReceiptResponse> queryFinReceiptResponseList = new ArrayList<>(1);
        taskArrayList.add(asyncQueryFinReceipt(queryBalanceParamCmd, queryFinReceiptResponseList));
        // 5.异步查询是否私募定投
        QueryHighFundInvPlanFlagTask queryHighFundInvPlanFlagTask = asyncQueryHighFundInvPlanFlag(queryBalanceParamCmd, siMuIndexVo);
        taskArrayList.add(queryHighFundInvPlanFlagTask);
        // 6.异步设置收益,理财通接口
        SetShouYiLiCaiTask setShouYiLiCaiTask = asyncSetShouYiLiCai(queryBalanceParamCmd, siMuIndexVo);
        taskArrayList.add(setShouYiLiCaiTask);
        howBuyRunTaskUil.runTask(taskArrayList);
        if (CollectionUtils.isEmpty(responseList)) {
            throw new BizException(BizErrorEnum.REQUEST_URI_BANNED.getCode(), "查询用户持仓返回是空的!");
        }
        QueryAcctBalanceResponse balanceResponse = responseList.get(0);
        if (!ReturnCodeEnum.SUCC_TMS.getCode().equals(balanceResponse.getReturnCode())) {
            throw new BizException(balanceResponse.getReturnCode(), balanceResponse.getDescription());
        }
        // 7.未开户标识
        setNoTxAcctFlag(queryBalanceParamCmd, siMuIndexVo, balanceResponse);
        // 8.产品信息构建
        List<FundItemVo> fundItemList = buildFundItemList(balanceResponse);
        // 9.如果产品维度的信息,如果有分期的,按照母基金的产品编码进行分组
        List<FundBalanceVo> fundBalanceVoList = buildFundBalanceVos(fundItemList);
        // 10.产品集合类型再次分组
        List<FundSetVo> fundSetVoList = buildFundSetVos(fundBalanceVoList, balanceResponse);
        siMuIndexVo.setFundSetVoList(fundSetVoList);
        // 11.汇总信息
        QueryFinReceiptResponse queryFinReceiptResponse = null;
        if (CollectionUtils.isNotEmpty(queryFinReceiptResponseList)) {
            queryFinReceiptResponse = queryFinReceiptResponseList.get(0);
        }
        setSumInfo(queryBalanceParamCmd, siMuIndexVo, balanceResponse, fundItemList, queryFinReceiptResponse);
        // 12.为了减少返回的字段,强制去除部分字段返回
        removeField(siMuIndexVo);
        // 13.好臻/香港的,如果有该产品,则固定返回该固定持仓空值,前端用来展示相关入口
        checkAndSetHzHkBalance(siMuIndexVo, balanceResponse);
        return siMuIndexVo;
    }

    /**
     * 好臻/香港的,如果有该产品,则固定返回该固定持仓空值,前端用来展示相关入口
     *
     * @param siMuIndexVo     cgi持仓接口返回
     * @param balanceResponse 中台持仓返回
     */
    private void checkAndSetHzHkBalance(SiMuIndexVo siMuIndexVo, QueryAcctBalanceResponse balanceResponse) {
        // 1.按照类型分类
        Map<String, FundSetVo> fundSetVoMap = siMuIndexVo.getFundSetVoList().stream().collect(Collectors.toMap(FundSetVo::getFundSetType, x -> x));
        // 2.如果用户有好臻产品,但是没有展示,就直接添加一个好臻空值
        if (YesOrNoEnum.YES.getCode().equals(balanceResponse.getHasHZProduct()) && !fundSetVoMap.containsKey(FundSetTypeEnum.HZ.getCode())) {
            FundSetVo fundSetVo = new FundSetVo();
            fundSetVo.setFundSetType(FundSetTypeEnum.HZ.getCode());
            fundSetVo.setSort(FundSetTypeEnum.HZ.getSort());
            fundSetVo.setBalanceNum(1);
            fundSetVo.setTotalAsset(BigDecimal.ZERO);
            siMuIndexVo.getFundSetVoList().add(fundSetVo);
            int balanceNum = siMuIndexVo.getBalanceNum();
            siMuIndexVo.setBalanceNum(balanceNum + 1);
        }
        // 3.香港不隔离,如果用户有香港产品,但是没有展示,就直接添加一个香港空值
        if (!YesOrNoEnum.YES.getCode().equals(siMuIndexVo.getIsHkDataQuarantine())) {
            if (YesOrNoEnum.YES.getCode().equals(balanceResponse.getHasHKProduct()) && !fundSetVoMap.containsKey(FundSetTypeEnum.HK.getCode())) {
                FundSetVo fundSetVo = new FundSetVo();
                fundSetVo.setFundSetType(FundSetTypeEnum.HK.getCode());
                fundSetVo.setSort(FundSetTypeEnum.HK.getSort());
                fundSetVo.setBalanceNum(1);
                fundSetVo.setTotalAsset(BigDecimal.ZERO);
                siMuIndexVo.getFundSetVoList().add(fundSetVo);
                int balanceNum = siMuIndexVo.getBalanceNum();
                siMuIndexVo.setBalanceNum(balanceNum + 1);
            }else{
                // 如果香港产品不隔离,但是仅有现金余额,需要添加一个持仓数量0,但是有资产为现金余额的标签
                BigDecimal cashCollection = balanceResponse.getTotalExchangeCashBalance() == null ? BigDecimal.ZERO : balanceResponse.getTotalExchangeCashBalance();
                if (BigDecimal.ZERO.compareTo(cashCollection) < 0 && !fundSetVoMap.containsKey(FundSetTypeEnum.HK.getCode())) {
                    FundSetVo fundSetVo = new FundSetVo();
                    fundSetVo.setFundSetType(FundSetTypeEnum.HK.getCode());
                    fundSetVo.setSort(FundSetTypeEnum.HK.getSort());
                    fundSetVo.setBalanceNum(0);
                    fundSetVo.setTotalAsset(cashCollection);
                    siMuIndexVo.getFundSetVoList().add(fundSetVo);
                }
            }

        }
    }


    /**
     * 异步设置理财分析,收益入口
     */
    private SetShouYiLiCaiTask asyncSetShouYiLiCai(QueryBalanceParamCmd queryBalanceParamCmd, SiMuIndexVo siMuIndexVo) {
        SetShouYiLiCaiTask setShouYiLiCaiTask = new SetShouYiLiCaiTask();
        setShouYiLiCaiTask.setQueryAcctOtherInfoService(queryAcctOtherInfoService);
        setShouYiLiCaiTask.setHbOneNo(queryBalanceParamCmd.getHboneNo());
        setShouYiLiCaiTask.setSiMuIndexVo(siMuIndexVo);
        return setShouYiLiCaiTask;
    }

    /**
     * 去除部分返回字段
     */
    private void removeField(SiMuIndexVo siMuIndexVo) {
        if (CollectionUtils.isEmpty(siMuIndexVo.getFundSetVoList())) {
            return;
        }
        // 遍历去除字段
        siMuIndexVo.getFundSetVoList().forEach(fundSetVo -> {
            // 11.1.如果是好臻+香港的,去除产品维度信息
            if (FundSetTypeEnum.HZ.getCode().equals(fundSetVo.getFundSetType()) || FundSetTypeEnum.HK.getCode().equals(fundSetVo.getFundSetType())) {
                fundSetVo.setFundBalanceVoList(null);
            } else {
                // 11.2.如果是非分期的,子产品不要吐出
                List<FundBalanceVo> fundBalanceVoList = fundSetVo.getFundBalanceVoList();
                if (CollectionUtils.isNotEmpty(fundBalanceVoList)) {
                    fundBalanceVoList.forEach(fundBalanceVo -> {
                        if (fundBalanceVo.getFundItemVoList().size() < 2) {
                            fundBalanceVo.setFundItemVoList(null);
                        }
                    });
                }

            }
        });

    }

    /**
     * 汇总信息
     */
    private void setSumInfo(QueryBalanceParamCmd queryBalanceParamCmd, SiMuIndexVo siMuIndexVo, QueryAcctBalanceResponse balanceResponse,
                            List<FundItemVo> fundItemList, QueryFinReceiptResponse onWayOrderInfo) {
        // 1.总资产
        siMuIndexVo.setTotalAsset(balanceResponse.getTotalMarketValue() == null ? BigDecimal.ZERO : balanceResponse.getTotalMarketValue());
        // 2.是否存在异常,持仓中只要有一笔存在异常，总资产就需显示计算中标识;1:是,0:否
        siMuIndexVo.setAbnormalState(fundItemList.stream().anyMatch(x -> YesOrNoEnum.YES.getCode().equals(x.getAbnormalState())) ? YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode());
        // 3.总收益计算状态:0-计算中;1-计算成功
        siMuIndexVo.setTotalIncomCalStat(fundItemList.stream().anyMatch(x -> YesOrNoEnum.YES.getCode().equals(x.getIncomeCalStat())) ? YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode());
        // 4.总待确认金额
        siMuIndexVo.setTotalUnconfirmedAmt(balanceResponse.getTotalUnconfirmedAmt() == null ? BigDecimal.ZERO : balanceResponse.getTotalUnconfirmedAmt());
        // 5.是否需要授权
        if ((YesOrNoEnum.YES.getCode().equals(balanceResponse.getHasHKProduct())
                || YesOrNoEnum.YES.getCode().equals(balanceResponse.getHasHZProduct()))
                && YesOrNoEnum.NO.getCode().equals(siMuIndexVo.getIsDataAuth())) {
            siMuIndexVo.setIsNeedAuth(YesOrNoEnum.YES.getCode());
        } else {
            siMuIndexVo.setIsNeedAuth(YesOrNoEnum.NO.getCode());
        }
        // 6.当前总收益
        siMuIndexVo.setTotalCurrentAsset(balanceResponse.getTotalCurrentAsset());
        // 7.持仓条数,1个母基金下有2子基金,算2条
        siMuIndexVo.setBalanceNum(fundItemList.size());
        // 8.在途订单
        if (onWayOrderInfo != null) {
            List<QueryAcctBalanceResponse.DealOrderBean> unpaidList = onWayOrderInfo.getUnpaidList();
            List<QueryAcctBalanceResponse.DealOrderBean> unconfirmedList = onWayOrderInfo.getUnconfirmedList();
            List<OnWayDealVo> onWayDealVoList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(unconfirmedList)) {
                List<OnWayDealVo> unconfirmedDealVoList = unconfirmedList.stream().map(x -> {
                    OnWayDealVo onWayDealVo = new OnWayDealVo();
                    onWayDealVo.setDealNo(x.getDealNo());
                    onWayDealVo.setDealType(x.getDealType());
                    return onWayDealVo;
                }).collect(Collectors.toList());
                onWayDealVoList.addAll(unconfirmedDealVoList);
            }
            if (CollectionUtils.isNotEmpty(unpaidList)) {
                List<OnWayDealVo> unpaidDealVoList = unpaidList.stream().map(x -> {
                    OnWayDealVo onWayDealVo = new OnWayDealVo();
                    onWayDealVo.setDealNo(x.getDealNo());
                    onWayDealVo.setDealType(x.getDealType());
                    return onWayDealVo;
                }).collect(Collectors.toList());
                onWayDealVoList.addAll(unpaidDealVoList);
            }
            siMuIndexVo.setOnWayDealVoList(onWayDealVoList);
            // 9.在途列表文案
            siMuIndexVo.setOnWayMsg(getOnWayMsg(unpaidList, unconfirmedList));
            // 10.资金到账提醒文案
            Integer buyUnRefundedPiece = onWayOrderInfo.getBuyUnrefundedPiece();
            Integer redeemUnRefundedPiece = onWayOrderInfo.getRedeemUnrefundedPiece();
            siMuIndexVo.setFundArrivalMsg(getFundArrivalMsg(buyUnRefundedPiece, redeemUnRefundedPiece));
        }
        // 11.是否不绑定定登录，用于特定用户不支持微信绑定自登陆,1:不绑定自登陆,0:绑定自登陆
        siMuIndexVo.setIsnologinBind(isZcVipBindWhite(queryBalanceParamCmd.getHboneNo()));
        siMuIndexVo.setTotalIncomCalStat(balanceResponse.getTotalIncomCalStat());
        // 12.补签协议是否需要授权
        List<WaitSupSignAgreementFundVo> waitSupSignAgreementFundList = siMuIndexVo.getWaitSupSignAgreementFundList();
        if (CollectionUtils.isNotEmpty(waitSupSignAgreementFundList)) {
            waitSupSignAgreementFundList.forEach(x -> {
                if (siMuIndexVo.getIsDataAuth() != null && siMuIndexVo.getIsDataAuth().equals(YesOrNoEnum.NO.getCode())) {
                    x.setIsNeedAuth(YesOrNoEnum.NO.getCode());
                    if (DisCodeEnum.HZ.getCode().equals(x.getDisCode())) {
                        x.setIsNeedAuth(YesOrNoEnum.YES.getCode());
                        return;
                    }
                    if (YesOrNoEnum.YES.getCode().equals(x.getHkSaleFlag())) {
                        x.setIsNeedAuth(YesOrNoEnum.YES.getCode());
                    }
                } else {
                    x.setIsNeedAuth(YesOrNoEnum.NO.getCode());
                }
            });
            siMuIndexVo.setWaitSupSignAgreementFundList(waitSupSignAgreementFundList);
        }
        // 13.现金余额
        siMuIndexVo.setTotalExchangeCashBalance(balanceResponse.getTotalExchangeCashBalance());
        siMuIndexVo.setTotalExchangeAvailableBalance(balanceResponse.getTotalExchangeAvailableBalance());
        siMuIndexVo.setTotalExchangeFrozenAmt(balanceResponse.getTotalUnconfirmedAmt());
    }


    /**
     * 获取资金到账提醒文案
     *
     * @param buyUnRefundedPiece    购买待退款笔数
     * @param redeemUnRefundedPiece 赎回待退款笔数
     *                              (1）若【购买待退款订单数】≠0 & 【赎回待回款订单数】=0：当前总收益下方展示小气泡提示语“ 预计 {【购买待退款订单数】} 笔购买退款即将到帐(仅供参考,以实际到账为准)”。
     *                              （2）若【购买待退款订单数】=0 & 【赎回待回款订单数】≠0：当前总收益下方展示小气泡提示语“ 预计 {【赎回待回款订单数】} 笔赎回资金即将到帐(仅供参考,以实际到账为准)”。
     *                              （3）若【购买待退款订单数】≠0 & 【赎回待回款订单数】≠0：当前总收益下方展示小气泡提示语“ 预计 {【购买待退款订单数+赎回待回款订单数】} 笔资金即将到帐(仅供参考,以实际到账为准)”。
     *                              （4）若【购买待退款订单数】=0 & 【赎回待回款订单数】=0：隐藏气泡提示。
     * @return 资金到账提醒文案
     */
    private String getFundArrivalMsg(Integer buyUnRefundedPiece, Integer redeemUnRefundedPiece) {
        // 1.若【购买待退款订单数】≠0 & 【赎回待回款订单数】=0：当前总收益下方展示小气泡提示语“ 预计 {【购买待退款订单数】} 笔购买退款即将到帐(仅供参考,以实际到账为准)”。
        if (buyUnRefundedPiece != null && buyUnRefundedPiece > 0 && (redeemUnRefundedPiece == null || redeemUnRefundedPiece == 0)) {
            return "预计" + buyUnRefundedPiece + "笔购买退款即将到帐";
        }
        // 2.若【购买待退款订单数】=0 & 【赎回待回款订单数】≠0：当前总收益下方展示小气泡提示语“ 预计 {【赎回待回款订单数】} 笔赎回资金即将到帐(仅供参考,以实际到账为准)”。
        if ((buyUnRefundedPiece == null || buyUnRefundedPiece == 0) && redeemUnRefundedPiece != null && redeemUnRefundedPiece > 0) {
            return "预计" + redeemUnRefundedPiece + "笔赎回资金即将到帐";
        }
        // 3.若【购买待退款订单数】≠0 & 【赎回待回款订单数】≠0：当前总收益下方展示小气泡提示语“ 预计 {【购买待退款订单数+赎回待回款订单数】} 笔资金即将到帐(仅供参考,以实际到账为准)”。
        if (buyUnRefundedPiece != null && buyUnRefundedPiece > 0 && redeemUnRefundedPiece > 0) {
            return "预计" + (buyUnRefundedPiece + redeemUnRefundedPiece) + "笔资金即将到帐";
        }
        // 4.若【购买待退款订单数】=0 & 【赎回待回款订单数】=0：隐藏气泡提示。
        return null;
    }


    /**
     * 获取在途文案
     * （1）若 待付款订单数＞0 且 待确认订单数=0：下方展示提示语“当前有{待付款订单数}笔交易待付款”；
     * （2）若 待付款订单数=0 且 待确认订单数＞0：下方展示提示语“当前有{待确认订单数}笔交易确认中”；
     * （3）若 待付款订单数＞0 且 待确认订单数＞0：下方展示提示语“当前有{待付款订单数+待确认订单数}笔交易进行中”；
     * （4）若均为0，隐藏提示语；
     *
     * @param unpaidList      待付款订单
     * @param unconfirmedList 待确认订单
     * @return 文案
     */
    private String getOnWayMsg(List<QueryAcctBalanceResponse.DealOrderBean> unpaidList, List<QueryAcctBalanceResponse.DealOrderBean> unconfirmedList) {
        // 1.若 待付款订单数＞0 且 待确认订单数=0：下方展示提示语“当前有{待付款订单数}笔交易待付款”
        if (CollectionUtils.isNotEmpty(unpaidList) && CollectionUtils.isEmpty(unconfirmedList)) {
            return "当前有" + unpaidList.size() + "笔交易待付款";
        }
        // 2.若 待付款订单数=0 且 待确认订单数＞0：下方展示提示语“当前有{待确认订单数}笔交易确认中”；
        if (CollectionUtils.isEmpty(unpaidList) && CollectionUtils.isNotEmpty(unconfirmedList)) {
            return "当前有" + unconfirmedList.size() + "笔交易确认中";
        }
        // 3.若 待付款订单数＞0 且 待确认订单数＞0：下方展示提示语“当前有{待付款订单数+待确认订单数}笔交易进行中”；
        if (CollectionUtils.isNotEmpty(unpaidList) && CollectionUtils.isNotEmpty(unconfirmedList)) {
            return "当前有" + (unpaidList.size() + unconfirmedList.size()) + "笔交易进行中";
        }
        return null;
    }

    /**
     * 异步查询在途信息
     */
    public QueryHighFundInvPlanFlagTask asyncQueryHighFundInvPlanFlag(QueryBalanceParamCmd queryBalanceParamCmd, SiMuIndexVo siMuIndexVo) {
        QueryHighFundInvPlanFlagTask queryHighFundInvPlanFlagTask = new QueryHighFundInvPlanFlagTask();
        queryHighFundInvPlanFlagTask.setQueryHighFundInvPlanListFacade(queryHighFundInvPlanListFacade);
        queryHighFundInvPlanFlagTask.setSiMuIndexVo(siMuIndexVo);
        queryHighFundInvPlanFlagTask.setTxAcctNo(queryBalanceParamCmd.getTxAcctNo());
        queryHighFundInvPlanFlagTask.setProductCode(queryBalanceParamCmd.getProductCode());
        queryHighFundInvPlanFlagTask.setIp(queryBalanceParamCmd.getIp());
        return queryHighFundInvPlanFlagTask;
    }

    /**
     * 异步查询在途信息
     */
    public QueryFinReceiptTask asyncQueryFinReceipt(QueryBalanceParamCmd queryBalanceParamCmd, List<QueryFinReceiptResponse> queryFinReceiptResponseList) {
        QueryFinReceiptTask queryFinReceiptTask = new QueryFinReceiptTask();
        queryFinReceiptTask.setQueryBalanceVolService(this);
        queryFinReceiptTask.setQueryBalanceParamCmd(queryBalanceParamCmd);
        queryFinReceiptTask.setQueryHbDisCodeListFacade(queryHbDisCodeListFacade);
        queryFinReceiptTask.setQueryFinReceiptResponseList(queryFinReceiptResponseList);
        return queryFinReceiptTask;
    }

    /**
     * 查询在途
     *
     * @param queryFinReceiptParamCmd 入参
     * @return 在途信息
     */
    public QueryFinReceiptResponse queryFinReceipt(QueryFinReceiptParamCmd queryFinReceiptParamCmd) {
        // 1.查询非香港在途
        QueryFinReceiptResponse receiptResponse = queryUnHkFinReceipt(queryFinReceiptParamCmd);
        // 2.查询香港在途
        com.howbuy.dtms.order.client.domain.response.finreceipt.QueryFinReceiptResponse hkQueryFinReceiptResponse = getHkQueryFinReceiptResponse(queryFinReceiptParamCmd);
        // 3.构建返回结果
        return buildFinReceiptResponse(receiptResponse, hkQueryFinReceiptResponse);
    }

    /**
     * 合并在途返回信息
     */
    private QueryFinReceiptResponse buildFinReceiptResponse(QueryFinReceiptResponse unHkReceiptResponse, com.howbuy.dtms.order.client.domain.response.finreceipt.QueryFinReceiptResponse hkQueryFinReceiptResponse) {
        QueryFinReceiptResponse queryFinReceiptResponse = new QueryFinReceiptResponse();
        queryFinReceiptResponse.setReturnCode(unHkReceiptResponse.getReturnCode());
        // 购买待退款订单数
        int buyUnRefundedPiece = unHkReceiptResponse.getBuyUnrefundedPiece() == null ? 0 : unHkReceiptResponse.getBuyUnrefundedPiece();
        int hkUnRefundedPiece = hkQueryFinReceiptResponse.getBuyUnrefundedPiece() == null ? 0 : hkQueryFinReceiptResponse.getBuyUnrefundedPiece();
        queryFinReceiptResponse.setBuyUnrefundedPiece(buyUnRefundedPiece + hkUnRefundedPiece);
        // 赎回待回款订单数
        int unHkRedeemUnRefundedPiece = unHkReceiptResponse.getRedeemUnrefundedPiece() == null ? 0 : unHkReceiptResponse.getRedeemUnrefundedPiece();
        int hkRedeemUnRefundedPiece = hkQueryFinReceiptResponse.getRedeemUnrefundedPiece() == null ? 0 : hkQueryFinReceiptResponse.getRedeemUnrefundedPiece();
        queryFinReceiptResponse.setRedeemUnrefundedPiece(unHkRedeemUnRefundedPiece + hkRedeemUnRefundedPiece);
        // 待付款订单
        List<QueryAcctBalanceResponse.DealOrderBean> dealOrderBeanList = new ArrayList<>();
        List<QueryAcctBalanceResponse.DealOrderBean> unHkUnpaidList = unHkReceiptResponse.getUnpaidList();
        if (CollectionUtils.isNotEmpty(unHkUnpaidList)) {
            dealOrderBeanList.addAll(unHkUnpaidList);
        }
        List<String> hkUnpaidList = hkQueryFinReceiptResponse.getUnpaidList();
        if (CollectionUtils.isNotEmpty(hkUnpaidList)) {
            for (String dealNo : hkUnpaidList) {
                QueryAcctBalanceResponse.DealOrderBean dealOrderBean = new QueryAcctBalanceResponse.DealOrderBean();
                dealOrderBean.setDealNo(dealNo);
                dealOrderBean.setDealType("0");
                dealOrderBeanList.add(dealOrderBean);
            }
        }
        queryFinReceiptResponse.setUnpaidList(dealOrderBeanList);
        // 待确认订单
        List<QueryAcctBalanceResponse.DealOrderBean> unConfirmedList = new ArrayList<>();
        List<QueryAcctBalanceResponse.DealOrderBean> unHkUnConfirmedList = unHkReceiptResponse.getUnconfirmedList();
        if (CollectionUtils.isNotEmpty(unHkUnConfirmedList)) {
            unConfirmedList.addAll(unHkUnConfirmedList);
        }
        List<String> hkUnConfirmedList = hkQueryFinReceiptResponse.getUnconfirmedList();
        if (CollectionUtils.isNotEmpty(hkUnConfirmedList)) {
            for (String dealNo : hkUnConfirmedList) {
                QueryAcctBalanceResponse.DealOrderBean dealOrderBean = new QueryAcctBalanceResponse.DealOrderBean();
                dealOrderBean.setDealNo(dealNo);
                dealOrderBean.setDealType("0");
                unConfirmedList.add(dealOrderBean);
            }
        }
        queryFinReceiptResponse.setUnconfirmedList(unConfirmedList);
        return queryFinReceiptResponse;
    }

    /**
     * 香港在途信息
     */
    private com.howbuy.dtms.order.client.domain.response.finreceipt.QueryFinReceiptResponse getHkQueryFinReceiptResponse(QueryFinReceiptParamCmd queryFinReceiptParamCmd) {
        if (StringUtils.isBlank(queryFinReceiptParamCmd.getHbOneNo())) {
            log.info("没有一账通号,不需要查询香港在途信息");
            return new com.howbuy.dtms.order.client.domain.response.finreceipt.QueryFinReceiptResponse();
        }
        if (YesOrNoEnum.NO.getCode().equals(queryFinReceiptParamCmd.getNotFilterHzFund())) {
            log.info("没有香港数据授权,不查询香港在途信息");
            return new com.howbuy.dtms.order.client.domain.response.finreceipt.QueryFinReceiptResponse();
        }

        QueryFinReciptRequest queryFinReciptRequest = new QueryFinReciptRequest();
        queryFinReciptRequest.setHbOneNo(queryFinReceiptParamCmd.getHbOneNo());
        log.info("查询海外在途-start,queryFinReciptRequest={}", JSON.toJSONString(queryFinReciptRequest));
        Response<com.howbuy.dtms.order.client.domain.response.finreceipt.QueryFinReceiptResponse> hkResponse = hkQueryFinreceiptFacade.execute(queryFinReciptRequest);
        log.info("查询海外在途-结果,hkResponse={}", JSON.toJSONString(hkResponse));
        if (!CGISimuConstants.HK_SUCCESS_CODE.equals(hkResponse.getCode()) || hkResponse.getData() == null) {
            return new com.howbuy.dtms.order.client.domain.response.finreceipt.QueryFinReceiptResponse();
        }
        return hkResponse.getData();
    }

    public QueryFinReceiptResponse queryUnHkFinReceipt(QueryFinReceiptParamCmd queryFinReceiptParamCmd) {
        log.info("查询非海外在途-start,queryFinReceiptParamCmd={}", JSON.toJSONString(queryFinReceiptParamCmd));
        if (queryFinReceiptParamCmd.getHkSaleFlag() != null && YesOrNoEnum.YES.getCode().equals(queryFinReceiptParamCmd.getHkSaleFlag())) {
            log.info("仅查询香港在途,不需要查询非香港在途接口");
            QueryFinReceiptResponse queryFinReceiptResponse = new QueryFinReceiptResponse();
            queryFinReceiptResponse.setReturnCode(ExceptionCodes.SUCCESS);
            return queryFinReceiptResponse;
        }
        QueryFinReceiptRequest request = new QueryFinReceiptRequest();
        // 查询好买分销列表
        request.setDisCodeList(getDisCodeList());
        request.setOutletCode(RemoteParametersProvider.getOutletCode());
        request.setOperIp(queryFinReceiptParamCmd.getIp());
        request.setTxChannel(RemoteParametersProvider.getTradeChannel());
        request.setDataTrack(UUID.randomUUID().toString());
        request.setTxAcctNo(queryFinReceiptParamCmd.getTxAcctNo());
        request.setNotFilterHkFund(YesOrNoEnum.NO.getCode());
        request.setNotFilterHzFund(queryFinReceiptParamCmd.getNotFilterHkFund());
        request.setHbOneNo(queryFinReceiptParamCmd.getHbOneNo());
        log.info("查询非海外在途-start,request={}", JSON.toJSONString(request));
        QueryFinReceiptResponse response = queryFinReceiptFacade.execute(request);
        log.info("查询非海外在途-结果,response={}", JSON.toJSONString(response));
        return response;
    }


    /**
     * 按照产品分类分组
     *
     * @param fundBalanceVoList
     * @return
     */
    private static List<FundSetVo> buildFundSetVos(List<FundBalanceVo> fundBalanceVoList, QueryAcctBalanceResponse balanceResponse) {
        Map<String, List<FundBalanceVo>> fundBalanceVoMap = fundBalanceVoList.stream().collect(Collectors.groupingBy(FundBalanceVo::getFundSetType));
        return fundBalanceVoMap.values().stream().map(subList -> {
            String fundSetType = subList.get(0).getFundSetType();
            FundSetVo fundSetVo = new FundSetVo();
            BigDecimal totalAsset = subList.stream().map(FundBalanceVo::getTotalAsset).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            // 香港专区,如果现金余额有值,需要将现金余额加入香港专区的现金汇总
            if (FundSetTypeEnum.HK.getCode().equals(fundSetType)) {
                BigDecimal disPlayHkTotalMarketValue = balanceResponse.getDisPlayHkTotalMarketValue() == null ? BigDecimal.ZERO : balanceResponse.getDisPlayHkTotalMarketValue();
                BigDecimal cashCollection = balanceResponse.getTotalExchangeCashBalance() == null ? BigDecimal.ZERO : balanceResponse.getTotalExchangeCashBalance();
                totalAsset = disPlayHkTotalMarketValue.add(cashCollection);
            }
            fundSetVo.setTotalAsset(totalAsset);
            fundSetVo.setFundSetType(fundSetType);
            fundSetVo.setSort(FundSetTypeEnum.getTypeEnumByCode(fundSetType));
            fundSetVo.setBalanceNum(subList.stream().mapToInt(FundBalanceVo::getBalanceNum).sum());
            fundSetVo.setFundBalanceVoList(subList.stream().sorted(Comparator.comparing(FundBalanceVo::getSort)).collect(Collectors.toList()));
            return fundSetVo;
        }).sorted(Comparator.comparing(FundSetVo::getSort)).collect(Collectors.toList());
    }

    /**
     * 母基金维度信息构建
     *
     * @param fundItemList
     * @return
     */
    private List<FundBalanceVo> buildFundBalanceVos(List<FundItemVo> fundItemList) {
        Map<String, List<FundItemVo>> fundItemVoMap = fundItemList.stream().collect(Collectors.groupingBy(FundItemVo::getFundCode));
        List<FundBalanceVo> fundBalanceVoList = new ArrayList<>();
        fundItemVoMap.forEach((fundCode, subList) -> {
            FundBalanceVo fundBalanceVo = new FundBalanceVo();
            FundItemVo fundItemVo = subList.get(0);
            // 产品代码
            fundBalanceVo.setFundCode(fundCode);
            // 产品名称
            fundBalanceVo.setFundName(fundItemVo.getFundName());
            // 是否分期成立,1-是;0-否
            fundBalanceVo.setStageEstablishFlag(fundItemVo.getStageEstablishFlag());
            // 获取产品集合类型
            fundBalanceVo.setFundSetType(getFundSetType(fundItemVo));
            // 是否显示收益率 0否 1是
            fundBalanceVo.setIsShowYieldRate(checkShowYieldRate(subList, fundBalanceVo.getFundSetType()));
            // 净值分红标识,0-否，1-是
            fundBalanceVo.setNavDivFlag(fundItemVo.getNavDivFlag());
            // 产品类型,7-一对多专户,11-私募
            fundBalanceVo.setProductType(fundItemVo.getProductType());
            // 产品二级类型:0-现金管理,1-债券,2-类固定收益,3-对冲策略,4-股票策略,5-股权,6-房地产基金,7-其他
            fundBalanceVo.setProductSubType(fundItemVo.getProductSubType());
            // 异常标识	0-否; 1-是
            fundBalanceVo.setAbnormalState(subList.stream().filter(Objects::nonNull).anyMatch(x -> YesOrNoEnum.YES.getCode().equals(x.getAbnormalState())) ? YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode());
            // 清算标识    0-否; 1-是
            fundBalanceVo.setCrisisFlag(subList.stream().filter(Objects::nonNull).anyMatch(x -> YesOrNoEnum.YES.getCode().equals(x.getCrisisFlag())) ? YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode());
            // 千禧年待投产品标识	0-否; 1-是
            fundBalanceVo.setQianXiFlag(subList.stream().filter(Objects::nonNull).anyMatch(x -> YesOrNoEnum.YES.getCode().equals(x.getQianXiFlag())) ? YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode());
            // 净值更新时间,YYYYMMDD或者MMDD
            fundBalanceVo.setNavDate(fundItemVo.getNavDate());
            // 净值披露方式,1-净值,2-份额收益
            fundBalanceVo.setNavDisclosureType(fundItemVo.getNavDisclosureType());
            // 收益计算状态 0-计算中;1-计算成功
            fundBalanceVo.setIncomeCalStat(subList.stream().filter(Objects::nonNull).anyMatch(x -> YesOrNoEnum.NO.getCode().equals(x.getIncomeCalStat())) ? YesOrNoEnum.NO.getCode() : YesOrNoEnum.YES.getCode());
            // 币种
            fundBalanceVo.setCurrencyCode(fundItemVo.getCurrencyCode());
            // 币种描述
            fundBalanceVo.setCurrencyDesc(fundItemVo.getCurrencyDesc());
            // 标准固收标识（固收类产品有此标识）,0-非标准固收，1-标准固收，2-现金管理， 3-券商集合理财 4-纯债产品
            fundBalanceVo.setStandardFixedIncomeFlag(fundItemVo.getStandardFixedIncomeFlag());
            // 到期日
            fundBalanceVo.setDueDate(fundItemVo.getDueDate());
            // 年化收益
            List<BigDecimal> yieldIncomeList = subList.stream().map(FundItemVo::getYieldIncome).filter(Objects::nonNull).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(yieldIncomeList)) {
                fundBalanceVo.setYieldIncome(yieldIncomeList.stream().reduce(BigDecimal.ZERO, BigDecimal::add));
            }
            // 固收货币产品净值日期（来自DB）
            fundBalanceVo.setYieldIncomeDt(fundItemVo.getYieldIncomeDt());
            // 产品存续期限(类似于5+3+2这种说明)
            fundBalanceVo.setProductDuration(fundItemVo.getProductDuration());
            // 私募股权回款(当前币种)
            fundBalanceVo.setCurrencyCashCollection(fundItemVo.getCurrencyCashCollection());
            // 产品期限说明
            fundBalanceVo.setCpqxsm(fundItemVo.getCpqxsm());
            // 产品渠道
            fundBalanceVo.setDisCode(fundItemVo.getDisCode());
            // 持仓数量
            fundBalanceVo.setBalanceNum(subList.size());
            // 好买香港代销标识: 0-否; 1-是
            fundBalanceVo.setHkSaleFlag(fundItemVo.getHkSaleFlag());
            // 部分数据需要求和,单独抽成方法
            dataSum(subList, fundBalanceVo);
            // 为了保持跟老接口返回产品顺序一致,加的产品原排序
            fundBalanceVo.setSort(fundItemVo.getSort());
            fundBalanceVo.setFundItemVoList(subList);
            fundBalanceVoList.add(fundBalanceVo);
        });
        return fundBalanceVoList;
    }

    private static void dataSum(List<FundItemVo> subList, FundBalanceVo fundBalanceVo) {
        // 当前收益（人民币）
        List<BigDecimal> currentAssetList = subList.stream().map(FundItemVo::getCurrentAsset).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(currentAssetList)) {
            fundBalanceVo.setCurrentAsset(currentAssetList.stream().reduce(BigDecimal.ZERO, BigDecimal::add));
        }
        // 当前收益（当前币种）
        List<BigDecimal> currentAssetCurrencyList = subList.stream().map(FundItemVo::getCurrentAssetCurrency).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(currentAssetCurrencyList)) {
            fundBalanceVo.setCurrentAssetCurrency(currentAssetCurrencyList.stream().reduce(BigDecimal.ZERO, BigDecimal::add));
        }
        // 总资产(人民币)
        setTotalAsset(subList, fundBalanceVo);
        // 总份额
        List<BigDecimal> totalBalanceVolList = subList.stream().map(FundItemVo::getTotalBalanceVol).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(totalBalanceVolList)) {
            fundBalanceVo.setTotalBalanceVol(totalBalanceVolList.stream().reduce(BigDecimal.ZERO, BigDecimal::add));
        }
        // 私募股权回款金额
        List<BigDecimal> cashCollectionList = subList.stream().map(FundItemVo::getCashCollection).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(cashCollectionList)) {
            BigDecimal totalCashCollection = cashCollectionList.stream().reduce(BigDecimal.ZERO, BigDecimal::add);
            fundBalanceVo.setCashCollection(totalCashCollection);
            if (totalCashCollection != null) {
                fundBalanceVo.setCashCollectionStr(totalCashCollection.toPlainString());
            }
        }
        // 总市值(当前币种)
        List<BigDecimal> currencyMarketValueList = subList.stream().map(FundItemVo::getCurrencyMarketValue).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(currencyMarketValueList)) {
            fundBalanceVo.setCurrencyMarketValue(currencyMarketValueList.stream().reduce(BigDecimal.ZERO, BigDecimal::add));
        }
        // 总市值(人民币)
        List<BigDecimal> currentMarketValueList = subList.stream().map(FundItemVo::getCurrencyMarketValue).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(currentMarketValueList)) {
            fundBalanceVo.setCurrencyMarketValue(currentMarketValueList.stream().reduce(BigDecimal.ZERO, BigDecimal::add));
        }
        // 当前收益率
        List<BigDecimal> yieldRateList = subList.stream().map(FundItemVo::getYieldRate).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(yieldRateList)) {
            fundBalanceVo.setYieldRate(yieldRateList.stream().reduce(BigDecimal.ZERO, BigDecimal::add));
        }
        // 净值
        List<String> navList = subList.stream().map(FundItemVo::getNav).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(navList)) {
            BigDecimal totalNav = navList.stream().map(BigDecimal::new).reduce(BigDecimal.ZERO, BigDecimal::add);
            fundBalanceVo.setNav(totalNav.toPlainString());
        }
        // 待投金额（人民币）
        List<BigDecimal> unPaidInAmtList = subList.stream().map(FundItemVo::getUnPaidInAmt).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(unPaidInAmtList)) {
            fundBalanceVo.setUnPaidInAmt(unPaidInAmtList.stream().reduce(BigDecimal.ZERO, BigDecimal::add));
        }
        // 总实缴金额
        List<BigDecimal> paidInAmtList = subList.stream().map(FundItemVo::getPaidInAmt).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(paidInAmtList)) {
            fundBalanceVo.setPaidInAmt(paidInAmtList.stream().reduce(BigDecimal.ZERO, BigDecimal::add));
        }
        // 待投金额（当前币种）
        List<BigDecimal> currencyUnPaidInAmtList = subList.stream().map(FundItemVo::getCurrencyUnPaidInAmt).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(currencyUnPaidInAmtList)) {
            fundBalanceVo.setCurrencyUnPaidInAmt(currencyUnPaidInAmtList.stream().reduce(BigDecimal.ZERO, BigDecimal::add));
        }
        // 净购买金额(投资成本)(当前币种)
        List<BigDecimal> currencyNetBuyAmountList = subList.stream().map(FundItemVo::getCurrencyNetBuyAmount).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(currencyNetBuyAmountList)) {
            fundBalanceVo.setCurrencyNetBuyAmount(currencyNetBuyAmountList.stream().reduce(BigDecimal.ZERO, BigDecimal::add));
        }
    }

    /**
     * 设置产品维度总资产
     */
    private static void setTotalAsset(List<FundItemVo> subList, FundBalanceVo fundBalanceVo) {
        fundBalanceVo.setTotalAsset(subList.stream().map(x -> {
            BigDecimal marketValue = x.getMarketValue() == null ? BigDecimal.ZERO : x.getMarketValue();
            BigDecimal unPaidInAmt = x.getUnPaidInAmt() == null ? BigDecimal.ZERO : x.getUnPaidInAmt();
            if (YesOrNoEnum.YES.getCode().equals(x.getHkSaleFlag()) || DisCodeEnum.HZ.getCode().equals(x.getDisCode())) {
                BigDecimal unConfirmedAmt = x.getUnconfirmedAmt() == null ? BigDecimal.ZERO : x.getUnconfirmedAmt();
                return marketValue.add(unPaidInAmt).add(unConfirmedAmt);
            }
            return marketValue.add(unPaidInAmt);
        }).reduce(BigDecimal.ZERO, BigDecimal::add));
    }


    /**
     * 判断是否显示收益率
     *
     * @param subList 母基金下的子基金(如果不是分期成立的,则只有一条母基金)
     * @return 是否显示收益率
     */
    private String checkShowYieldRate(List<FundItemVo> subList, String fundSetType) {
        // 分期的不显示收益率
        if (subList.size() > 1) {
            return YesOrNoEnum.NO.getCode();
        }
        // 当前收益为空,就返回不显示收益率
        FundItemVo fundItemVo = subList.get(0);
        if (fundItemVo.getCurrentAssetCurrency() == null) {
            return YesOrNoEnum.NO.getCode();
        }
        //阳光私募股权,非分期,收益计算完成,显示收益率
        if (FundSetTypeEnum.SUNSHINE.getCode().equals(fundSetType)) {
            return YesOrNoEnum.YES.getCode();
        }
        // 非阳光私募股权,非分期,收益计算完成, 固定收益+券商固收  显示收益率
        if (FundSetTypeEnum.FIXED_INCOME.getCode().equals(fundSetType) && fundItemVo.getStandardFixedIncomeFlag() != null && StandardFixedIncomeFlagEnum.BROKERAGE_COLLECTION_GS.getCode().equals(fundItemVo.getStandardFixedIncomeFlag())) {
            return YesOrNoEnum.YES.getCode();
        }
        return YesOrNoEnum.NO.getCode();
    }

    /**
     * 产品维度信息构建
     */
    private List<FundItemVo> buildFundItemList(QueryAcctBalanceResponse balanceResponse) {
        List<FundItemVo> fundItemList = new ArrayList<>();
        List<UnconfirmeProduct> unConfirmProducts = balanceResponse.getUnconfirmeProducts();
        Map<String, List<UnconfirmeProduct>> unConfirmProductMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(unConfirmProducts)) {
            unConfirmProductMap = unConfirmProducts.stream().collect(Collectors.groupingBy(x -> x.getFundCode() + Constant.UNDER_LINE + x.getDisCode()));
        }
        int sort = 0;
        for (QueryAcctBalanceResponse.BalanceBean bean : balanceResponse.getBalanceList()) {
            FundItemVo fundItem = new FundItemVo();
            // 产品编码
            fundItem.setFundCode(bean.getProductCode());
            // 子产品编码
            fundItem.setFundSubCode(bean.getSubProductCode());
            // 产品名称
            fundItem.setFundName(bean.getProductName());
            // 净值分红标识,0-否，1-是
            fundItem.setNavDivFlag(bean.getNavDivFlag());
            // 产品类型,7-一对多专户,11-私募
            fundItem.setProductType(bean.getProductType());
            // 是否分期成立,1-是;0-否
            fundItem.setStageEstablishFlag(bean.getStageEstablishFlag());
            // 待投金额（人民币）
            fundItem.setUnPaidInAmt(bean.getUnPaidInAmt());
            // 认缴金额
            fundItem.setPaidInAmt(bean.getPaidInAmt());
            // 待投金额（当前币种）
            fundItem.setCurrencyUnPaidInAmt(bean.getCurrencyUnPaidInAmt());
            // 产品二级类型:0-现金管理,1-债券,2-类固定收益,3-对冲策略,4-股票策略,5-股权,6-房地产基金,7-其他
            fundItem.setProductSubType(bean.getProductSubType());
            // 异常标识	0-否; 1-是
            fundItem.setAbnormalState(getAbnormalState(bean));
            // 清算标识   0-否; 1-是
            fundItem.setCrisisFlag(bean.getCrisisFlag());
            // 千禧年持仓功能适配需求 20230216 start
            fundItem.setQianXiFlag(bean.getQianXiFlag());
            // 市值
            fundItem.setMarketValue(bean.getMarketValue());
            // 总市值(当前币种)
            fundItem.setCurrencyMarketValue(bean.getCurrencyMarketValue());
            // 当前收益(人民币）
            fundItem.setCurrentAsset(bean.getCurrentAsset());
            // 当前收益（当前币种）
            fundItem.setCurrentAssetCurrency(bean.getCurrentAssetCurrency());
            // 持仓份额
            fundItem.setTotalBalanceVol(bean.getBalanceVol());
            // 私募股权回款金额
            fundItem.setCashCollection(bean.getCashCollection());
            // 私募股权回款金额String
            if (bean.getCashCollection() != null) {
                fundItem.setCashCollectionStr(bean.getCashCollection().toPlainString());
            }
            // 当前收益率
            fundItem.setYieldRate(bean.getYieldRate());
            // 未确认金额
            List<UnconfirmeProduct> unConfirmProductList = unConfirmProductMap.get(bean.getProductCode() + Constant.UNDER_LINE + bean.getDisCode());
            if (CollectionUtils.isNotEmpty(unConfirmProductList)) {
                BigDecimal unConfirmAmt = unConfirmProductList.stream().map(UnconfirmeProduct::getUnconfirmedAmt).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                fundItem.setUnconfirmedAmt(unConfirmAmt);
            }
            // 净值
            if (bean.getNav() != null) {
                fundItem.setNav(bean.getNav().toPlainString());
            } else {
                fundItem.setNav(null);
            }
            // 好买香港代销标识: 0-否; 1-是
            fundItem.setHkSaleFlag(bean.getHkSaleFlag());
            // 净值更新时间
            if (bean.getNavDt() != null) {
                String yearDay = DateUtils.formatToString(new Date(), DateUtils.YYYY);
                String navDt = DateUtils.formatToString(DateUtils.formatToDate(bean.getNavDt(), DateUtils.YYYYMMDD), DateUtils.YYYY);
                if (navDt != null && navDt.equals(yearDay)) {
                    fundItem.setNavDate(DateUtils.formatToString(DateUtils.formatToDate(bean.getNavDt(), DateUtils.YYYYMMDD), DateUtils.MMdd));
                } else {
                    fundItem.setNavDate(bean.getNavDt());
                }
            }
            // 净值披露方式
            fundItem.setNavDisclosureType(bean.getNavDisclosureType());
            // 收益计算状态 	    0-未计算; 1-已计算
            fundItem.setIncomeCalStat(bean.getIncomeCalStat());
            // 币种编码
            fundItem.setCurrencyCode(bean.getCurrency());
            // 币种名称
            fundItem.setCurrencyDesc(CurrencyEnum.getNameByCode(bean.getCurrency()));
            // 标准固收标识（固收类产品有此标识） 0-非标准固收，1-标准固收，2-现金管理， 3-券商集合理财 4-纯债产品
            fundItem.setStandardFixedIncomeFlag(bean.getStandardFixedIncomeFlag());
            // 分销渠道
            fundItem.setDisCode(bean.getDisCode());
            // 到期日
            fundItem.setDueDate(bean.getDueDate());
            // 固收货币产品七日年化收益（来自DB）
            fundItem.setYieldIncome(bean.getYieldIncome());
            // 固收货币产品净值日期（来自DB）
            if (bean.getYieldIncomeDt() != null) {
                String yearDay = DateUtils.formatToString(new Date(), DateUtils.YYYY);
                String yieldIncomeDt = DateUtils.formatToString(DateUtils.formatToDate(bean.getYieldIncomeDt(), DateUtils.YYYYMMDD), DateUtils.YYYY);
                if (yieldIncomeDt != null && yieldIncomeDt.equals(yearDay)) {
                    fundItem.setYieldIncomeDt(DateUtils.formatToString(DateUtils.formatToDate(bean.getYieldIncomeDt(), DateUtils.YYYYMMDD), DateUtils.MMdd));
                } else {
                    fundItem.setYieldIncomeDt(bean.getYieldIncomeDt());
                }
            }

            // 产品存续期限
            fundItem.setProductDuration(bean.getFundCXQXStr());
            // 私募股权回款(当前币种)
            fundItem.setCurrencyCashCollection(bean.getCurrencyCashCollection());
            // 产品期限说明
            fundItem.setCpqxsm(bean.getCpqxsm());
            // 净购买金额(投资成本)(当前币种)
            fundItem.setCurrencyNetBuyAmount(bean.getCurrencyNetBuyAmount());
            // 产品集合类型
            fundItem.setFundSetType(getFundSetType(fundItem));
            // 是否显示收益率
            fundItem.setIsShowYieldRate(checkShowYieldRate(Collections.singletonList(fundItem), fundItem.getFundSetType()));
            // 为了保持跟老接口返回产品顺序一致,加的产品原排序
            fundItem.setSort(sort++);
            fundItemList.add(fundItem);
        }
        return fundItemList;
    }

    /**
     * 检查臻财VIP绑定白名单（这批用户不支持自动登录，每次都要输入密码）
     *
     * @param hboneNo
     * @return void
     * @author: huaqiang.liu
     * @date: 2021/5/11 10:02
     * @since JDK 1.8
     */
    public String isZcVipBindWhite(String hboneNo) {
        try {
            String zcVipBindWhite = simuCcmsServiceRegister.getZcvipBindWhite();
            if (StringUtils.isBlank(zcVipBindWhite)) {
                return YesOrNoEnum.NO.getCode();
            }
            List<String> whiteList = Arrays.asList(zcVipBindWhite.split(","));
            if (whiteList.contains(hboneNo)) {
                return YesOrNoEnum.YES.getCode();

            }
        } catch (Exception e) {
            log.error("QueryBalanceVolService|checkIsZcVipBindWhite error:{}", e.getMessage(), e);
        }
        return YesOrNoEnum.NO.getCode();
    }

    /**
     * 产品集合类型
     *
     * @param fundItemVo 产品基本信息
     *                   固定收益: hkSaleFlag=0 && productSubType=2
     *                   私募股权（非好臻):hkSaleFlag=0 && productSubType=5 && disCode!= HZ000N001
     *                   阳光私募:hkSaleFlag=0 &&  productSubType!=2/5
     *                   好买香港专区:hkSaleFlag=1
     *                   好臻专区:HZ000N001
     * @return 产品集合类型,@see com.howbuy.cgi.trade.simu.common.enums.FundSetTypeEnum
     */
    private String getFundSetType(FundItemVo fundItemVo) {
        // 好买香港专区
        if (YesOrNoEnum.YES.getCode().equals(fundItemVo.getHkSaleFlag())) {
            return FundSetTypeEnum.HK.getCode();
        }
        // 好臻专区
        if (DisCodeEnum.HZ.getCode().equals(fundItemVo.getDisCode())) {
            return FundSetTypeEnum.HZ.getCode();
        }
        // 固定收益
        if (YesOrNoEnum.NO.getCode().equals(fundItemVo.getHkSaleFlag()) && ProductDBTypeEnum.GUDINGSHOUYI.getCode().equals(fundItemVo.getProductSubType())) {
            return FundSetTypeEnum.FIXED_INCOME.getCode();
        }
        // 私募股权
        if (YesOrNoEnum.NO.getCode().equals(fundItemVo.getHkSaleFlag()) && ProductDBTypeEnum.GUQUAN.getCode().equals(fundItemVo.getProductSubType()) && !DisCodeEnum.HZ.getCode().equals(fundItemVo.getDisCode())) {
            return FundSetTypeEnum.PRIVATE_EQUITY.getCode();
        }
        // 阳光私募
        return FundSetTypeEnum.SUNSHINE.getCode();
    }

    /**
     * 获取异常标识
     */
    public String getAbnormalState(QueryAcctBalanceResponse.BalanceBean bean) {
        // 1.先统计下各种异常标识
        // 1.1.持仓返回的异常标识
        boolean abnormalFlag = StringUtils.isNotBlank(bean.getAbnormalFlag()) && YesOrNoEnum.YES.getCode().equals(bean.getAbnormalFlag());
        // 1.2.投资成本异常
        boolean netBuyAmountAbnormal = (bean.getCurrencyNetBuyAmount() == null || BigDecimal.ZERO.compareTo(bean.getCurrencyNetBuyAmount()) > 0);
        // 1.3.参考市值异常
        boolean currencyMarketValueAbnormal = (bean.getCurrencyMarketValue() == null && ZERO.equals(bean.getCurrencyMarketValueCtl())) || BigDecimal.ZERO.compareTo(bean.getCurrencyMarketValue()) > 0;
        // 1.4.持仓份额异常
        boolean totalBalaAbnormal = bean.getBalanceVol() == null || BigDecimal.ZERO.compareTo(bean.getBalanceVol()) >= 0;
        // 1.5.参考净值异常
        boolean navAbnormal = (bean.getNav() == null && ZERO.equals(bean.getNavCtl())) || BigDecimal.ZERO.compareTo(bean.getNav()) > 0;
        // 2.获取汇总异常标识
        return getTotalAbnormalState(bean, abnormalFlag, netBuyAmountAbnormal, currencyMarketValueAbnormal, totalBalaAbnormal, navAbnormal);
    }

    /**
     * 获取汇总异常标识
     */
    private static String getTotalAbnormalState(QueryAcctBalanceResponse.BalanceBean bean, boolean abnormalFlag,
                                                boolean netBuyAmountAbnormal, boolean currencyMarketValueAbnormal,
                                                boolean totalBalaAbnormal, boolean navAbnormal) {
        if (ProductDBTypeEnum.GUDINGSHOUYI.getCode().equals(bean.getProductSubType())) {
            return guShouAbnormalState(bean, abnormalFlag, currencyMarketValueAbnormal, totalBalaAbnormal, navAbnormal);
        } else if (ProductDBTypeEnum.GUQUAN.getCode().equals(bean.getProductSubType())) {
            // 2.股权
            // 当前投资成本/持仓份额 任一字段＜0 或为 空值
            if (netBuyAmountAbnormal || totalBalaAbnormal) {
                return YesOrNoEnum.YES.getCode();
            } else {
                return YesOrNoEnum.NO.getCode();
            }
        } else {
            // 3.阳光私募
            // 异常+参考市值/持仓份额/参考净值 任一字段＜0 或为 空值
            if (abnormalFlag || currencyMarketValueAbnormal || totalBalaAbnormal || navAbnormal) {
                return YesOrNoEnum.YES.getCode();
            } else {
                return YesOrNoEnum.NO.getCode();
            }
        }
    }

    /**
     * 固收异常标识
     */
    private static String guShouAbnormalState(QueryAcctBalanceResponse.BalanceBean bean, boolean abnormalFlag, boolean currencyMarketValueAbnormal, boolean totalBalaAbnormal, boolean navAbnormal) {
        // 1.固收
        // 1.1.对于 非货基型券商集合 产品，参考市值/持仓份额/参考净值 任一字段＜0 或为 空值；
        if (StandardFixedIncomeFlagEnum.BROKERAGE_COLLECTION_GS.getCode().equals(bean.getStandardFixedIncomeFlag()) && NavDisclosureTypeEnum.JZ.getCode().equals(bean.getNavDisclosureType())) {
            if (abnormalFlag || currencyMarketValueAbnormal || totalBalaAbnormal || navAbnormal) {
                return YesOrNoEnum.YES.getCode();
            } else {
                return YesOrNoEnum.NO.getCode();
            }
        } else {
            // 1.2.对于 正常固收/股权固收/现金管理/货基型券商集合/纯债产品 产品，参考市值/持仓份额 任一字段＜0 或为 空值;
            if (currencyMarketValueAbnormal || totalBalaAbnormal) {
                return YesOrNoEnum.YES.getCode();
            } else {
                return YesOrNoEnum.NO.getCode();
            }
        }
    }


    private static void setNoTxAcctFlag(QueryBalanceParamCmd queryBalanceParamCmd, SiMuIndexVo
            siMuIndexVo, QueryAcctBalanceResponse balanceResponse) {
        if (StringUtils.isBlank(queryBalanceParamCmd.getSubAccountId()) && StringUtils.isBlank(queryBalanceParamCmd.getTxAcctNo())) {
            // 未开户且无持仓的用户需开户才能访问
            // 设置未开户标识，有持仓时前端判断禁用按钮
            siMuIndexVo.setNoTxAcctNo(YesOrNoEnum.YES.getCode());
            if (balanceResponse == null || balanceResponse.getBalanceList() == null || balanceResponse.getBalanceList().isEmpty()) {
                throw new BizException(BizErrorEnum.REQUEST_URI_BANNED.getCode(), "您还未开户，开户后方可访问此页面！");
            }
        } else {
            siMuIndexVo.setNoTxAcctNo(YesOrNoEnum.NO.getCode());
        }
    }

    /**
     * 异步设置账户相关的信息
     */
    private SetSiMuAccountInfoTask asyncSetAccountInfo(QueryBalanceParamCmd queryBalanceParamCmd, SiMuIndexVo siMuIndexVo) {
        SetSiMuAccountInfoTask setSiMuAccountInfoTask = new SetSiMuAccountInfoTask();
        setSiMuAccountInfoTask.setRelatedAccountService(relatedAccountService);
        setSiMuAccountInfoTask.setKycService(kycService);
        setSiMuAccountInfoTask.setHboneService(hboneService);
        setSiMuAccountInfoTask.setQuerySupSignAgreementListFacade(querySupSignAgreementListFacade);
        setSiMuAccountInfoTask.setQueryBalanceParamCmd(queryBalanceParamCmd);
        setSiMuAccountInfoTask.setInviteService(inviteService);
        setSiMuAccountInfoTask.setSiMuIndexVo(siMuIndexVo);
        return setSiMuAccountInfoTask;
    }

    /**
     * 异步查询持仓信息
     */
    public QueryAcctBalanceTask asyncQueryAcctBalance(QueryBalanceParamCmd queryBalanceParamCmd, List<QueryAcctBalanceResponse> responseList) {
        QueryAcctBalanceTask queryAcctBalanceTask = new QueryAcctBalanceTask();
        queryAcctBalanceTask.setQueryHbDisCodeListFacade(queryHbDisCodeListFacade);
        queryAcctBalanceTask.setQueryBalanceVolService(this);
        queryAcctBalanceTask.setQueryBalanceParamCmd(queryBalanceParamCmd);
        queryAcctBalanceTask.setResponseList(responseList);
        return queryAcctBalanceTask;
    }

    /**
     * 查询旧高端用户持仓信息
     */
    public QueryAcctBalanceResponse queryOldAcctBalance(QueryBalanceParamCmd queryBalanceParamCmd) {
        QueryAcctBalanceRequest queryAcctBalanceRequest = new QueryAcctBalanceRequest();
        log.info("QueryBalanceVolService-queryAcctBalance-查询持仓接口,queryBalanceParamCmd={}", JSON.toJSONString(queryBalanceParamCmd));
        // 分销渠道
        if (StringUtils.isNotBlank(queryBalanceParamCmd.getDisCode())) {
            queryAcctBalanceRequest.setDisCode(queryBalanceParamCmd.getDisCode());
            queryAcctBalanceRequest.setDisCodeList(Collections.singletonList(queryBalanceParamCmd.getDisCode()));
        } else {
            List<String> disCodeList = getDisCodeList();
            queryAcctBalanceRequest.setDisCodeList(disCodeList);
            queryAcctBalanceRequest.setDisCode(DisCodeEnum.HM.getCode());
        }
        queryAcctBalanceRequest.setOutletCode(RemoteParametersProvider.getOutletCode());
        queryAcctBalanceRequest.setOperIp(queryBalanceParamCmd.getIp());
        queryAcctBalanceRequest.setTxChannel(RemoteParametersProvider.getTradeChannel());
        queryAcctBalanceRequest.setDataTrack(UUID.randomUUID().toString());
        queryAcctBalanceRequest.setTxAcctNo(queryBalanceParamCmd.getTxAcctNo());
        queryAcctBalanceRequest.setHbOneNo(queryBalanceParamCmd.getHboneNo());
        queryAcctBalanceRequest.setProductCode(queryBalanceParamCmd.getProductCode());
        // 是否香港代销
        queryAcctBalanceRequest.setHkSaleFlag(queryBalanceParamCmd.getHkSaleFlag());
        queryAcctBalanceRequest.setProductType(queryBalanceParamCmd.getProductType());
        queryAcctBalanceRequest.setProductSubType(queryBalanceParamCmd.getProductSubType());
        queryAcctBalanceRequest.setNotFilterHzFund(queryBalanceParamCmd.getNotFilterHzFund());
        queryAcctBalanceRequest.setNotFilterHkFund(queryBalanceParamCmd.getNotFilterHkFund());
        log.info("查询非海外持仓接口-start,queryAcctBalanceRequest={}", JSON.toJSONString(queryAcctBalanceRequest));
        QueryAcctBalanceResponse response = queryAcctBalanceFacade.execute(queryAcctBalanceRequest);
        log.info("查询非海外持仓接口-结果,response={}", JSON.toJSONString(response));
        return response;
    }

    /**
     * 查询高端用户持仓信息
     */
    public QueryAcctBalanceResponse queryNewAcctBalance(QueryBalanceParamCmd queryBalanceParamCmd) {
        log.info("QueryBalanceVolService-queryNewAcctBalance-查询持仓接口,queryBalanceParamCmd={}", JSON.toJSONString(queryBalanceParamCmd));
        QueryAcctBalanceRequest queryAcctBalanceRequest = new QueryAcctBalanceRequest();
        // 分销渠道
        if (StringUtils.isNotBlank(queryBalanceParamCmd.getDisCode())) {
            queryAcctBalanceRequest.setDisCode(queryBalanceParamCmd.getDisCode());
            queryAcctBalanceRequest.setDisCodeList(Collections.singletonList(queryBalanceParamCmd.getDisCode()));
        } else {
            List<String> disCodeList = getDisCodeList();
            queryAcctBalanceRequest.setDisCodeList(disCodeList);
            queryAcctBalanceRequest.setDisCode(DisCodeEnum.HM.getCode());
        }
        queryAcctBalanceRequest.setOutletCode(RemoteParametersProvider.getOutletCode());
        queryAcctBalanceRequest.setOperIp(queryBalanceParamCmd.getIp());
        queryAcctBalanceRequest.setTxChannel(RemoteParametersProvider.getTradeChannel());
        TraceParam traceParam = RequestChainTrace.get();
        if (traceParam != null) {
            queryAcctBalanceRequest.setDataTrack(traceParam.getTraceId());
        } else {
            queryAcctBalanceRequest.setDataTrack(UUID.randomUUID().toString());
        }
        queryAcctBalanceRequest.setTxAcctNo(queryBalanceParamCmd.getTxAcctNo());
        queryAcctBalanceRequest.setHbOneNo(queryBalanceParamCmd.getHboneNo());
        queryAcctBalanceRequest.setProductCode(queryBalanceParamCmd.getProductCode());
        // 是否香港代销
        queryAcctBalanceRequest.setHkSaleFlag(queryBalanceParamCmd.getHkSaleFlag());
        queryAcctBalanceRequest.setProductType(queryBalanceParamCmd.getProductType());
        queryAcctBalanceRequest.setProductSubType(queryBalanceParamCmd.getProductSubType());
        queryAcctBalanceRequest.setNotFilterHzFund(queryBalanceParamCmd.getNotFilterHzFund());
        queryAcctBalanceRequest.setNotFilterHkFund(queryBalanceParamCmd.getNotFilterHkFund());
        log.info("查询新持仓接口-start,queryAcctBalanceRequest={}", JSON.toJSONString(queryAcctBalanceRequest));
        QueryAcctBalanceResponse response = queryAcctBalanceNewFacade.execute(queryAcctBalanceRequest);
        log.info("查询新持仓接口-结果,response={}", JSON.toJSONString(response));
        return response;
    }


    /**
     * 获取所有分销代码
     */
    public List<String> getDisCodeList() {
        QueryHbDisCodeListRequest queryHbDisCodeListRequest = new QueryHbDisCodeListRequest();
        Result<List<DisDto>> listResult = queryHbDisCodeListFacade.execute(queryHbDisCodeListRequest);
        List<DisDto> dtoList = listResult.getData();
        List<String> disCodeList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(dtoList)) {
            for (DisDto disDto : dtoList) {
                if (YesOrNoEnum.YES.getCode().equals(disDto.getHowbuyFlag())) {
                    disCodeList.add(disDto.getDisCode());
                }
            }
        } else {
            disCodeList.add(DisCodeEnum.HM.getCode());
        }
        return disCodeList;
    }

    /**
     * 查询数据授权校验需要的信息
     *
     * @param hbOneNo 一账通
     * @param request 入参
     * @return 数据授权校验需要的信息
     */
    public AcctDataAuthCheckDto acctDataAuthCheckDto(String hbOneNo, HttpServletRequest request) {
        // 1.查询用户持仓信息
        MemberCustInfoModel custInfo = hboneService.queryCustInfoByHboneNo(hbOneNo);
        if (custInfo == null) {
            log.error("IndexSimuController-查询关联账户客户信息失败,hBoneNo={}", hbOneNo);
            throw new BizException(BizErrorEnum.SYSTEM_ERROR.getCode(), BizErrorEnum.SYSTEM_ERROR.getDesc());
        }
        QueryBalanceParamCmd queryBalanceParamCmd = new QueryBalanceParamCmd();
        queryBalanceParamCmd.setHboneNo(hbOneNo);
        queryBalanceParamCmd.setTxAcctNo(custInfo.getCustNo());
        queryBalanceParamCmd.setIp(WebUtil.getCustIP(request));
        queryBalanceParamCmd.setNotFilterHkFund(YesOrNoEnum.YES.getCode());
        queryBalanceParamCmd.setNotFilterHzFund(YesOrNoEnum.YES.getCode());
        QueryAcctBalanceResponse queryAcctBalanceResponse = queryNewAcctBalance(queryBalanceParamCmd);
        // 2.查询用户权限
        AcctDataAuthDto acctDataAuthDto = accCenterService.getAcctDataAuthInfo(hbOneNo);
        // 3.构建校验信息实体
        AcctDataAuthCheckDto authCheckDto = new AcctDataAuthCheckDto();
        authCheckDto.setIsAuth(acctDataAuthDto.getIsDataAuth());
        authCheckDto.setIsHkDataQuarantine(acctDataAuthDto.getIsHkDataQuarantine());
        // 3.1.是否有na产品
        String hasNaProduct = YesOrNoEnum.NO.getCode();
        // 3.2.是否有香港产品
        String hasHKProduct = YesOrNoEnum.NO.getCode();
        // 3.3.是否有好臻产品
        String hasHzProduct = YesOrNoEnum.NO.getCode();
        // 3.4.是否有现金余额
        String hasCashBalance = YesOrNoEnum.NO.getCode();
        if (queryAcctBalanceResponse != null) {
            BigDecimal cashBalance = queryAcctBalanceResponse.getTotalExchangeCashBalance() != null ? queryAcctBalanceResponse.getTotalExchangeCashBalance() : BigDecimal.ZERO;
            hasCashBalance = BigDecimal.ZERO.compareTo(cashBalance) < 0 ? YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode();
            hasHKProduct = StringUtils.isBlank(queryAcctBalanceResponse.getHasHKProduct()) ? YesOrNoEnum.NO.getCode() : queryAcctBalanceResponse.getHasHKProduct();
            hasHzProduct = StringUtils.isBlank(queryAcctBalanceResponse.getHasHZProduct()) ? YesOrNoEnum.NO.getCode() : queryAcctBalanceResponse.getHasHZProduct();
            List<QueryAcctBalanceResponse.BalanceBean> balanceList = queryAcctBalanceResponse.getBalanceList();
            if (CollectionUtils.isNotEmpty(balanceList)) {
                for (QueryAcctBalanceResponse.BalanceBean balanceBean : balanceList) {
                    if (NaProductFeeTypeEnum.HOWBUY_FEE.getCode().equals(balanceBean.getNaProductFeeType())) {
                        hasNaProduct = YesOrNoEnum.YES.getCode();
                    }
                }
            }
        }
        authCheckDto.setHasCashBalance(hasCashBalance);
        authCheckDto.setHasHKProduct(hasHKProduct);
        authCheckDto.setHasHZProduct(hasHzProduct);
        authCheckDto.setHasNaProduct(hasNaProduct);
        return authCheckDto;
    }
}
