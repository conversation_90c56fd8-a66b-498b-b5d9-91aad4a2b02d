package com.howbuy.cgi.trade.simu.service.task;

import com.howbuy.cgi.trade.simu.model.dto.QueryBalanceContext;
import com.howbuy.interlayer.product.model.HighProductAppointmentInfoModel;
import com.howbuy.interlayer.product.service.HighProductService;
import com.howbuy.tms.common.utils.DateUtils;
import org.apache.commons.collections.CollectionUtils;

import java.util.*;

/**
 * @Description:查询最近预约日历
 * @Author: yun.lu
 * Date: 2025/8/4 11:01
 */
public class QueryLatestByAppintDtTask extends HowbuyBaseTask {
    private List<String> fundCodes;
    private QueryBalanceContext queryBalanceContext;
    private HighProductService highProductService;

    @Override
    protected void callTask() {
        if (CollectionUtils.isEmpty(fundCodes)) {
            queryBalanceContext.setProductAppointMap(new HashMap<>());
        }

        String appDtm = DateUtils.formatToString(new Date(), DateUtils.YYYYMMDDHHMMSS);
        List<HighProductAppointmentInfoModel> productAppointList = highProductService.getLatestByAppintDt(fundCodes, appDtm);

        Map<String, List<HighProductAppointmentInfoModel>> productAppointMap = new HashMap<>(fundCodes.size());
        for (HighProductAppointmentInfoModel highProductAppointmentInfoModel : productAppointList) {
            List<HighProductAppointmentInfoModel> productAppointDtList = productAppointMap.get(highProductAppointmentInfoModel.getProductId());
            if (productAppointDtList == null) {
                productAppointDtList = new ArrayList<>();
            }

            productAppointDtList.add(highProductAppointmentInfoModel);
            productAppointMap.put(highProductAppointmentInfoModel.getProductId(), productAppointDtList);
        }
        queryBalanceContext.setProductAppointMap(productAppointMap);
    }

    public List<String> getFundCodes() {
        return fundCodes;
    }

    public void setFundCodes(List<String> fundCodes) {
        this.fundCodes = fundCodes;
    }

    public QueryBalanceContext getQueryBalanceContext() {
        return queryBalanceContext;
    }

    public void setQueryBalanceContext(QueryBalanceContext queryBalanceContext) {
        this.queryBalanceContext = queryBalanceContext;
    }

    public HighProductService getHighProductService() {
        return highProductService;
    }

    public void setHighProductService(HighProductService highProductService) {
        this.highProductService = highProductService;
    }

    public QueryLatestByAppintDtTask(List<String> fundCodes, QueryBalanceContext queryBalanceContext, HighProductService highProductService) {
        this.fundCodes = fundCodes;
        this.queryBalanceContext = queryBalanceContext;
        this.highProductService = highProductService;
    }
}
