package com.howbuy.cgi.trade.simu.model.dto;

import com.howbuy.tms.common.enums.busi.OwnershipTransferIdentityEnum;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @description:(高端持有基金)
 * @reason:
 * <AUTHOR>
 * @date 2018年6月21日 下午9:06:36
 * @since JDK 1.7
 */
public class HkFundBalanceDto implements Serializable {
    private static final long serialVersionUID = -5610418332175150805L;
    /**
     * 产品编码
     */
    private String fundCode;
    /**
     * 产品名称
     */
    private String fundNameAbbr;
    /**
     * 产品类型
     */
    private String productType;
    /**
     * 产品子类型
     */
    private String productSubType;
    /**
     * 净值
     */
    private BigDecimal nav;
    /**
     * 净值日期
     */
    private String navDate;
    /**
     * 分红提醒标识,0:无需分红提醒;1:提醒收益偏差;2:提醒即将分红
     */
    private String navDivFlag;
    private BigDecimal totalBala;
    /**
     * 待确认份额
     */
    private BigDecimal unconfirmedVol;
    /**
     * 待确认金额
     */
    private BigDecimal unconfirmedAmt;
    /**
     * 市值(人民币)
     */
    private BigDecimal fundAsset;
    /**
     * 市值(当前币种)
     */
    private BigDecimal currencyMarketValue;
    /**
     * 收益率
     */
    private BigDecimal yieldRate;
    /**
     * 当前收益(人民币）
     */
    private BigDecimal currentAsset;
    /**
     * 当前收益（当前币种）
     */
    private BigDecimal currentAssetCurrency;
    /**
     * 0-计算中；1-计算完成
     */
    private String incomeCalStat;
    /**
     * 可追加 1.可追加，其他值不可追加
     */
    private String canBuy;

    /**
     * 产品购买状态,CAN_BUY:可购买,CAN_NOT_BUY:不可购买,CAN_MODIFY:可修改,CAN_NOT_MODIFY:不可修改;
     */
    private String fundBuyStatus;
    /**
     *  赎回状态 1.可赎回 ，其他值不可赎回
     */
    private String canRedeem;
    /**
     * 单位持仓成本
     */
    private BigDecimal unitBalanceCostExFee;
    /**
     * 单位持仓成本
     */
    private BigDecimal unitBalanceCostExFeeRmb;
    /**
     * 黑名单
     */
    private String inDirectBlackList;
    /**
     *  pc访问地址
     */
    private String webUrl;
    /**
     * 代销产品推荐
     */
    private String cptjly;
    /**
     * 基金分类 - 一级分类
     */
    private String ejflName;
    /**
     * 开放日开始时间
     */
    private String openStartDate;
    /**
     * 开放日结束时间
     */
    private String openEndDate;
    /**
     * 资产策略一级分类 0.多策略 1.股票型 2.股权型 3.CTA 4.另类 5.固收与中性
     */
    private String assetStrategyFirstType;

    /**
     * 回款进度
     */
    private String accumBackRatio;
    /**
     * 币种
     */
    private String currency;
    /**
     * 私募股权回款
     */
    private BigDecimal cashCollection;
    /**
     * 私募股权回款(当前币种)
     */
    private BigDecimal currencyCashCollection;
    /**
     * 净购买金额(投资成本)(当前币种)
     */
    private BigDecimal currencyNetBuyAmount;
    /**
     * 净购买金额(投资成本)
     */
    private BigDecimal netBuyAmount;
    /**
     * 认缴金额
     */
    private BigDecimal paidInAmt;
    /**
     * 实缴金额
     */
    private BigDecimal paidTotalAmt;

    /**
     * 实缴百分比
     */
    private String paidSubTotalRatio;
    /**
     * 产品存续期限(类似于5+3+2这种说明)
     */
    private String fundCXQXStr;
    /**
     * 分期成立标识(证券类有此标识:0-否,1-是)
     */
    private String StageEstablishFlag;
    /**
     * 分次call标识(股权类有此标识:0-否,1-是)
     */
    private String fractionateCallFlag;
    /**
     * 销售类型 :1-直销; 2-代销
     */
    private String scaleType;
    /**
     * 好买香港代销标识: 0-否; 1-是
     */
    private String hkSaleFlag;
    /**
     *
     * 是否复构 0-否 1-是
     **/
    private String rePurchaseFlag;

    /**
     *  业绩比较基准
     **/
    private String  benchmark;

    /**
     *  起息日
     **/
    private String valueDate;

    /**
     * 到期日
     **/
    private String dueDate;

    /**
     * 标准固收标识(固收类有此标识:0-否,1-是)
     */
    private String standardFixedIncomeFlag;

    /**
     * 清盘中标识(清盘中产品有此标识:0-否,1-是)
     */
    private String crisisFlag;
    /**
     * 固收货币产品七日年化收益（来自DB）
     */
    private BigDecimal yieldIncome;
    /**
     * 固收货币产品净值日期（来自DB）
     */
    private String yieldIncomeDt;

    /**
     * 是否可以修改复购协议(0-否,1-是)
     */
    private String canModifyRepurchaseProtocolFlag;

    /**
     * 客户复购协议
     */
    private CustRepurchaseProtocolDto custRepurchaseProtocol;

    /**
     * 产品复购参数配置 0-否 1-是
     */
    private String productRepurchaseFlag;
    /**
     * 业绩比较基准类型（固收类产品有此标识）<br> 0-业绩比较基准（年化） 1-业绩报酬计提基准（年化）
     **/
    private String benchmarkType;
    /**
     * DB产品类型
     */
    private String dbFundType;

    /**
     * 可赎回份额
     */
    private BigDecimal availVol;

    /**
     * 购买预约开始日期
     */
    private String  buyAppointStartDt;

    /**
     * 赎回预约结束日期
     */
    private String  buyAppointEndDt;

    /**
     * 打款截止日期
     */
    private String  payEndDate;

    /**
     * 赎回预约开始日期
     */
    private String  sellAppointStartDt;

    /**
     * 赎回预约结束日期
     */
    private String  sellAppointEndDt;

    /**
     *产品销售来源 0-好买 1-海外 2-其他
     */
    private String  productSaleSource;
    /**
     *锁定份额
     */
    private BigDecimal lockVol;

    /**
     * NA产品收费类型 10201-好买收费 0-管理人收费
     */
    private String naProductFeeType;
    /**
     * 累计应收管理费
     */
    private BigDecimal receivManageFee;
    /**
     * 累计应收业绩报酬
     */
    private BigDecimal receivPreformFee;
    /**
     * NA产品费后市值(当前币种， 减去当前累计应收管理费和累计应收业绩报酬)
     */
    private BigDecimal currencyMarketValueExFee;
    /**
     *  NA产品费后市值(人民币， 减去当前累计应收管理费和累计应收业绩报酬)
     */
    private BigDecimal marketValueExFee;
    /**
     * 是否高毅领山产品 0-否 1-是
     */
    private String gaoYiLingShanFlag;
    /**
     * 平衡因子
     */
    private BigDecimal balanceFactor;
    /**
     * 平衡因子转换完成 1-是 0-否
     */
    private String convertFinish;
    /**
     * 平衡因子日期
     */
    private String balanceFactorDate;

    /**
     * 股权转让标识
     * @see OwnershipTransferIdentityEnum
     */
    private String ownershipTransferIdentity;

    /**
     * 产品策略
     */
    private String strategy;


    /**
     * 产品策略中文描述
     */
    private String strategyStr;


    /**********************字段String处理 start ***************************/
    /**
     * 份额
     */
    private String navStr;
    /**
     * 总持仓
     */
    private String totalBalaStr;
    /**
     * 待确认份额
     */
    private String unconfirmedVolStr;
    /**
     * 待确认金额
     */
    private String unconfirmedAmtStr;
    /**
     *  市值(人民币)
     */
    private String fundAssetStr;
    /**
     * 市值(当前币种)
     */
    private String currencyMarketValueStr;
    /**
     * 收益率
     */
    private String yieldRateStr;
    /**
     * 当前收益(人民币）
     */
    private String currentAssetStr;
    /**
     * 当前收益（当前币种）
     */
    private String currentAssetCurrencyStr;
    /**
     * 私募股权回款
     */
    private String cashCollectionStr;
    /**
     * 私募股权回款(当前币种)
     */
    private String currencyCashCollectionStr;
    /**
     * 净购买金额(投资成本)(当前币种)
     */
    private String currencyNetBuyAmountStr;
    /**
     * 净购买金额(投资成本)
     */
    private String netBuyAmountStr;
    /**
     * 认缴金额
     */
    private String paidInAmtStr;
    /**
     * 累计应收管理费
     */
    private String receivManageFeeStr;
    /**
     * 累计应收业绩报酬
     */
    private String receivPreformFeeStr;
    /**
     * NA产品费后市值(当前币种， 减去当前累计应收管理费和累计应收业绩报酬)
     */
    private String currencyMarketValueExFeeStr;
    /**
     * // NA产品费后市值(人民币， 减去当前累计应收管理费和累计应收业绩报酬)
     */
    private String marketValueExFeeStr;
    /**********************字段String处理 end***************************/

    /**
     * 股权产品期限说明
     */
    private String cpqxsm;

    /**
     * 子产品代码
     */
    private String subProductCode;
    /**
     * 产品成立日期
     */
    private String establishDt;

    /**
     * 是否海外储蓄罐(1:是;0:否)
     */
    private String sfhwcxg;

    public String getCpqxsm() {
        return cpqxsm;
    }
    /**
     * 万份收益--持仓特殊产品指标控制需求新增 20221122
     */
    private BigDecimal copiesIncome;

    /**
     * 净值披露方式(1-净值,2-份额收益)--持仓特殊产品指标控制需求新增 20221122
     */
    private String navDisclosureType;

    /**
     * 对于 阳光私募/非货基型券商集合 产品，若【最新净值日期】比第一笔交易的【确认日期】早3个月以上
     * 异常标志(0-否 1-是)--持仓特殊产品指标控制需求新增 20221122
     */
    private String abnormalFlag;

    /** 人民币市值-是否控制表人为置空 */
    private String fundAssetCtl;
    /** 当前币种市值-是否控制表人为置空 */
    private String currencyMarketValueCtl;
    /** NA产品费后市值（当前币种）-是否控制表人为置空 */
    private String currencyMarketValueExFeeCtl;
    /** NA产品费后市值（人民币）-是否控制表人为置空 */
    private String marketValueExFeeCtl;
    /** 人民币收益-是否控制表人为置空 */
    private String currentAssetCtl;
    /** 当前币种收益-是否控制表人为置空 */
    private String currentAssetCurrencyCtl;
    /** 收益率-是否控制表人为置空 */
    private String yieldRateCtl;
    /** 份额-是否控制表人为置空 */
    private String totalBalaCtl;
    /** 待确认份额-是否控制表人为置空 */
    private String unconfirmedVolCtl;
    /** 净值-是否控制表人为置空 */
    private String navCtl;
    /**
     * 是否拆单产品 1-是
     */
    private String stageFlag;
    // 千禧年持仓功能适配需求 20230216
    /** 是否为千禧年产品 0-否、1-是 */
    private String qianXiFlag = "0";

    /** 待投金额（人民币） */
    private BigDecimal unPaidInAmt;

    /** 待投金额（当前币种） */
    private BigDecimal currencyUnPaidInAmt;

    /**
     * 分销机构编码
     */
    private String disCode;

    /**
     * 产品最新收益
     */
    private BigDecimal dayIncome;


    /**
     * 最新收益率
     */
    private BigDecimal dayIncomeRate;


    /**
     * 最新收益日期
     */
    private String incomeLatestDay;

    /**
     * 产品累计收益
     */
    private BigDecimal accumIncome;

    public String getAssetStrategyFirstType() {
        return assetStrategyFirstType;
    }

    public void setAssetStrategyFirstType(String assetStrategyFirstType) {
        this.assetStrategyFirstType = assetStrategyFirstType;
    }

    public String getAccumBackRatio() {
        return accumBackRatio;
    }

    public void setAccumBackRatio(String accumBackRatio) {
        this.accumBackRatio = accumBackRatio;
    }

    public BigDecimal getPaidTotalAmt() {
        return paidTotalAmt;
    }

    public void setPaidTotalAmt(BigDecimal paidTotalAmt) {
        this.paidTotalAmt = paidTotalAmt;
    }

    public String getPaidSubTotalRatio() {
        return paidSubTotalRatio;
    }

    public void setPaidSubTotalRatio(String paidSubTotalRatio) {
        this.paidSubTotalRatio = paidSubTotalRatio;
    }

    public String getSubProductCode() {
        return subProductCode;
    }

    public void setSubProductCode(String subProductCode) {
        this.subProductCode = subProductCode;
    }

    public String getEstablishDt() {
        return establishDt;
    }

    public void setEstablishDt(String establishDt) {
        this.establishDt = establishDt;
    }

    public String getStrategy() {
        return strategy;
    }

    public void setStrategy(String strategy) {
        this.strategy = strategy;
    }

    public String getStrategyStr() {
        return strategyStr;
    }

    public void setStrategyStr(String strategyStr) {
        this.strategyStr = strategyStr;
    }

    public BigDecimal getUnitBalanceCostExFee() {
        return unitBalanceCostExFee;
    }

    public void setUnitBalanceCostExFee(BigDecimal unitBalanceCostExFee) {
        this.unitBalanceCostExFee = unitBalanceCostExFee;
    }

    public BigDecimal getUnitBalanceCostExFeeRmb() {
        return unitBalanceCostExFeeRmb;
    }

    public void setUnitBalanceCostExFeeRmb(BigDecimal unitBalanceCostExFeeRmb) {
        this.unitBalanceCostExFeeRmb = unitBalanceCostExFeeRmb;
    }

    public String getStageFlag() {
        return stageFlag;
    }

    public void setStageFlag(String stageFlag) {
        this.stageFlag = stageFlag;
    }


    public BigDecimal getDayIncome() {
        return dayIncome;
    }

    public void setDayIncome(BigDecimal dayIncome) {
        this.dayIncome = dayIncome;
    }

    public BigDecimal getDayIncomeRate() {
        return dayIncomeRate;
    }

    public void setDayIncomeRate(BigDecimal dayIncomeRate) {
        this.dayIncomeRate = dayIncomeRate;
    }

    public String getIncomeLatestDay() {
        return incomeLatestDay;
    }

    public void setIncomeLatestDay(String incomeLatestDay) {
        this.incomeLatestDay = incomeLatestDay;
    }

    public BigDecimal getAccumIncome() {
        return accumIncome;
    }

    public void setAccumIncome(BigDecimal accumIncome) {
        this.accumIncome = accumIncome;
    }

    public String getDisCode() {
        return disCode;
    }

    public void setDisCode(String disCode) {
        this.disCode = disCode;
    }

    public String getQianXiFlag() {
        return qianXiFlag;
    }

    public void setQianXiFlag(String qianXiFlag) {
        this.qianXiFlag = qianXiFlag;
    }

    public BigDecimal getUnPaidInAmt() {
        return unPaidInAmt;
    }

    public void setUnPaidInAmt(BigDecimal unPaidInAmt) {
        this.unPaidInAmt = unPaidInAmt;
    }

    public BigDecimal getCurrencyUnPaidInAmt() {
        return currencyUnPaidInAmt;
    }

    public void setCurrencyUnPaidInAmt(BigDecimal currencyUnPaidInAmt) {
        this.currencyUnPaidInAmt = currencyUnPaidInAmt;
    }

    public String getFundAssetCtl() {
        return fundAssetCtl;
    }

    public void setFundAssetCtl(String fundAssetCtl) {
        this.fundAssetCtl = fundAssetCtl;
    }

    public String getCurrencyMarketValueCtl() {
        return currencyMarketValueCtl;
    }

    public void setCurrencyMarketValueCtl(String currencyMarketValueCtl) {
        this.currencyMarketValueCtl = currencyMarketValueCtl;
    }

    public String getCurrencyMarketValueExFeeCtl() {
        return currencyMarketValueExFeeCtl;
    }

    public void setCurrencyMarketValueExFeeCtl(String currencyMarketValueExFeeCtl) {
        this.currencyMarketValueExFeeCtl = currencyMarketValueExFeeCtl;
    }

    public String getOwnershipTransferIdentity() {
        return ownershipTransferIdentity;
    }

    public void setOwnershipTransferIdentity(String ownershipTransferIdentity) {
        this.ownershipTransferIdentity = ownershipTransferIdentity;
    }

    public String getMarketValueExFeeCtl() {
        return marketValueExFeeCtl;
    }

    public void setMarketValueExFeeCtl(String marketValueExFeeCtl) {
        this.marketValueExFeeCtl = marketValueExFeeCtl;
    }

    public String getCurrentAssetCtl() {
        return currentAssetCtl;
    }

    public void setCurrentAssetCtl(String currentAssetCtl) {
        this.currentAssetCtl = currentAssetCtl;
    }

    public String getCurrentAssetCurrencyCtl() {
        return currentAssetCurrencyCtl;
    }

    public void setCurrentAssetCurrencyCtl(String currentAssetCurrencyCtl) {
        this.currentAssetCurrencyCtl = currentAssetCurrencyCtl;
    }

    public String getYieldRateCtl() {
        return yieldRateCtl;
    }

    public void setYieldRateCtl(String yieldRateCtl) {
        this.yieldRateCtl = yieldRateCtl;
    }

    public String getTotalBalaCtl() {
        return totalBalaCtl;
    }

    public void setTotalBalaCtl(String totalBalaCtl) {
        this.totalBalaCtl = totalBalaCtl;
    }

    public String getUnconfirmedVolCtl() {
        return unconfirmedVolCtl;
    }

    public void setUnconfirmedVolCtl(String unconfirmedVolCtl) {
        this.unconfirmedVolCtl = unconfirmedVolCtl;
    }

    public String getNavCtl() {
        return navCtl;
    }

    public void setNavCtl(String navCtl) {
        this.navCtl = navCtl;
    }



    public String getAbnormalFlag() {
        return abnormalFlag;
    }

    public void setAbnormalFlag(String abnormalFlag) {
        this.abnormalFlag = abnormalFlag;
    }

    public String getNavDisclosureType() {
        return navDisclosureType;
    }

    public void setNavDisclosureType(String navDisclosureType) {
        this.navDisclosureType = navDisclosureType;
    }
    public BigDecimal getCopiesIncome() {
        return copiesIncome;
    }

    public void setCopiesIncome(BigDecimal copiesIncome) {
        this.copiesIncome = copiesIncome;
    }
    public void setCpqxsm(String cpqxsm) {
        this.cpqxsm = cpqxsm;
    }
    private String bigToStr(BigDecimal value) {
        if(value == null){
            return null;
        }
        return  value.toPlainString();
    }

    public String getNavStr() {
        if (null == this.nav) {
            return "--";
        }
        return bigToStr(this.nav);
    }

    public String getTotalBalaStr() {
        return bigToStr(this.totalBala);
    }

    public String getUnconfirmedVolStr() {
        return bigToStr(this.unconfirmedVol);
    }

    public String getUnconfirmedAmtStr() {
        return bigToStr(this.unconfirmedAmt);
    }

    public String getFundAssetStr() {
        return bigToStr(this.fundAsset);
    }

    public String getCurrencyMarketValueStr() {
        return bigToStr(this.currencyMarketValue);
    }

    public String getYieldRateStr() {
        return bigToStr(this.yieldRate);
    }

    public String getCurrentAssetStr() {
        return bigToStr(this.currentAsset);
    }

    public String getCurrentAssetCurrencyStr() {
        return bigToStr(this.currentAssetCurrency);
    }

    public String getCashCollectionStr() {
        return bigToStr(this.cashCollection);
    }

    public String getCurrencyCashCollectionStr() {
        return bigToStr(this.currencyCashCollection);
    }

    public String getCurrencyNetBuyAmountStr() {
        return bigToStr(this.currencyNetBuyAmount);
    }

    public String getNetBuyAmountStr() {
        return bigToStr(this.netBuyAmount);
    }

    public String getPaidInAmtStr() {
        return bigToStr(this.paidInAmt);
    }

    public String getReceivManageFeeStr() {
        return this.receivManageFeeStr;
    }

    public String getReceivPreformFeeStr() {
        return this.receivPreformFeeStr;
    }

    public String getCurrencyMarketValueExFeeStr() {
        return bigToStr(this.currencyMarketValueExFee);
    }

    public String getMarketValueExFeeStr() {
        return bigToStr(this.marketValueExFee);
    }

    public void setNavStr(String navStr) {
        this.navStr = navStr;
    }

    public void setTotalBalaStr(String totalBalaStr) {
        this.totalBalaStr = totalBalaStr;
    }

    public void setUnconfirmedVolStr(String unconfirmedVolStr) {
        this.unconfirmedVolStr = unconfirmedVolStr;
    }

    public void setUnconfirmedAmtStr(String unconfirmedAmtStr) {
        this.unconfirmedAmtStr = unconfirmedAmtStr;
    }

    public void setFundAssetStr(String fundAssetStr) {
        this.fundAssetStr = fundAssetStr;
    }

    public void setCurrencyMarketValueStr(String currencyMarketValueStr) {
        this.currencyMarketValueStr = currencyMarketValueStr;
    }

    public void setYieldRateStr(String yieldRateStr) {
        this.yieldRateStr = yieldRateStr;
    }

    public void setCurrentAssetStr(String currentAssetStr) {
        this.currentAssetStr = currentAssetStr;
    }

    public void setCurrentAssetCurrencyStr(String currentAssetCurrencyStr) {
        this.currentAssetCurrencyStr = currentAssetCurrencyStr;
    }

    public void setCashCollectionStr(String cashCollectionStr) {
        this.cashCollectionStr = cashCollectionStr;
    }

    public void setCurrencyCashCollectionStr(String currencyCashCollectionStr) {
        this.currencyCashCollectionStr = currencyCashCollectionStr;
    }

    public void setCurrencyNetBuyAmountStr(String currencyNetBuyAmountStr) {
        this.currencyNetBuyAmountStr = currencyNetBuyAmountStr;
    }

    public void setNetBuyAmountStr(String netBuyAmountStr) {
        this.netBuyAmountStr = netBuyAmountStr;
    }

    public void setPaidInAmtStr(String paidInAmtStr) {
        this.paidInAmtStr = paidInAmtStr;
    }

    public void setReceivManageFeeStr(String receivManageFeeStr) {
        this.receivManageFeeStr = receivManageFeeStr;
    }

    public void setReceivPreformFeeStr(String receivPreformFeeStr) {
        this.receivPreformFeeStr = receivPreformFeeStr;
    }

    public void setCurrencyMarketValueExFeeStr(String currencyMarketValueExFeeStr) {
        this.currencyMarketValueExFeeStr = currencyMarketValueExFeeStr;
    }

    public void setMarketValueExFeeStr(String marketValueExFeeStr) {
        this.marketValueExFeeStr = marketValueExFeeStr;
    }

    /*************************************************/


    public BigDecimal getCurrencyCashCollection() {
        return currencyCashCollection;
    }

    public void setCurrencyCashCollection(BigDecimal currencyCashCollection) {
        this.currencyCashCollection = currencyCashCollection;
    }

    public BigDecimal getCurrencyNetBuyAmount() {
        return currencyNetBuyAmount;
    }

    public void setCurrencyNetBuyAmount(BigDecimal currencyNetBuyAmount) {
        this.currencyNetBuyAmount = currencyNetBuyAmount;
    }

    public String getStageEstablishFlag() {
        return StageEstablishFlag;
    }

    public void setStageEstablishFlag(String stageEstablishFlag) {
        StageEstablishFlag = stageEstablishFlag;
    }

    public String getFractionateCallFlag() {
        return fractionateCallFlag;
    }

    public void setFractionateCallFlag(String fractionateCallFlag) {
        this.fractionateCallFlag = fractionateCallFlag;
    }

    public String getInDirectBlackList() {
        return inDirectBlackList;
    }

    public void setInDirectBlackList(String inDirectBlackList) {
        this.inDirectBlackList = inDirectBlackList;
    }

    public BigDecimal getPaidInAmt() {
        return paidInAmt;
    }

    public void setPaidInAmt(BigDecimal paidInAmt) {
        this.paidInAmt = paidInAmt;
    }

    public String getFundCXQXStr() {
        return fundCXQXStr;
    }

    public void setFundCXQXStr(String fundCXQXStr) {
        this.fundCXQXStr = fundCXQXStr;
    }

    public String getHkSaleFlag() {
        return hkSaleFlag;
    }

    public void setHkSaleFlag(String hkSaleFlag) {
        this.hkSaleFlag = hkSaleFlag;
    }

    public BigDecimal getNetBuyAmount() {
        return netBuyAmount;
    }

    public void setNetBuyAmount(BigDecimal netBuyAmount) {
        this.netBuyAmount = netBuyAmount;
    }

    public BigDecimal getYieldRate() {
        return yieldRate;
    }

    public void setYieldRate(BigDecimal yieldRate) {
        this.yieldRate = yieldRate;
    }

    public BigDecimal getCurrentAsset() {
        return currentAsset;
    }

    public void setCurrentAsset(BigDecimal currentAsset) {
        this.currentAsset = currentAsset;
    }

    public BigDecimal getCurrentAssetCurrency() {
        return currentAssetCurrency;
    }

    public void setCurrentAssetCurrency(BigDecimal currentAssetCurrency) {
        this.currentAssetCurrency = currentAssetCurrency;
    }

    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }

    public String getFundNameAbbr() {
        return fundNameAbbr;
    }

    public void setFundNameAbbr(String fundNameAbbr) {
        this.fundNameAbbr = fundNameAbbr;
    }

    public String getNav() {
        return this.getNavStr();
    }

    public void setNav(BigDecimal nav) {
        this.nav = nav;
    }

    public String getNavDate() {
        return navDate;
    }

    public void setNavDate(String navDate) {
        this.navDate = navDate;
    }

    public String getNavDivFlag() {
        return navDivFlag;
    }

    public void setNavDivFlag(String navDivFlag) {
        this.navDivFlag = navDivFlag;
    }

    public BigDecimal getTotalBala() {
        return totalBala;
    }

    public void setTotalBala(BigDecimal totalBala) {
        this.totalBala = totalBala;
    }

    public BigDecimal getFundAsset() {
        return fundAsset;
    }

    public void setFundAsset(BigDecimal fundAsset) {
        this.fundAsset = fundAsset;
    }

    public String getCanBuy() {
        return canBuy;
    }

    public void setCanBuy(String canBuy) {
        this.canBuy = canBuy;
    }

    public String getCanRedeem() {
        return canRedeem;
    }

    public void setCanRedeem(String canRedeem) {
        this.canRedeem = canRedeem;
    }

    public String getProductType() {
        return productType;
    }

    public void setProductType(String productType) {
        this.productType = productType;
    }

    public String getScaleType() {
        return scaleType;
    }

    public void setScaleType(String scaleType) {
        this.scaleType = scaleType;
    }

    public String getWebUrl() {
        return webUrl;
    }

    public void setWebUrl(String webUrl) {
        this.webUrl = webUrl;
    }

    public String getCptjly() {
        return cptjly;
    }

    public void setCptjly(String cptjly) {
        this.cptjly = cptjly;
    }

    public String getEjflName() {
        return ejflName;
    }

    public void setEjflName(String ejflName) {
        this.ejflName = ejflName;
    }

    public String getOpenStartDate() {
        return openStartDate;
    }

    public void setOpenStartDate(String openStartDate) {
        this.openStartDate = openStartDate;
    }

    public String getOpenEndDate() {
        return openEndDate;
    }

    public void setOpenEndDate(String openEndDate) {
        this.openEndDate = openEndDate;
    }

    public BigDecimal getUnconfirmedVol() {
        return unconfirmedVol;
    }

    public void setUnconfirmedVol(BigDecimal unconfirmedVol) {
        this.unconfirmedVol = unconfirmedVol;
    }

    public BigDecimal getUnconfirmedAmt() {
        return unconfirmedAmt;
    }

    public void setUnconfirmedAmt(BigDecimal unconfirmedAmt) {
        this.unconfirmedAmt = unconfirmedAmt;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public BigDecimal getCurrencyMarketValue() {
        return currencyMarketValue;
    }

    public void setCurrencyMarketValue(BigDecimal currencyMarketValue) {
        this.currencyMarketValue = currencyMarketValue;
    }

    public String getIncomeCalStat() {
        return incomeCalStat;
    }

    public void setIncomeCalStat(String incomeCalStat) {
        this.incomeCalStat = incomeCalStat;
    }

    public BigDecimal getCashCollection() {
        return cashCollection;
    }

    public void setCashCollection(BigDecimal cashCollection) {
        this.cashCollection = cashCollection;
    }

    public String getProductSubType() {
        return productSubType;
    }

    public void setProductSubType(String productSubType) {
        this.productSubType = productSubType;
    }

    public String getRePurchaseFlag() {
        return rePurchaseFlag;
    }

    public void setRePurchaseFlag(String rePurchaseFlag) {
        this.rePurchaseFlag = rePurchaseFlag;
    }

    public String getBenchmark() {
        return benchmark;
    }

    public void setBenchmark(String benchmark) {
        this.benchmark = benchmark;
    }

    public String getValueDate() {
        return valueDate;
    }

    public void setValueDate(String valueDate) {
        this.valueDate = valueDate;
    }

    public String getDueDate() {
        return dueDate;
    }

    public void setDueDate(String dueDate) {
        this.dueDate = dueDate;
    }

    public String getStandardFixedIncomeFlag() {
        return standardFixedIncomeFlag;
    }

    public void setStandardFixedIncomeFlag(String standardFixedIncomeFlag) {
        this.standardFixedIncomeFlag = standardFixedIncomeFlag;
    }

    public String getCrisisFlag() {
        return crisisFlag;
    }

    public void setCrisisFlag(String crisisFlag) {
        this.crisisFlag = crisisFlag;
    }

    public BigDecimal getYieldIncome() {
        return yieldIncome;
    }

    public void setYieldIncome(BigDecimal yieldIncome) {
        this.yieldIncome = yieldIncome;
    }

    public String getFundBuyStatus() {
        return fundBuyStatus;
    }

    public void setFundBuyStatus(String fundBuyStatus) {
        this.fundBuyStatus = fundBuyStatus;
    }

    public String getYieldIncomeDt() {
        return yieldIncomeDt;
    }

    public void setYieldIncomeDt(String yieldIncomeDt) {
        this.yieldIncomeDt = yieldIncomeDt;
    }

    public CustRepurchaseProtocolDto getCustRepurchaseProtocol() {
        return custRepurchaseProtocol;
    }

    public void setCustRepurchaseProtocol(CustRepurchaseProtocolDto custRepurchaseProtocol) {
        this.custRepurchaseProtocol = custRepurchaseProtocol;
    }

    public String getCanModifyRepurchaseProtocolFlag() {
        return canModifyRepurchaseProtocolFlag;
    }

    public void setCanModifyRepurchaseProtocolFlag(String canModifyRepurchaseProtocolFlag) {
        this.canModifyRepurchaseProtocolFlag = canModifyRepurchaseProtocolFlag;
    }

    public String getProductRepurchaseFlag() {
        return productRepurchaseFlag;
    }

    public void setProductRepurchaseFlag(String productRepurchaseFlag) {
        this.productRepurchaseFlag = productRepurchaseFlag;
    }

    public String getBenchmarkType() {
        return benchmarkType;
    }

    public void setBenchmarkType(String benchmarkType) {
        this.benchmarkType = benchmarkType;
    }

    public String getDbFundType() {
        return dbFundType;
    }

    public void setDbFundType(String dbFundType) {
        this.dbFundType = dbFundType;
    }


    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public BigDecimal getAvailVol() {
        return availVol;
    }

    public void setAvailVol(BigDecimal availVol) {
        this.availVol = availVol;
    }

    public String getBuyAppointStartDt() {
        return buyAppointStartDt;
    }

    public void setBuyAppointStartDt(String buyAppointStartDt) {
        this.buyAppointStartDt = buyAppointStartDt;
    }

    public String getBuyAppointEndDt() {
        return buyAppointEndDt;
    }

    public void setBuyAppointEndDt(String buyAppointEndDt) {
        this.buyAppointEndDt = buyAppointEndDt;
    }

    public String getPayEndDate() {
        return payEndDate;
    }

    public void setPayEndDate(String payEndDate) {
        this.payEndDate = payEndDate;
    }

    public String getSellAppointStartDt() {
        return sellAppointStartDt;
    }

    public void setSellAppointStartDt(String sellAppointStartDt) {
        this.sellAppointStartDt = sellAppointStartDt;
    }

    public String getSellAppointEndDt() {
        return sellAppointEndDt;
    }

    public void setSellAppointEndDt(String sellAppointEndDt) {
        this.sellAppointEndDt = sellAppointEndDt;
    }

    public String getProductSaleSource() {
        return productSaleSource;
    }

    public void setProductSaleSource(String productSaleSource) {
        this.productSaleSource = productSaleSource;
    }

    public BigDecimal getLockVol() {
        return lockVol;
    }

    public void setLockVol(BigDecimal lockVol) {
        this.lockVol = lockVol;
    }

    public String getNaProductFeeType() {
        return naProductFeeType;
    }

    public void setNaProductFeeType(String naProductFeeType) {
        this.naProductFeeType = naProductFeeType;
    }

    public BigDecimal getReceivManageFee() {
        return receivManageFee;
    }

    public void setReceivManageFee(BigDecimal receivManageFee) {
        this.receivManageFee = receivManageFee;
    }

    public BigDecimal getReceivPreformFee() {
        return receivPreformFee;
    }

    public void setReceivPreformFee(BigDecimal receivPreformFee) {
        this.receivPreformFee = receivPreformFee;
    }

    public BigDecimal getCurrencyMarketValueExFee() {
        return currencyMarketValueExFee;
    }

    public void setCurrencyMarketValueExFee(BigDecimal currencyMarketValueExFee) {
        this.currencyMarketValueExFee = currencyMarketValueExFee;
    }

    public BigDecimal getMarketValueExFee() {
        return marketValueExFee;
    }

    public void setMarketValueExFee(BigDecimal marketValueExFee) {
        this.marketValueExFee = marketValueExFee;
    }

    public String getGaoYiLingShanFlag() {
        return gaoYiLingShanFlag;
    }

    public void setGaoYiLingShanFlag(String gaoYiLingShanFlag) {
        this.gaoYiLingShanFlag = gaoYiLingShanFlag;
    }

    public BigDecimal getBalanceFactor() {
        return balanceFactor;
    }

    public void setBalanceFactor(BigDecimal balanceFactor) {
        this.balanceFactor = balanceFactor;
    }

    public String getConvertFinish() {
        return convertFinish;
    }

    public void setConvertFinish(String convertFinish) {
        this.convertFinish = convertFinish;
    }

    public String getBalanceFactorDate() {
        return balanceFactorDate;
    }

    public void setBalanceFactorDate(String balanceFactorDate) {
        this.balanceFactorDate = balanceFactorDate;
    }

    public String getSfhwcxg() {
        return sfhwcxg;
    }

    public void setSfhwcxg(String sfhwcxg) {
        this.sfhwcxg = sfhwcxg;
    }
}
