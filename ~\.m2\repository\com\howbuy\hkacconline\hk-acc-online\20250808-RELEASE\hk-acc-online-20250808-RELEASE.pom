<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.3.12.RELEASE</version>
        <relativePath />
    </parent>
    <groupId>com.howbuy.hkacconline</groupId>
    <artifactId>hk-acc-online</artifactId>
    <version>20250808-RELEASE</version>
    <packaging>pom</packaging>
    <name>hk-acc-online</name>
    <properties>
        
        <java.version>1.8</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>

        <nacos-spring-context.version>1.1.0</nacos-spring-context.version>
        <nacos-config-spring-boot-starter.version>0.2.10</nacos-config-spring-boot-starter.version>
        <org.springframework.security.version>3.2.6.RELEASE</org.springframework.security.version>
        <dubbo.version>2.7.15</dubbo.version>

        <mysql.version>5.1.49</mysql.version>
        <mybatis.plus.version>3.5.5</mybatis.plus.version>
        <pagehelper.spring.version>1.2.3</pagehelper.spring.version>
        <druid.version>0.2.9</druid.version>

        <fastjson.version>2.0.37</fastjson.version>
        <ch.qos.logback.version>1.2.3</ch.qos.logback.version>
        <joda-time.version>2.3</joda-time.version>
        <aspectj.version>1.8.13</aspectj.version>
        <dom4j.version>1.6.1</dom4j.version>
        <ehcache.version>1.3.0</ehcache.version>
        <jedis.version>2.9.3</jedis.version>
        <org.javassist.version>3.20.0-GA</org.javassist.version>
        <com.squareup.okhttp3.version>4.12.0</com.squareup.okhttp3.version>

        <com.howbuy.howbuy-boot-actuator.version>1.0.7-RELEASE</com.howbuy.howbuy-boot-actuator.version>
        <com.howbuy.howbuy-sharding-id.version>2.0.4-RELEASE</com.howbuy.howbuy-sharding-id.version>
        <com.howbuy.common-facade.version>3.5.7-RELEASE</com.howbuy.common-facade.version>
        <com.howbuy.common-service.version>3.5.7-RELEASE</com.howbuy.common-service.version>
        <com.howbuy.acc-common-utils.version>3.5.9-RELEASE</com.howbuy.acc-common-utils.version>
        <com.howbuy.howbuy-auth-facade.version>2.1.9-RELEASE</com.howbuy.howbuy-auth-facade.version>
        <com.howbuy.howbuy-cachemanagement.version>3.5.1-RELEASE</com.howbuy.howbuy-cachemanagement.version>
        <com.howbuy.howbuy-ccms-watcher.version>6.0.1-RELEASE</com.howbuy.howbuy-ccms-watcher.version>
        <com.howbuy.howbuy-message-service.version>2.2.1-RELEASE</com.howbuy.howbuy-message-service.version>
        <com.howbuy.howbuy-message-rocket.version>2.2.1-RELEASE</com.howbuy.howbuy-message-rocket.version>
        <com.howbuy.hk-acc-online.version>20250808-RELEASE</com.howbuy.hk-acc-online.version>
        <com.howbuy.hk-acc-common.version>20250808-RELEASE</com.howbuy.hk-acc-common.version>
        <com.howbuy.message-public-client.version>5.1.13-RELEASE</com.howbuy.message-public-client.version>
        <com.howbuy.crm-td-client.version>1.9.5.7-RELEASE</com.howbuy.crm-td-client.version>
        <com.howbuy.crm-nt-client.version>1.8.2.2-RELEASE</com.howbuy.crm-nt-client.version>
        <com.howbuy.dtms-product.version>1.7.9.7-RELEASE</com.howbuy.dtms-product.version>
        <com.howbuy.dtms-order-client.version>1.9.4.7-RELEASE</com.howbuy.dtms-order-client.version>

        <powermock.version>2.0.2</powermock.version>
        <com.howbuy.acc-center-facade.version>3.5.9-RELEASE</com.howbuy.acc-center-facade.version>
<com.howbuy.dtms-product-client.version>1.9.0.8-RELEASE</com.howbuy.dtms-product-client.version>
<com.howbuy.hk-acc-common-util.version>3.6.4-RELEASE</com.howbuy.hk-acc-common-util.version>
<com.howbuy.hk-acc-common-file.version>3.6.4-RELEASE</com.howbuy.hk-acc-common-file.version>
</properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.alibaba.nacos</groupId>
                <artifactId>nacos-spring-context</artifactId>
                <version>${nacos-spring-context.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.fasterxml.jackson.core</groupId>
                        <artifactId>jackson-databind</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.fasterxml.jackson.core</groupId>
                        <artifactId>jackson-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>slf4j-api</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-logging</artifactId>
                        <groupId>commons-logging</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>commons-io</groupId>
                        <artifactId>commons-io</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-codec</artifactId>
                        <groupId>commons-codec</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.alibaba.boot</groupId>
                <artifactId>nacos-config-spring-boot-starter</artifactId>
                <version>${nacos-config-spring-boot-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo-remoting-zookeeper</artifactId>
                <version>${dubbo.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>log4j</groupId>
                        <artifactId>log4j</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.dubbo</groupId>
                        <artifactId>dubbo-remoting-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.dubbo</groupId>
                        <artifactId>dubbo-common</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo</artifactId>
                <version>${dubbo.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.security</groupId>
                <artifactId>spring-security-core</artifactId>
                <version>${org.springframework.security.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-expression</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis.plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <version>${pagehelper.spring.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.mybatis</groupId>
                        <artifactId>mybatis</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.mybatis</groupId>
                        <artifactId>mybatis-spring</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.alibaba.druid</groupId>
                <artifactId>druid-wrapper</artifactId>
                <version>${druid.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>

            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-classic</artifactId>
                <version>${ch.qos.logback.version}</version>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>log4j-over-slf4j</artifactId>
                <version>1.7.22</version>
            </dependency>

            <dependency>
                <groupId>joda-time</groupId>
                <artifactId>joda-time</artifactId>
                <version>${joda-time.version}</version>
            </dependency>
            <dependency>
                <groupId>org.aspectj</groupId>
                <artifactId>aspectjweaver</artifactId>
                <version>${aspectj.version}</version>
            </dependency>
            <dependency>
                <groupId>org.aspectj</groupId>
                <artifactId>aspectjrt</artifactId>
                <version>${aspectj.version}</version>
            </dependency>
            <dependency>
                <groupId>dom4j</groupId>
                <artifactId>dom4j</artifactId>
                <version>${dom4j.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-lang</groupId>
                <artifactId>commons-lang</artifactId>
                <version>2.6</version>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.javassist</groupId>
                <artifactId>javassist</artifactId>
                <version>${org.javassist.version}</version>
            </dependency>


            <dependency>
                <groupId>com.howbuy.boot</groupId>
                <artifactId>howbuy-boot-actuator</artifactId>
                <version>${com.howbuy.howbuy-boot-actuator.version}</version>
            </dependency>
            <dependency>
                <groupId>com.howbuy.sharding</groupId>
                <artifactId>howbuy-sharding-id</artifactId>
                <version>${com.howbuy.howbuy-sharding-id.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.zaxxer</groupId>
                        <artifactId>HikariCP</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.howbuy.common</groupId>
                <artifactId>common-facade</artifactId>
                <version>${com.howbuy.common-facade.version}</version>
            </dependency>
            <dependency>
                <groupId>com.howbuy.common</groupId>
                <artifactId>common-service</artifactId>
                <version>${com.howbuy.common-service.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>*</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.howbuy.commons.validator</groupId>
                <artifactId>howbuy-commons-validator</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.howbuy.acc</groupId>
                <artifactId>acc-common-utils</artifactId>
                <version>${com.howbuy.acc-common-utils.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>*</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.howbuy</groupId>
                <artifactId>utils</artifactId>
                <version>1.0.0-SNAPSHOT</version>
                <exclusions>
                    <exclusion>
                        <groupId>*</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.howbuy</groupId>
                <artifactId>howbuy-auth-facade</artifactId>
                <version>${com.howbuy.howbuy-auth-facade.version}</version>
            </dependency>

            <dependency>
                <groupId>com.howbuy.hkacconline</groupId>
                <artifactId>hk-acc-online-facade</artifactId>
                <version>${com.howbuy.hk-acc-online.version}</version>
            </dependency>
            <dependency>
                <groupId>com.howbuy.hkacconline</groupId>
                <artifactId>hk-acc-online-dao</artifactId>
                <version>${com.howbuy.hk-acc-online.version}</version>
            </dependency>
            <dependency>
                <groupId>com.howbuy.hkacccommon</groupId>
                <artifactId>hk-acc-common-util</artifactId>
                <version>${com.howbuy.hk-acc-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.howbuy.hkacccommon</groupId>
                <artifactId>hk-acc-common-file</artifactId>
                <version>${com.howbuy.hk-acc-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.howbuy.cc.message</groupId>
                <artifactId>message-public-client</artifactId>
                <version>${com.howbuy.message-public-client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.howbuy.acccenter</groupId>
                <artifactId>acc-center-facade</artifactId>
                <version>${com.howbuy.acc-center-facade.version}</version>
            </dependency>
            <dependency>
                <groupId>com.howbuy.crm</groupId>
                <artifactId>crm-td-client</artifactId>
                <version>${com.howbuy.crm-td-client.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>log4j</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.logging.log4j</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>log4j-over-slf4j</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.github.pagehelper</groupId>
                        <artifactId>pagehelper</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            
            
            <dependency>
                <groupId>org.powermock</groupId>
                <artifactId>powermock-module-testng</artifactId>
                <version>${powermock.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.powermock</groupId>
                <artifactId>powermock-api-mockito2</artifactId>
                <version>${powermock.version}</version>
                <scope>test</scope>
            </dependency>
            
            <dependency>
                <groupId>com.howbuy</groupId>
                <artifactId>howbuy-cachemanagement</artifactId>
                <version>${com.howbuy.howbuy-cachemanagement.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>slf4j-log4j12</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>activemq-all</artifactId>
                        <groupId>org.apache.activemq</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.howbuy</groupId>
                <artifactId>howbuy-ccms-watcher</artifactId>
                <version>${com.howbuy.howbuy-ccms-watcher.version}</version>
            </dependency>
            <dependency>
                <groupId>com.howbuy</groupId>
                <artifactId>howbuy-message-service</artifactId>
                <version>${com.howbuy.howbuy-message-service.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>slf4j-log4j12</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.howbuy</groupId>
                <artifactId>howbuy-message-rocket</artifactId>
                <version>${com.howbuy.howbuy-message-rocket.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>slf4j-log4j12</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>${com.squareup.okhttp3.version}</version>
            </dependency>

            <dependency>
                <groupId>com.howbuy.crm</groupId>
                <artifactId>crm-nt-client</artifactId>
                <version>${com.howbuy.crm-nt-client.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>commons-lang3</artifactId>
                        <groupId>org.apache.commons</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>httpclient</artifactId>
                        <groupId>org.apache.httpcomponents</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>joda-time</artifactId>
                        <groupId>joda-time</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>*</artifactId>
                        <groupId>org.apache.logging.log4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-codec</artifactId>
                        <groupId>commons-codec</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>*</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>disruptor</artifactId>
                        <groupId>com.lmax</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>lombok</artifactId>
                        <groupId>org.projectlombok</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>cn.hutool</groupId>
                        <artifactId>hutool-all</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>javax.mail</groupId>
                        <artifactId>mail</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            
            <dependency>
                <groupId>com.howbuy.dtms</groupId>
                <artifactId>dtms-product-client</artifactId>
                <version>${com.howbuy.dtms-product-client.version}</version>
            </dependency>

            <dependency>
                <groupId>com.howbuy.dtms</groupId>
                <artifactId>dtms-order-client</artifactId>
                <version>${com.howbuy.dtms-order-client.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <distributionManagement>
        <repository>
            <id>howbuy-release</id>
            <name>howbuy-release</name>
            <url>http://nx-java.howbuy.pa/repository/howbuy-release/</url>
        </repository>
        <snapshotRepository>
            <id>howbuy-snapshot</id>
            <name>howbuy-snapshot</name>
            <url>http://nx-java.howbuy.pa/repository/howbuy-snapshot/</url>
        </snapshotRepository>
    </distributionManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
        </plugins>
    </build>

    </project>