package com.howbuy.cgi.trade.simu.service.task;

import com.howbuy.cgi.trade.simu.model.dto.QueryBalanceContext;
import com.howbuy.simu.dto.business.product.SimuZcpzJjInfo;
import com.howbuy.simu.service.base.product.SmZcpzService;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description:查询产品策略
 * @Author: yun.lu
 * Date: 2025/8/4 11:06
 */
public class QueryStrategyTask extends HowbuyBaseTask {
    private List<String> fundCodeList;
    private SmZcpzService smZcpzService;
    private QueryBalanceContext queryBalanceContext;

    @Override
    protected void callTask() {
        List<SimuZcpzJjInfo> infoList = smZcpzService.getSimuZcpzJjInfoList(fundCodeList);
        Map<String, SimuZcpzJjInfo> strategyMap = infoList.stream().collect(Collectors.toMap(SimuZcpzJjInfo::getJjdm, x -> x));
        queryBalanceContext.setStrategyMap(strategyMap);
    }

    public List<String> getFundCodeList() {
        return fundCodeList;
    }

    public void setFundCodeList(List<String> fundCodeList) {
        this.fundCodeList = fundCodeList;
    }

    public SmZcpzService getSmZcpzService() {
        return smZcpzService;
    }

    public void setSmZcpzService(SmZcpzService smZcpzService) {
        this.smZcpzService = smZcpzService;
    }

    public QueryBalanceContext getQueryBalanceContext() {
        return queryBalanceContext;
    }

    public void setQueryBalanceContext(QueryBalanceContext queryBalanceContext) {
        this.queryBalanceContext = queryBalanceContext;
    }

    public QueryStrategyTask(List<String> fundCodeList, SmZcpzService smZcpzService, QueryBalanceContext queryBalanceContext) {
        this.fundCodeList = fundCodeList;
        this.smZcpzService = smZcpzService;
        this.queryBalanceContext = queryBalanceContext;
    }
}
