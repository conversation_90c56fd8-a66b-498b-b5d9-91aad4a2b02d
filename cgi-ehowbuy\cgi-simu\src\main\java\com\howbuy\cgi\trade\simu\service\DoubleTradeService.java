package com.howbuy.cgi.trade.simu.service;

import com.alibaba.fastjson.JSON;
import com.howbuy.cgi.common.enums.BizErrorEnum;
import com.howbuy.cgi.common.exception.BizException;
import com.howbuy.cgi.common.util.JacksonUtil;
import com.howbuy.cgi.trade.simu.common.enums.DoubleTradeButtonStatusEnum;
import com.howbuy.cgi.trade.simu.common.enums.DoubleTradeGroupTypeEnum;
import com.howbuy.cgi.trade.simu.common.enums.DoubleTradeStatusEnum;
import com.howbuy.cgi.trade.simu.common.enums.DoubleTradeTypeEnum;
import com.howbuy.cgi.trade.simu.model.cmd.*;
import com.howbuy.cgi.trade.simu.model.vo.CommonResult;
import com.howbuy.cgi.trade.simu.model.vo.DoubleTradeInfoVo;
import com.howbuy.cgi.trade.simu.model.vo.DoubleTradeResultVo;
import com.howbuy.cgi.trade.simu.model.vo.HasDoubleFlagVo;
import com.howbuy.crm.doubletrade.dto.DoubleTradeForPlatDomain;
import com.howbuy.crm.doubletrade.request.*;
import com.howbuy.crm.doubletrade.response.*;
import com.howbuy.crm.doubletrade.service.*;
import com.howbuy.interlayer.product.model.*;
import com.howbuy.interlayer.product.service.HighProductService;
import com.howbuy.interlayer.product.service.TradeDayService;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.enums.busi.BusinessCodeEnum;
import com.howbuy.tms.common.enums.busi.DisCodeEnum;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.common.enums.database.IsScheduledTradeEnum;
import com.howbuy.tms.common.exception.ValidateException;
import com.howbuy.tms.common.message.MessageSource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description:双录相关接口
 * @Author: yun.lu
 * Date: 2024/6/14 16:26
 */
@Service
@Slf4j
public class DoubleTradeService {
    @Autowired
    @Qualifier("simu.queryHasDoubleFlagService")
    private QueryHasDoubleFlagService queryHasDoubleFlagService;
    @Autowired
    @Qualifier("simu.createDoubleTradeVerifyTaskService")
    private CreateDoubleTradeVerifyTaskService createDoubleTradeVerifyTaskService;
    @Autowired
    @Qualifier("simu.createDoubleTradeService")
    private CreateDoubleTradeService createDoubleTradeService;
    @Autowired
    @Qualifier("simu.updateDoubleTradeTypeService")
    private UpdateDoubleTradeTypeService updateDoubleTradeTypeService;
    @Autowired
    @Qualifier("simu.updateDoubleTradeStatusService")
    private UpdateDoubleTradeStatusService updateDoubleTradeStatusService;
    @Autowired
    @Qualifier("service.crm.queryDoubleTradeListService")
    private QueryDoubleTradeListService queryDoubleTradeListService;
    @Autowired
    @Qualifier("simu.queryTxVerifyResultService")
    private QueryTxVerifyResultService queryTxVerifyResultService;
    @Autowired
    private HighProductService highProductService;
    @Autowired
    private BuyService buyService;
    @Autowired
    @Qualifier("simu.tradeDayService")
    private TradeDayService tradeDayService;
    @Autowired
    @Qualifier("simu.queryDoubleTradeResultService")
    private QueryDoubleTradeResultService queryDoubleTradeResultService;

    private static final String SUCCESS_CODE = "0000";

    /**
     * 校验是否需要双录
     *
     * @param checkNeedDoubleTradeCmd 入参
     */
    public HasDoubleFlagVo checkDoubleTradeFlag(CheckNeedDoubleTradeCmd checkNeedDoubleTradeCmd) {
        // 1.查询是否需要双录
        HasDoubleFlagVo hasDoubleFlagVo = checkNeedDoubleFlag(checkNeedDoubleTradeCmd);
        // 2.如果需要双录,还需要查询当前双录状态
        if (YesOrNoEnum.YES.getCode().equals(hasDoubleFlagVo.getDoubleFlag()) && !StringUtils.isEmpty(hasDoubleFlagVo.getTradeId())) {
            log.info("需要双录,任务id不为空,查询双录状态");
            QueryDoubleTradeResultRequest request = new QueryDoubleTradeResultRequest();
            request.setHboneNo(checkNeedDoubleTradeCmd.getHbOneNo());
            request.setPreId(checkNeedDoubleTradeCmd.getPreId());
            QueryDoubleTradeResultResponse resultResponse = queryDoubleTradeResultService.execute(request);
            if (resultResponse != null && SUCCESS_CODE.equals(resultResponse.getReturnCode())) {
                hasDoubleFlagVo.setDoubleFlag(getDoubleFlag(resultResponse.getHandleFlag()) ? YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode());
                return hasDoubleFlagVo;
            }
        }
        log.info("不需要双录,不需要查询双录状态");
        return hasDoubleFlagVo;

    }

    private boolean getDoubleFlag(String handleFlag) {
        log.info("双录状态为handleFlag={}", handleFlag);
        if (StringUtils.isEmpty(handleFlag)) {
            log.info("没有双录状态,默认需要双录");
            return true;
        }
        // 不是未双录,那就不需要双录
        return YesOrNoEnum.YES.getCode().equals(handleFlag);
    }

    /**
     * 查询是否需要双录
     *
     * @param checkNeedDoubleTradeCmd 入参
     * @return 是否需要双录
     */
    private HasDoubleFlagVo checkNeedDoubleFlag(CheckNeedDoubleTradeCmd checkNeedDoubleTradeCmd) {
        QueryHasDoubleFlagRequest queryHasDoubleFlagRequest = new QueryHasDoubleFlagRequest();
        queryHasDoubleFlagRequest.setHboneNo(checkNeedDoubleTradeCmd.getHbOneNo());
        queryHasDoubleFlagRequest.setPreId(checkNeedDoubleTradeCmd.getPreId());
        queryHasDoubleFlagRequest.setProdCode(checkNeedDoubleTradeCmd.getFundCode());
        queryHasDoubleFlagRequest.setAppamt(checkNeedDoubleTradeCmd.getNetAppAmt());
        queryHasDoubleFlagRequest.setTxChannel(checkNeedDoubleTradeCmd.getTxChannel());
        setSubmitDt(queryHasDoubleFlagRequest, checkNeedDoubleTradeCmd);
        QueryHasDoubleFlagResponse response = queryHasDoubleFlagService.execute(queryHasDoubleFlagRequest);
        HasDoubleFlagVo hasDoubleFlagVo = new HasDoubleFlagVo();
        if (response != null && SUCCESS_CODE.equals(response.getReturnCode())) {
            hasDoubleFlagVo.setDoubleFlag(response.getDoubleFlag());
            hasDoubleFlagVo.setTempId(response.getTempId());
            hasDoubleFlagVo.setTradeId(response.getTradeId());
            return hasDoubleFlagVo;
        }
        hasDoubleFlagVo.setDoubleFlag(YesOrNoEnum.NO.getCode());
        return hasDoubleFlagVo;

    }


    /**
     * 是否预约购买
     */
    private boolean isAdvanceBuy(String mBusiCode, String isScheduledTrade) {
        return BusinessCodeEnum.SUBS.getMCode().equals(mBusiCode)
                || (BusinessCodeEnum.PURCHASE.getMCode().equals(mBusiCode)
                && (IsScheduledTradeEnum.SupportBuyAdvance.getCode().equals(isScheduledTrade)
                || IsScheduledTradeEnum.SupportBuyAndRedeemAdvance.getCode().equals(isScheduledTrade)));
    }

    /**
     * 设置工作日
     */
    private void setSubmitDt(QueryHasDoubleFlagRequest queryHasDoubleFlagRequest, CheckNeedDoubleTradeCmd checkNeedDoubleTradeCmd) {
        // 查询产品基本信息
        HighProductInfoModel highProductBaseModel = buyService.getHighProductInfo(checkNeedDoubleTradeCmd.getFundCode());

        String mBusiCode = buyService.getMBusiCode(highProductBaseModel);
        // 业务类型,认购/申购
        String tradeType;
        if (BusinessCodeEnum.PURCHASE.getCode().equals(mBusiCode)) {
            tradeType = "2";
        } else {
            tradeType = "1";
        }
        queryHasDoubleFlagRequest.setTradeType(tradeType);
        String submitTaDt;
        Date now = new Date();
        if (isAdvanceBuy(mBusiCode, highProductBaseModel.getIsScheduledTrade())) {
            //查询购买预约开放日历
            HighProductAppointmentInfoModel productAppointmentInfoBean = highProductService.getAppointmentInfoByAppointDate(checkNeedDoubleTradeCmd.getFundCode(), "0",
                    highProductBaseModel.getShareClass(), DisCodeEnum.HM.getCode(), now);
            if (productAppointmentInfoBean == null) {
                log.error("支持预约,但是没有查到对应的预约日历");
                throw new ValidateException(ExceptionCodes.APPOINTMENT_INFO_IS_NULL,
                        MessageSource.getMessageByCode(ExceptionCodes.APPOINTMENT_INFO_IS_NULL));
            }
            submitTaDt = productAppointmentInfoBean.getOpenEndDt();
        } else {
            WorkDayModel workDayModel = tradeDayService.getWorkDayModel(now);
            submitTaDt = workDayModel.getWorkday();
        }
        queryHasDoubleFlagRequest.setSubmitTaDt(submitTaDt);
    }

    /**
     * 获取双录url
     *
     * @param createDoubleTradeUrlCmd 生成url入参
     * @return 双录任务id
     */
    public String createDoubleTradeUrl(CreateDoubleTradeUrlCmd createDoubleTradeUrlCmd) {
        CreateDoubleTradeVerifyTaskRequest createDoubleTradeVerifyTaskRequest = new CreateDoubleTradeVerifyTaskRequest();
        createDoubleTradeVerifyTaskRequest.setHboneNo(createDoubleTradeUrlCmd.getHbOneNo());
        createDoubleTradeVerifyTaskRequest.setTempId(createDoubleTradeUrlCmd.getTempId());
        createDoubleTradeVerifyTaskRequest.setSuccessUrl(createDoubleTradeUrlCmd.getSuccessUrl());
        createDoubleTradeVerifyTaskRequest.setFailUrl(createDoubleTradeUrlCmd.getFailUrl());
        createDoubleTradeVerifyTaskRequest.setPreId(createDoubleTradeUrlCmd.getPreId());
        createDoubleTradeVerifyTaskRequest.setProdCode(createDoubleTradeUrlCmd.getFundCode());
        createDoubleTradeVerifyTaskRequest.setAmt(createDoubleTradeUrlCmd.getAmt());
        CreateDoubleTradeVerifyTaskResponse response = createDoubleTradeVerifyTaskService.execute(createDoubleTradeVerifyTaskRequest);
        if (response != null && SUCCESS_CODE.equals(response.getReturnCode())) {
            return response.getVerifyUrl();
        }
        throw new BizException(BizErrorEnum.BUSY_ERROR.getCode(), "获取双录url异常");
    }

    /**
     * 查询双录结果
     *
     * @param bizId 流水id
     * @return 双录结果
     */
    public DoubleTradeResultVo queryDoubleTradeResult(String bizId) {
        QueryTxVerifyResultRequest queryTxVerifyResultRequest = new QueryTxVerifyResultRequest();
        queryTxVerifyResultRequest.setBizId(bizId);
        QueryTxVerifyResultResponse response = queryTxVerifyResultService.execute(queryTxVerifyResultRequest);
        DoubleTradeResultVo doubleTradeResultVo = new DoubleTradeResultVo();
        if (response != null && SUCCESS_CODE.equals(response.getReturnCode())) {
            doubleTradeResultVo.setTaskState(response.getTaskState());
            doubleTradeResultVo.setFailReason(response.getFailReason());
            return doubleTradeResultVo;
        }
        log.info("查询双录结果异常,使用默认提示,response={}", JSON.toJSONString(response));
        doubleTradeResultVo.setFailReason("暂未获取失败原因，请您尝试重新双录或转线下双录");
        return doubleTradeResultVo;
    }

    /**
     * 生成线下双录任务
     *
     * @param createOffLineDoubleTradeCmd 入参
     */
    public CommonResult createOffLineDoubleTradeTask(CreateOffLineDoubleTradeCmd createOffLineDoubleTradeCmd) {
        CreateDoubleTradeRequest createDoubleTradeRequest = new CreateDoubleTradeRequest();
        createDoubleTradeRequest.setHboneNo(createOffLineDoubleTradeCmd.getHbOneNo());
        createDoubleTradeRequest.setPreId(createOffLineDoubleTradeCmd.getPreId());
        createDoubleTradeRequest.setFundCode(createOffLineDoubleTradeCmd.getFundCode());
        CreateDoubleTradeResponse response = createDoubleTradeService.execute(createDoubleTradeRequest);
        CommonResult commonResult = new CommonResult();
        if (response != null && SUCCESS_CODE.equals(response.getReturnCode())) {
            commonResult.setIsSuccess(YesOrNoEnum.YES.getCode());
            return commonResult;
        }
        if (response != null) {
            commonResult.setDesc(response.getDescription());
        }
        commonResult.setIsSuccess(YesOrNoEnum.NO.getCode());
        return commonResult;
    }

    /**
     * 查询双录列表
     *
     * @param hboneNo 一账通
     * @return 双录列表
     */
    public List<DoubleTradeInfoVo> queryDoubleTradeListService(String hboneNo,
                                                               List<String> disCodeLists) {
        List<DoubleTradeInfoVo> doubleTradeForPlatList = new ArrayList<>();
        QueryDoubleTradeListRequest request = new QueryDoubleTradeListRequest();
        request.setHboneNo(hboneNo);
        log.info("QueryDoubleTradeListRequest:" + JacksonUtil.objToJson(request));
        QueryDoubleTradeListResponse res = this.queryDoubleTradeListService.execute(request);
        log.info("QueryDoubleTradeListResponse:" + JacksonUtil.objToJson(res));
        List<DoubleTradeForPlatDomain> doubleTrades = res.getDoubleTrades();
        if (CollectionUtils.isEmpty(doubleTrades)) {
            return doubleTradeForPlatList;
        } else {
            List<String> fundCodeList = doubleTrades.stream().map(DoubleTradeForPlatDomain::getFundCode).distinct().collect(Collectors.toList());
            List<HighProductControlModel> highProductControlList = this.highProductService.getHighProductControlList(fundCodeList);
            Map<String, String> disCodeMap = highProductControlList.stream().collect(Collectors.toMap(HighProductControlModel::getFundCode, BaseModel::getDisCode));
            for (DoubleTradeForPlatDomain domain : doubleTrades) {
                if (DoubleTradeStatusEnum.UN_EFFECT.getStatus().equals(domain.getStatus())) {
                    continue;
                }
                // 没有分销渠道筛选,展示所有
                if(CollectionUtils.isEmpty(disCodeLists)){
                    DoubleTradeInfoVo doubleTradeInfoVo = buildDoubleTradeButtonInfo(domain, disCodeMap);
                    doubleTradeForPlatList.add(doubleTradeInfoVo);
                    continue;
                }
                // 包含分销渠道筛选,只展示包含分销渠道的
                if(disCodeLists.contains(disCodeMap.get(domain.getFundCode()))){
                    DoubleTradeInfoVo doubleTradeInfoVo = buildDoubleTradeButtonInfo(domain, disCodeMap);
                    doubleTradeForPlatList.add(doubleTradeInfoVo);
                }
            }
            return doubleTradeForPlatList;
        }
    }

    /**
     * 双录实体
     *
     * @param disCodeMap 分销信息
     * @param domain     双录信息
     * @return 双录信息
     */
    private DoubleTradeInfoVo buildDoubleTradeButtonInfo(DoubleTradeForPlatDomain domain, Map<String, String> disCodeMap) {
        log.info("构建双录信息,domain={}", JSON.toJSONString(domain));
        DoubleTradeInfoVo model = new DoubleTradeInfoVo();
        model.setAmount(domain.getAmount());
        model.setTradeId(domain.getTradeId());
        model.setDeadlineDt(domain.getDeadlineDt());
        model.setFundCode(domain.getFundCode());
        model.setFundName(domain.getFundName());
        model.setStatus(domain.getStatus());
        model.setTradeId(domain.getTradeId());
        model.setUploadDt(domain.getUploadDt());
        model.setDisCode(disCodeMap.get(domain.getFundCode()));
        model.setTempId(domain.getTempId());
        model.setDoubleType(domain.getDoubleType());
        model.setPreId(domain.getPreId());
        // 1.待双录：包括【双录状态】=待处理、视频生成中、审核不通过
        List<String> waiteDoubleTradeList = new ArrayList<>();
        waiteDoubleTradeList.add(DoubleTradeStatusEnum.UN_DOUBLE_TRADE.getStatus());
        waiteDoubleTradeList.add(DoubleTradeStatusEnum.CHECK_FAIL.getStatus());
        waiteDoubleTradeList.add(DoubleTradeStatusEnum.VIDEO_CREATING.getStatus());
        if (waiteDoubleTradeList.contains(domain.getStatus())) {
            model.setDoubleTradeGroupType(DoubleTradeGroupTypeEnum.WAITE_PROCESS.getType());
            if (DoubleTradeTypeEnum.MANUAL_DOUBLE_TRADE.getType().equals(domain.getDoubleType())) {
                if (DoubleTradeStatusEnum.UN_DOUBLE_TRADE.getStatus().equals(domain.getStatus()) || DoubleTradeStatusEnum.CHECK_FAIL.getStatus().equals(domain.getStatus())) {
                    log.info("人人双录,待处理/审核不通过,隐藏按钮,提示文案");
                    model.setNoticeMsg("请等待您的专属服务人员联系您进行双录");
                    model.setButtonStatus(DoubleTradeButtonStatusEnum.UN_SHOW.getStatus());
                    return model;
                }
                log.info("人人双录,不会有视频生成中,隐藏按钮,不提示文案");
                model.setButtonStatus(DoubleTradeButtonStatusEnum.UN_SHOW.getStatus());
                return model;
            } else {
                if (DoubleTradeStatusEnum.UN_DOUBLE_TRADE.getStatus().equals(domain.getStatus()) || DoubleTradeStatusEnum.CHECK_FAIL.getStatus().equals(domain.getStatus())) {
                    if (StringUtils.isEmpty(domain.getTempId())) {
                        log.info("人机双录,待处理/审核不通过,没有匹配到模版,隐藏按钮,提示文案");
                        model.setNoticeMsg("暂不支持系统智能双录，请等待系统处理或联系您的专属服务人员咨询");
                        model.setButtonStatus(DoubleTradeButtonStatusEnum.SHOW_UN_CLICK.getStatus());
                        return model;
                    } else {
                        log.info("人机双录,待处理/审核不通过,有模版,展示按钮");
                        model.setButtonStatus(DoubleTradeButtonStatusEnum.SHOW_CAN_CLICK.getStatus());
                        return model;
                    }
                } else {
                    log.info("人机双录,视频生成中,隐藏按钮,提示文案");
                    model.setNoticeMsg("您已完成系统智能双录，请等待双录视频生成");
                    model.setButtonStatus(DoubleTradeButtonStatusEnum.UN_SHOW.getStatus());
                    return model;
                }

            }

        } else if (DoubleTradeStatusEnum.WAITE_CHECK.getStatus().equals(domain.getStatus())) {
            log.info("待审核,不展示按钮");
            model.setDoubleTradeGroupType(DoubleTradeGroupTypeEnum.WAITE_CHECK.getType());
            model.setButtonStatus(DoubleTradeButtonStatusEnum.UN_SHOW.getStatus());
            return model;
        } else if (DoubleTradeStatusEnum.HAVE_DOUBLE_TRADE.getStatus().equals(domain.getStatus())) {
            log.info("已完成,不展示按钮");
            model.setDoubleTradeGroupType(DoubleTradeGroupTypeEnum.COMPLETED.getType());
            model.setButtonStatus(DoubleTradeButtonStatusEnum.UN_SHOW.getStatus());
            return model;
        }
        log.info("不是任何场景,没有按钮,没有文案,默认放进待处理分组,domain={}", JSON.toJSONString(domain));
        model.setDoubleTradeGroupType(DoubleTradeGroupTypeEnum.WAITE_PROCESS.getType());
        model.setButtonStatus(DoubleTradeButtonStatusEnum.UN_SHOW.getStatus());
        return model;
    }

    /**
     * 更新双录方式
     *
     * @param updateDoubleTradeTypeCmd 更新双录方式入参
     * @return 结果
     */
    public CommonResult updateDoubleTradeType(UpdateDoubleTradeTypeCmd updateDoubleTradeTypeCmd) {
        UpdateDoubleTradeTypeRequest updateDoubleTradeTypeRequest = new UpdateDoubleTradeTypeRequest();
        updateDoubleTradeTypeRequest.setHboneNo(updateDoubleTradeTypeCmd.getHbOneNo());
        updateDoubleTradeTypeRequest.setTradeId(updateDoubleTradeTypeCmd.getTradeId());
        updateDoubleTradeTypeRequest.setType(updateDoubleTradeTypeCmd.getType());
        UpdateDoubleTradeTypeResponse response = updateDoubleTradeTypeService.execute(updateDoubleTradeTypeRequest);
        CommonResult commonResult = new CommonResult();
        if (response != null && SUCCESS_CODE.equals(response.getReturnCode())) {
            commonResult.setIsSuccess(YesOrNoEnum.YES.getCode());
            return commonResult;
        }
        if (response != null) {
            commonResult.setDesc(response.getDescription());
        }
        commonResult.setIsSuccess(YesOrNoEnum.NO.getCode());
        return commonResult;
    }

    /**
     * 更新双录状态
     *
     * @param updateDoubleTradeStatusCmd
     */
    public void updateDoubleTradeStatus(UpdateDoubleTradeStatusCmd updateDoubleTradeStatusCmd) {
        UpdateDoubleTradeStatusRequest updateDoubleTradeStatusRequest = new UpdateDoubleTradeStatusRequest();
        updateDoubleTradeStatusRequest.setHboneNo(updateDoubleTradeStatusCmd.getHbOneNo());
        updateDoubleTradeStatusRequest.setTradeId(updateDoubleTradeStatusCmd.getTradeId());
        updateDoubleTradeStatusRequest.setStatus(updateDoubleTradeStatusCmd.getStatus());
        updateDoubleTradeStatusService.execute(updateDoubleTradeStatusRequest);
    }
}
