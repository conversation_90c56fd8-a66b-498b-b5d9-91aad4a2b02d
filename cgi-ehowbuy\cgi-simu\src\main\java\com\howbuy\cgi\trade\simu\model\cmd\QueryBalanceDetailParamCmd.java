package com.howbuy.cgi.trade.simu.model.cmd;

import com.howbuy.cgi.trade.simu.model.dto.AcctDataAuthDto;
import com.howbuy.cgi.trade.simu.model.dto.BaseDto;
import lombok.Data;

/**
 * @description: 查询持仓详情参数命令对象
 * @author: hongdong.xie
 * @date: 2025/9/8 14:30
 * @since JDK 1.8
 */
@Data
public class QueryBalanceDetailParamCmd extends BaseDto {

    /**
     * 基金代码
     */
    private String fundCode;

    /**
     * 子账号id
     */
    private String subAccountId;

    /**
     * 一账通号
     */
    private String hboneNo;

    /**
     * 当前ip
     */
    private String ip;

    /**
     * 交易账号
     */
    private String txAcctNo;

    /**
     * 用户名
     */
    private String custName;

    /**
     * 关联客户一账通,注意不是子账号
     */
    private String relatedHboneNo;

    /**
     * 分销渠道
     */
    private String disCode;

    /**
     * 不过滤香港产品,1:是,0:否
     */
    private String notFilterHkFund;

    /**
     * 不过滤好臻产品,1:是,0:否
     */
    private String notFilterHzFund;

    /**
     * 是否数据授权
     */
    private AcctDataAuthDto acctDataAuthInfo;
}