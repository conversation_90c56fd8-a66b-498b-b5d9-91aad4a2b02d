<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>com.howbuy.cgi</groupId>
		<artifactId>cgi-service</artifactId>
		<version>4.9.16-RELEASE</version>
	</parent>

	<name>service-kyc</name>
	<artifactId>service-kyc</artifactId>
	<version>4.9.16-RELEASE</version>

	<dependencies>
		
		<dependency>
			<groupId>com.howbuy.cgi</groupId>
			<artifactId>service-common</artifactId>
		</dependency>

		<dependency>
			<groupId>com.howbuy.acccenter</groupId>
			<artifactId>acc-center-facade</artifactId>
		</dependency>

		<dependency>
			<groupId>com.howbuy.acccenter</groupId>
			<artifactId>acc-center-common</artifactId>
		</dependency>
		
		<dependency>
			<groupId>com.howbuy.acc</groupId>
			<artifactId>acc-common-utils</artifactId>
		</dependency>
		
		<dependency>
			<groupId>com.howbuy.cc</groupId>
			<artifactId>center-client</artifactId>
		</dependency>
		
		<dependency>
			<groupId>com.howbuy.cgi</groupId>
			<artifactId>cgi-common</artifactId>
		</dependency>
		
		<dependency>
			<groupId>javax.servlet</groupId>
			<artifactId>javax.servlet-api</artifactId>
			<scope>provided</scope>
		</dependency>

		<!-- spring framework -->
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-core</artifactId>
		</dependency>
		
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-beans</artifactId>
		</dependency>
		
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-aop</artifactId>
		</dependency>
		
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-context</artifactId>
		</dependency>
		
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-context-support</artifactId>
		</dependency>
		
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-test</artifactId>
		</dependency>
		
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-oxm</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-web</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-webmvc</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-orm</artifactId>
		</dependency>
		
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-jdbc</artifactId>
		</dependency>
		
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-tx</artifactId>
		</dependency>
		
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-aspects</artifactId>
		</dependency>
		
		<dependency>
			<groupId>net.unicon.springframework</groupId>
			<artifactId>springframework-addons</artifactId>
		</dependency>

	</dependencies>

</project>