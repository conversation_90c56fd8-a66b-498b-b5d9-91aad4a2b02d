package com.howbuy.cgi.trade.simu.service.task;

import com.howbuy.cgi.trade.simu.model.cmd.QueryBalanceDetailOtherInfoCmd;
import com.howbuy.cgi.trade.simu.model.dto.QueryBalanceDetailContext;
import com.howbuy.cgi.trade.simu.model.dto.QueryBalanceDetailOtherInfoVo;
import com.howbuy.cgi.trade.simu.service.QueryAcctOtherInfoService;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @Description:查询持仓补偿信息
 * @Author: yun.lu
 * Date: 2025/9/10 19:20
 */
@Getter
@Setter
public class QueryOtherBalanceInfoTask extends HowbuyBaseTask {
    private QueryAcctOtherInfoService queryAcctOtherInfoService;
    private QueryBalanceDetailContext queryBalanceDetailContext;
    private String fundCode;
    private String disCode;
    private String hbOneNo;
    private String ip;
    private List<String> subFundCodeList;

    @Override
    protected void callTask() {
        QueryBalanceDetailOtherInfoCmd queryBalanceDetailOtherInfoCmd = new QueryBalanceDetailOtherInfoCmd();
        queryBalanceDetailOtherInfoCmd.setFundCode(fundCode);
        queryBalanceDetailOtherInfoCmd.setDisCode(disCode);
        queryBalanceDetailOtherInfoCmd.setHboneNo(hbOneNo);
        queryBalanceDetailOtherInfoCmd.setIp(ip);
        queryBalanceDetailOtherInfoCmd.setSubFundCodeList(subFundCodeList);
        QueryBalanceDetailOtherInfoVo queryBalanceDetailOtherInfoVo = queryAcctOtherInfoService.getOtherBalanceDetail(queryBalanceDetailOtherInfoCmd);
        queryBalanceDetailContext.setQueryBalanceDetailOtherInfoVo(queryBalanceDetailOtherInfoVo);
    }
}
