package com.howbuy.cgi.trade.simu.service.task;

import com.howbuy.cgi.common.remote.param.RemoteParametersProvider;
import com.howbuy.cgi.trade.simu.model.dto.QueryBalanceContext;
import com.howbuy.cgi.trade.simu.model.dto.QueryBalanceDetailContext;
import com.howbuy.cgi.trade.simu.service.QueryBalanceVolService;
import com.howbuy.interlayer.common.Constants;
import com.howbuy.tms.high.orders.facade.search.queryredeemfundstatus.QueryRedeemFundStatusFacade;
import com.howbuy.tms.high.orders.facade.search.queryredeemfundstatus.QueryRedeemFundStatusRequest;
import com.howbuy.tms.high.orders.facade.search.queryredeemfundstatus.QueryRedeemFundStatusResponse;
import org.apache.commons.collections.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description:查询赎回状态
 * @Author: yun.lu
 * Date: 2025/8/4 10:34
 */
public class RdmFundStatusTask extends HowbuyBaseTask {
    private List<String> productCodeList;
    private QueryBalanceVolService queryBalanceVolService;
    private QueryRedeemFundStatusFacade queryRedeemFundStatusFacade;
    private QueryBalanceContext queryBalanceContext;
    private String custIP;
    private String custNo;

    @Override
    protected void callTask() {
        QueryRedeemFundStatusRequest request = new QueryRedeemFundStatusRequest();
        request.setProductCodeList(productCodeList);
        // 获取好买分销代码列表
        List<String> disCodeList = queryBalanceVolService.getDisCodeList();
        request.setDisCodeList(disCodeList);
        request.setOutletCode(Constants.OUTLET_CODE_HOWBUY);
        request.setOperIp(custIP);
        request.setTxAcctNo(custNo);
        request.setTxChannel(RemoteParametersProvider.getTradeChannel());
        QueryRedeemFundStatusResponse response = queryRedeemFundStatusFacade.execute(request);
        Map<String, QueryRedeemFundStatusResponse.RedeemFundStatusBean> redeemFundStatusMap = new HashMap<>();
        if (response != null && CollectionUtils.isNotEmpty(response.getRedeemFundStatusList())) {
            for (QueryRedeemFundStatusResponse.RedeemFundStatusBean bean : response.getRedeemFundStatusList()) {
                redeemFundStatusMap.put(bean.getProductCode(), bean);
            }
        }
        queryBalanceContext.setRedeemFundStatusMap(redeemFundStatusMap);
    }

    public List<String> getProductCodeList() {
        return productCodeList;
    }

    public void setProductCodeList(List<String> productCodeList) {
        this.productCodeList = productCodeList;
    }

    public QueryBalanceVolService getQueryBalanceVolService() {
        return queryBalanceVolService;
    }

    public void setQueryBalanceVolService(QueryBalanceVolService queryBalanceVolService) {
        this.queryBalanceVolService = queryBalanceVolService;
    }

    public QueryRedeemFundStatusFacade getQueryRedeemFundStatusFacade() {
        return queryRedeemFundStatusFacade;
    }

    public void setQueryRedeemFundStatusFacade(QueryRedeemFundStatusFacade queryRedeemFundStatusFacade) {
        this.queryRedeemFundStatusFacade = queryRedeemFundStatusFacade;
    }

    public QueryBalanceContext getQueryBalanceContext() {
        return queryBalanceContext;
    }

    public void setQueryBalanceContext(QueryBalanceContext queryBalanceContext) {
        this.queryBalanceContext = queryBalanceContext;
    }

    public String getCustIP() {
        return custIP;
    }

    public void setCustIP(String custIP) {
        this.custIP = custIP;
    }

    public String getCustNo() {
        return custNo;
    }

    public void setCustNo(String custNo) {
        this.custNo = custNo;
    }

    public RdmFundStatusTask(List<String> productCodeList, QueryBalanceVolService queryBalanceVolService, QueryRedeemFundStatusFacade queryRedeemFundStatusFacade, QueryBalanceContext queryBalanceContext, String custIP, String custNo) {
        this.productCodeList = productCodeList;
        this.queryBalanceVolService = queryBalanceVolService;
        this.queryRedeemFundStatusFacade = queryRedeemFundStatusFacade;
        this.queryBalanceContext = queryBalanceContext;
        this.custIP = custIP;
        this.custNo = custNo;
    }
}
