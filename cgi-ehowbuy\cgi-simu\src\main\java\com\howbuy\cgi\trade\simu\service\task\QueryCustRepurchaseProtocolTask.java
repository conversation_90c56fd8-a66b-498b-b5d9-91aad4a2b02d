package com.howbuy.cgi.trade.simu.service.task;

import com.howbuy.cgi.trade.simu.model.dto.CustRepurchaseProtocolDto;
import com.howbuy.cgi.trade.simu.model.dto.QueryBalanceContext;
import com.howbuy.common.bean.BeanUtils;
import com.howbuy.tms.high.orders.facade.search.queryrepurchaseprotocol.QueryCustRepurchaseProtocolFacade;
import com.howbuy.tms.high.orders.facade.search.queryrepurchaseprotocol.QueryCustRepurchaseProtocolRequest;
import com.howbuy.tms.high.orders.facade.search.queryrepurchaseprotocol.QueryCustRepurchaseProtocolResposne;
import com.howbuy.tms.high.orders.facade.search.queryrepurchaseprotocol.bean.CustRepurchaseProtocolBean;
import org.apache.commons.collections.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description:购买协议
 * @Author: yun.lu
 * Date: 2025/8/4 10:43
 */
public class QueryCustRepurchaseProtocolTask extends HowbuyBaseTask {
    private static final Logger log = LogManager.getLogger(QueryCustRepurchaseProtocolTask.class);

    private String txAcctNo;
    private List<String> fundCodes;
    private QueryCustRepurchaseProtocolFacade queryCustRepurchaseProtocolFacade;
    private QueryBalanceContext queryBalanceContext;

    @Override
    protected void callTask() {
        Map<String, CustRepurchaseProtocolDto> custRepurchaseProtocolMap = new HashMap<>();
        try {
            QueryCustRepurchaseProtocolRequest request = new QueryCustRepurchaseProtocolRequest();
            request.setTxAcctNo(txAcctNo);
            request.setFundCodes(fundCodes);
            QueryCustRepurchaseProtocolResposne resp = queryCustRepurchaseProtocolFacade.execute(request);
            if (CollectionUtils.isNotEmpty(resp.getCustRepurchaseProtocolList())) {
                CustRepurchaseProtocolDto custRepurchaseProtocolDto = null;
                for (CustRepurchaseProtocolBean custRepurchaseProtocolBean : resp.getCustRepurchaseProtocolList()) {
                    custRepurchaseProtocolDto = new CustRepurchaseProtocolDto();
                    BeanUtils.copyProperties(custRepurchaseProtocolBean, custRepurchaseProtocolDto);
                    custRepurchaseProtocolMap.put(custRepurchaseProtocolBean.getFundCode(), custRepurchaseProtocolDto);
                }
            }
        } catch (Exception e) {
            log.error("QueryCustRepurchaseProtocolTask-queryCustRepurchaseProtocol-查询购买协议error:", e);
        }

        queryBalanceContext.setCustRepurchaseProtocolMap(custRepurchaseProtocolMap);
    }

    public QueryCustRepurchaseProtocolTask(String txAcctNo, List<String> fundCodes, QueryCustRepurchaseProtocolFacade queryCustRepurchaseProtocolFacade, QueryBalanceContext queryBalanceContext) {
        this.txAcctNo = txAcctNo;
        this.fundCodes = fundCodes;
        this.queryCustRepurchaseProtocolFacade = queryCustRepurchaseProtocolFacade;
        this.queryBalanceContext = queryBalanceContext;
    }

    public String getTxAcctNo() {
        return txAcctNo;
    }

    public void setTxAcctNo(String txAcctNo) {
        this.txAcctNo = txAcctNo;
    }

    public List<String> getFundCodes() {
        return fundCodes;
    }

    public void setFundCodes(List<String> fundCodes) {
        this.fundCodes = fundCodes;
    }

    public QueryCustRepurchaseProtocolFacade getQueryCustRepurchaseProtocolFacade() {
        return queryCustRepurchaseProtocolFacade;
    }

    public void setQueryCustRepurchaseProtocolFacade(QueryCustRepurchaseProtocolFacade queryCustRepurchaseProtocolFacade) {
        this.queryCustRepurchaseProtocolFacade = queryCustRepurchaseProtocolFacade;
    }

    public QueryBalanceContext getQueryBalanceContext() {
        return queryBalanceContext;
    }

    public void setQueryBalanceContext(QueryBalanceContext queryBalanceContext) {
        this.queryBalanceContext = queryBalanceContext;
    }
}
