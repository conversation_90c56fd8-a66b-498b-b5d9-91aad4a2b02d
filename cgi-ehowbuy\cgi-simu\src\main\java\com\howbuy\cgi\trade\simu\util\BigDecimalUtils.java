/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.cgi.trade.simu.util;

import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2025/9/4 17:17
 * @since JDK 1.8
 */
public class BigDecimalUtils {

    /**
     * @description: bigdecimal 进度处理，不足的用0补充
     * @param numStr
     * @param precision
     * @param roundingMode
     * @return java.lang.String
     * @author: jinqing.rao
     * @date: 2024/6/6 13:56
     * @since JDK 1.8
     */
    public static String formatToThousandths(BigDecimal numStr, Integer precision, RoundingMode roundingMode){
        if(null == numStr){
            return null;
        }
        // 默认两位
        if(null == precision){
            precision = 2;
        }
        // 如果是0 直接返回 0 加小数位
        if(numStr.compareTo(BigDecimal.ZERO) == 0){
            return "0." + StringUtils.repeat("0", precision);
        }
        BigDecimal roundedValue = numStr.setScale(precision, roundingMode);
        StringBuilder patternBuilder = new StringBuilder("#,###.");
        // 已0.开头的需要特殊处理,不然输出结果小数点前没数字, 例如 .0
        if(numStr.toString().startsWith("0.")){
            patternBuilder = new StringBuilder("0.");
        }
        for (int i = 0; i < precision; i++) {
            patternBuilder.append('0');
        }
        // 创建DecimalFormat实例，使用"."和","作为小数点和千分位分隔符
        DecimalFormat df = new DecimalFormat(patternBuilder.toString());
        df.setGroupingUsed(true); // 开启千分位分隔
        // 格式化四舍五入后的BigDecimal
        return df.format(roundedValue);
    }
    
    /**
     * @description: 格式化BigDecimal成字符串
     * @param value 值
     * @param scale 小数位
     * @param roundingMode 截取类型
     * @return java.lang.String
     * @author: lingma
     * @date: 2025/9/9 15:40
     * @since JDK 1.8
     */
    public static String formatToString(BigDecimal value, int scale, RoundingMode roundingMode) {
        if (null == value) {
            return null;
        }
        return value.setScale(scale, roundingMode).toString();
    }

}
