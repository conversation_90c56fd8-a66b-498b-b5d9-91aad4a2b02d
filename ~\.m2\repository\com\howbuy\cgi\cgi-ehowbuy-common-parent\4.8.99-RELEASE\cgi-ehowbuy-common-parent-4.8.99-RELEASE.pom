<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<groupId>com.howbuy.cgi</groupId>
	<artifactId>cgi-ehowbuy-common-parent</artifactId>
	<version>4.8.99-RELEASE</version>
	<packaging>pom</packaging>

	<properties>
		<skipTests>false</skipTests>
		
		<java.version>1.8</java.version>
		<project.encoding>UTF-8</project.encoding>
		
		<spring.version>5.2.16.RELEASE</spring.version>
		<jackson2.version>2.11.4</jackson2.version>
		<log4j.version>2.17.0</log4j.version>
		<howbuy.client.version>1.0.0-release</howbuy.client.version>
		<howbuy.middleware.version>1.0.0-release</howbuy.middleware.version>
		<UserAgentUtils.version>1.21</UserAgentUtils.version>

		<dubbo.version>2.7.15</dubbo.version>
		<alibaba.cloud.nacos.version>2.2.0.RELEASE</alibaba.cloud.nacos.version>
		<spring-cloud.version>Hoxton.SR12</spring-cloud.version>
		<howbuy.actuator.version>1.1.4-RELEASE</howbuy.actuator.version>

		<com.howbuy.howbuy-ccms-watcher.version>6.0.1-RELEASE</com.howbuy.howbuy-ccms-watcher.version>
		<com.howbuy.howbuy-dfile-service.version>1.18.1-RELEASE</com.howbuy.howbuy-dfile-service.version>
        <com.howbuy.howbuy-session.version>1.0.1-RELEASE</com.howbuy.howbuy-session.version>

		<com.howbuy.tms-common-log-pattern.version>1.0.0-RELEASE</com.howbuy.tms-common-log-pattern.version>

		<com.howbuy.robot-order-center-client.version>4.8.72-RELEASE</com.howbuy.robot-order-center-client.version>
		<com.howbuy.order-center-client.version>4.8.72-RELEASE</com.howbuy.order-center-client.version>
		<com.howbuy.batch-center-client.version>4.8.72-RELEASE</com.howbuy.batch-center-client.version>
		<com.howbuy.elasticsearch-center-client.version>4.8.72-RELEASE</com.howbuy.elasticsearch-center-client.version>
		<com.howbuy.gateway-bxebank-client.version>********-001-RELEASE</com.howbuy.gateway-bxebank-client.version>
		<com.howbuy.gateway-captcha-client.version>********-001-RELEASE</com.howbuy.gateway-captcha-client.version>
		<com.howbuy.csdc-pre-client.version>1.0.0-release</com.howbuy.csdc-pre-client.version>
		<com.howbuy.regular-order-center-client.version>1.0.0-release</com.howbuy.regular-order-center-client.version>
		<com.howbuy.high-order-center-client.version>4.8.88-RELEASE</com.howbuy.high-order-center-client.version>
		<com.howbuy.high-batch-center-client.version>********-001-RELEASE</com.howbuy.high-batch-center-client.version>

		<com.howbuy.fin-online-facade.version>********-RELEASE</com.howbuy.fin-online-facade.version>
		<com.howbuy.ftx-order-facade.version>2.1.1-RELEASE</com.howbuy.ftx-order-facade.version>
		<com.howbuy.fbs-online-facade.version>3.40.5-RELEASE</com.howbuy.fbs-online-facade.version>
		<com.howbuy.fbs-online-search-facade.version>3.40.5-RELEASE</com.howbuy.fbs-online-search-facade.version>
		<com.howbuy.fbs-common-facade.version>3.17.0-RELEASE</com.howbuy.fbs-common-facade.version>

		
		<com.howbuy.acc-center-facade.version>3.6.6-RELEASE</com.howbuy.acc-center-facade.version>
		<com.howbuy.acc-center-common.version>3.6.6-RELEASE</com.howbuy.acc-center-common.version>
		<com.howbuy.acc-common-utils.version>3.5.9-RELEASE</com.howbuy.acc-common-utils.version>
		<com.howbuy.hk-acc-online-facade.version>20250808-RELEASE</com.howbuy.hk-acc-online-facade.version>

		<com.howbuy.pay-common-model.version>2.5.8-RELEASE</com.howbuy.pay-common-model.version>
		<com.howbuy.pay-online-facade.version>********-RELEASE</com.howbuy.pay-online-facade.version>

		<com.howbuy.pdc-online-facade.version>3.30.0-RELEASE</com.howbuy.pdc-online-facade.version>
		<com.howbuy.common-facade.version>3.5.7-RELEASE</com.howbuy.common-facade.version>

		<com.howbuy.howbuy-simu-client.version>release-20250717-oldtable-offline-RELEASE</com.howbuy.howbuy-simu-client.version>
		<com.howbuy.howbuy-web-client.version>release-app-8.3.6-v3-RELEASE</com.howbuy.howbuy-web-client.version>
		<com.howbuy.howbuy-fund-client.version>release-20250811-4019-new-RELEASE</com.howbuy.howbuy-fund-client.version>
		<com.howbuy.crm-td-client.version>1.9.6.5-RELEASE</com.howbuy.crm-td-client.version>
		<com.howbuy.crm-nt-client.version>1.9.6.5-RELEASE</com.howbuy.crm-nt-client.version>
		<com.howbuy.howbuy-act-client.version>4.1.1-RELEASE</com.howbuy.howbuy-act-client.version>
		<com.howbuy.howbuy-cms-client.version>release-20250805-khhx-RELEASE</com.howbuy.howbuy-cms-client.version>
		<com.howbuy.center-client.version>6.4.10-RELEASE</com.howbuy.center-client.version>
		<com.howbuy.message-public-client.version>5.1.16-RELEASE</com.howbuy.message-public-client.version>
		<com.howbuy.crm-core-client.version>bugfix-20250807-RELEASE</com.howbuy.crm-core-client.version>

		<com.howbuy.howbuy-weixin-client.version>1.2.2-RELEASE</com.howbuy.howbuy-weixin-client.version>
		<howbuy.hbone-facade.version>2.5.0</howbuy.hbone-facade.version>
		<com.howbuy.common-service.version>3.5.7-RELEASE</com.howbuy.common-service.version>
		<com.howbuy.howbuy-auth-common.version>2.1.6-RELEASE</com.howbuy.howbuy-auth-common.version>
		<com.howbuy.es-web-facade.version>20250812-RELEASE</com.howbuy.es-web-facade.version>
		<com.howbuy.howbuy-interceptor.version>0.0.1-SNAPSHOT</com.howbuy.howbuy-interceptor.version>

		<com.howbuy.tms-common.version>4.8.72-RELEASE</com.howbuy.tms-common.version>
		<com.howbuy.tms-common-enums.version>4.8.72-RELEASE</com.howbuy.tms-common-enums.version>
		<com.howbuy.tms-common-lang.version>4.8.72-RELEASE</com.howbuy.tms-common-lang.version>
		<com.howbuy.tms-common-client.version>4.8.72-RELEASE</com.howbuy.tms-common-client.version>
		<com.howbuy.tms-common-service.version>4.8.72-RELEASE</com.howbuy.tms-common-service.version>
		<com.howbuy.tms-common-message-service.version>4.8.72-RELEASE</com.howbuy.tms-common-message-service.version>

		<com.howbuy.howbuy-persistence.version>release-20250425-hw2.9-RELEASE</com.howbuy.howbuy-persistence.version>

		<com.howbuy.howbuy-cachemanagement.version>3.5.1-RELEASE</com.howbuy.howbuy-cachemanagement.version>

		<com.howbuy.product-center-model.version>4.8.72-RELEASE</com.howbuy.product-center-model.version>
		<com.howbuy.product-center-client.version>4.8.72-RELEASE</com.howbuy.product-center-client.version>
        <com.howbuy.param-server-facade.version>3.41.0-RELEASE</com.howbuy.param-server-facade.version>
		<com.howbuy.howbuy-auth-facade.version>2.0.9-RELEASE</com.howbuy.howbuy-auth-facade.version>

		<com.howbuy.pension-order-client.version>4.8.57-RELEASE</com.howbuy.pension-order-client.version>

		<com.howbuy.gateway-taxbank-client.version>********-001-RELEASE</com.howbuy.gateway-taxbank-client.version>

		<org.javassist.version>3.23.1-GA</org.javassist.version>
		<com.howbuy.mockito-core.version>2.23.4</com.howbuy.mockito-core.version>
		<com.howbuy.powermock-module-junit4.version>2.0.2</com.howbuy.powermock-module-junit4.version>
		<com.howbuy.powermock-api-mockito2.version>2.0.2</com.howbuy.powermock-api-mockito2.version>
		<com.howbuy.org.jacoco.agent.version>0.8.5</com.howbuy.org.jacoco.agent.version>


    	<com.howbuy.elasticsearch-center.version>4.8.25-RELEASE</com.howbuy.elasticsearch-center.version>
		<com.howbuy.howbuy-member-client.version>app-8.3.0-v3-RELEASE</com.howbuy.howbuy-member-client.version>
		<com.howbuy.activity-center-client.version>4.8.99-RELEASE</com.howbuy.activity-center-client.version>
        <com.howbuy.cgi-ehowbuy-common-parent.version>4.8.99-RELEASE</com.howbuy.cgi-ehowbuy-common-parent.version>
        <com.howbuy.cgi-service.version>4.8.99-RELEASE</com.howbuy.cgi-service.version>
    </properties>

	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>com.howbuy</groupId>
				<artifactId>gateway-taxbank-client</artifactId>
				<version>${com.howbuy.gateway-taxbank-client.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy.cgi</groupId>
				<artifactId>cgi-common</artifactId>
				<version>${com.howbuy.cgi-ehowbuy-common-parent.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy.cgi</groupId>
				<artifactId>service-common</artifactId>
				<version>${com.howbuy.cgi-service.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy.cgi</groupId>
				<artifactId>service-account</artifactId>
				<version>${com.howbuy.cgi-service.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy.cgi</groupId>
				<artifactId>service-asset</artifactId>
				<version>${com.howbuy.cgi-service.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy.cgi</groupId>
				<artifactId>service-dc</artifactId>
				<version>${com.howbuy.cgi-service.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy.cgi</groupId>
				<artifactId>service-fincenter</artifactId>
				<version>${com.howbuy.cgi-service.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy.cgi</groupId>
				<artifactId>service-kyc</artifactId>
				<version>${com.howbuy.cgi-service.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy.cgi</groupId>
				<artifactId>service-piggy</artifactId>
				<version>${com.howbuy.cgi-service.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy.cgi</groupId>
				<artifactId>service-regular</artifactId>
				<version>${com.howbuy.cgi-service.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy.cgi</groupId>
				<artifactId>service-robot</artifactId>
				<version>${com.howbuy.cgi-service.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy.cgi</groupId>
				<artifactId>service-trade</artifactId>
				<version>${com.howbuy.cgi-service.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy</groupId>
				<artifactId>howbuy-wireless-entity</artifactId>
				<version>${howbuy.client.version}</version>
			</dependency>
			<dependency>
				<groupId>com.google.guava</groupId>
				<artifactId>guava</artifactId>
				<version>15.0-rc1</version>
			</dependency>
			<dependency>
				<groupId>com.googlecode.protobuf-java-format</groupId>
				<artifactId>protobuf-java-format</artifactId>
				<version>1.2</version>
			</dependency>
			<dependency>
				<groupId>commons-codec</groupId>
				<artifactId>commons-codec</artifactId>
				<version>1.9</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy.tms</groupId>
				<artifactId>fin-center-client</artifactId>
				<version>${howbuy.middleware.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy.tms</groupId>
				<artifactId>activity-center-client</artifactId>
				<version>${com.howbuy.activity-center-client.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy</groupId>
				<artifactId>howbuy-persistence</artifactId>
				<version>${com.howbuy.howbuy-persistence.version}</version>
				<exclusions>
					<exclusion>
						<artifactId>log4j</artifactId>
						<groupId>log4j</groupId>
					</exclusion>
					<exclusion>
						<artifactId>slf4j-log4j12</artifactId>
						<groupId>org.slf4j</groupId>
					</exclusion>
					<exclusion>
						<artifactId>slf4j-api</artifactId>
						<groupId>org.slf4j</groupId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>org.mybatis</groupId>
				<artifactId>mybatis</artifactId>
				<version>3.5.6</version>
			</dependency>
			<dependency>
				<groupId>commons-pool</groupId>
				<artifactId>commons-pool</artifactId>
				<version>1.3</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy.crm</groupId>
				<artifactId>crm-core-client</artifactId>
				<version>${com.howbuy.crm-core-client.version}</version>
				<exclusions>
					<exclusion>
						<groupId>org.projectlombok</groupId>
						<artifactId>lombok</artifactId>
					</exclusion>
				</exclusions>
			</dependency>

			<dependency>
			    <groupId>com.howbuy.dfile</groupId>
			    <artifactId>howbuy-dfile-service</artifactId>
			    <version>${com.howbuy.howbuy-dfile-service.version}</version>
				<exclusions>
					<exclusion>
						<artifactId>logback-classic</artifactId>
						<groupId>ch.qos.logback</groupId>
					</exclusion>
				</exclusions>
			</dependency>

			<dependency>
			    <groupId>com.howbuy.dfile</groupId>
			    <artifactId>howbuy-dfile-impl-nfs</artifactId>
			    <version>${com.howbuy.howbuy-dfile-service.version}</version>
			</dependency>

			<dependency>
			    <groupId>com.howbuy.dfile</groupId>
			    <artifactId>howbuy-dfile-impl-webdav</artifactId>
			    <version>${com.howbuy.howbuy-dfile-service.version}</version>
			</dependency>

			<dependency>
				<groupId>com.howbuy.hkacconline</groupId>
				<artifactId>hk-acc-online-facade</artifactId>
				<version>${com.howbuy.hk-acc-online-facade.version}</version>
			</dependency>

			<dependency>
			    <groupId>eu.bitwalker</groupId>
				<artifactId>UserAgentUtils</artifactId>
				<version>${UserAgentUtils.version}</version>
			</dependency>

			<dependency>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot</artifactId>
				<version>2.3.12.RELEASE</version>
			</dependency>
			<dependency>
				<groupId>org.javassist</groupId>
			  	<artifactId>javassist</artifactId>
			    <version>${org.javassist.version}</version>
			</dependency>

			<dependency>
				<groupId>com.howbuy.tms</groupId>
			    <artifactId>tms-common-log-pattern</artifactId>
			    <version>${com.howbuy.tms-common-log-pattern.version}</version>
			</dependency>

			<dependency>
				<groupId>com.howbuy</groupId>
			    <artifactId>howbuy-ccms-watcher</artifactId>
			    <version>${com.howbuy.howbuy-ccms-watcher.version}</version>
			</dependency>


			<dependency>
				<groupId>com.alibaba.cloud</groupId>
				<artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
				<version>${alibaba.cloud.nacos.version}</version>
			</dependency>
			<dependency>
				<groupId>com.alibaba.cloud</groupId>
				<artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
				<version>${alibaba.cloud.nacos.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy.cgi</groupId>
				<artifactId>cgi-service</artifactId>
				<version>${com.howbuy.cgi-service.version}</version>
			</dependency>

			<dependency>
	            <groupId>com.howbuy</groupId>
	            <artifactId>param-server-facade</artifactId>
	            <version>${com.howbuy.param-server-facade.version}</version>
            </dependency>

			
			<dependency>
				<groupId>com.howbuy.tms</groupId>
				<artifactId>tms-common-enums</artifactId>
				<version>${com.howbuy.tms-common-enums.version}</version>
			</dependency>

			<dependency>
				<groupId>com.howbuy.tms</groupId>
				<artifactId>tms-common-lang</artifactId>
				<version>${com.howbuy.tms-common-lang.version}</version>
			</dependency>

			<dependency>
				<groupId>com.howbuy.tms</groupId>
				<artifactId>tms-common-client</artifactId>
				<version>${com.howbuy.tms-common-client.version}</version>
			</dependency>

			<dependency>
				<groupId>com.howbuy.tms</groupId>
				<artifactId>tms-common-service</artifactId>
				<version>${com.howbuy.tms-common-service.version}</version>
				<exclusions>
					<exclusion>
						<groupId>com.howbuy</groupId>
						<artifactId>howbuy-cachemanagement-1</artifactId>
					</exclusion>
					<exclusion>
						<artifactId>log4j</artifactId>
						<groupId>log4j</groupId>
					</exclusion>
					<exclusion>
						<artifactId>slf4j-log4j12</artifactId>
						<groupId>org.slf4j</groupId>
					</exclusion>
					<exclusion>
						<artifactId>slf4j-api</artifactId>
						<groupId>org.slf4j</groupId>
					</exclusion>
					<exclusion>
						<artifactId>dubbo</artifactId>
						<groupId>com.alibaba</groupId>
					</exclusion>
					<exclusion>
						<artifactId>springframework-addons</artifactId>
						<groupId>net.unicon.springframework</groupId>
					</exclusion>
					<exclusion>
						<groupId>javax.servlet</groupId>
						<artifactId>servlet-api</artifactId>
					</exclusion>
					<exclusion>
						<artifactId>howbuy-ccms-independent</artifactId>
						<groupId>com.howbuy</groupId>
					</exclusion>
					<exclusion>
						<groupId>redis.clients</groupId>
						<artifactId>jedis</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.apache.commons</groupId>
						<artifactId>commons-pool2</artifactId>
					</exclusion>
				</exclusions>
			</dependency>

			<dependency>
				<groupId>com.howbuy.tms</groupId>
				<artifactId>tms-common-message-service</artifactId>
				<version>${com.howbuy.tms-common-message-service.version}</version>
			</dependency>
			

			<dependency>
				<groupId>com.howbuy</groupId>
				<artifactId>howbuy-interceptor</artifactId>
				<version>${com.howbuy.howbuy-interceptor.version}</version>
				<exclusions>
					<exclusion>
						<groupId>javax.servlet</groupId>
						<artifactId>servlet-api</artifactId>
					</exclusion>
					<exclusion>
						<groupId>com.howbuy</groupId>
						<artifactId>howbuy-cachemanagement</artifactId>
					</exclusion>
					<exclusion>
						<artifactId>log4j</artifactId>
						<groupId>log4j</groupId>
					</exclusion>
					<exclusion>
						<artifactId>slf4j-log4j12</artifactId>
						<groupId>org.slf4j</groupId>
					</exclusion>
					<exclusion>
						<artifactId>slf4j-api</artifactId>
						<groupId>org.slf4j</groupId>
					</exclusion>
				</exclusions>
			</dependency>

			<dependency>
				<groupId>com.howbuy.es</groupId>
				<artifactId>es-web-facade</artifactId>
				<version>${com.howbuy.es-web-facade.version}</version>
			</dependency>
		
			<dependency>
				<groupId>com.howbuy.cc</groupId>
				<artifactId>hbone-facade</artifactId>
				<version>${howbuy.hbone-facade.version}</version>
			</dependency>
				
			<dependency>
				<groupId>com.howbuy.common</groupId>
				<artifactId>common-service</artifactId>
				<version>${com.howbuy.common-service.version}</version>
			</dependency>
		
			<dependency>
				<groupId>com.howbuy.cc.message</groupId>
				<artifactId>message-public-client</artifactId>
				<version>${com.howbuy.message-public-client.version}</version>
			</dependency>
		
			<dependency>
				<groupId>com.howbuy</groupId>
				<artifactId>howbuy-weixin-client</artifactId>
				<version>${com.howbuy.howbuy-weixin-client.version}</version>
				<exclusions>
					<exclusion>
						<groupId>*</groupId>
						<artifactId>*</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
				
			<dependency>
				<groupId>com.howbuy.cc</groupId>
				<artifactId>center-client</artifactId>
				<version>${com.howbuy.center-client.version}</version>
			</dependency>
		
			<dependency>
				<groupId>com.howbuy</groupId>
				<artifactId>howbuy-cms-client</artifactId>
				<version>${com.howbuy.howbuy-cms-client.version}</version>
				<exclusions>
					<exclusion>
						<artifactId>log4j</artifactId>
						<groupId>log4j</groupId>
					</exclusion>
					<exclusion>
						<artifactId>slf4j-log4j12</artifactId>
						<groupId>org.slf4j</groupId>
					</exclusion>
					<exclusion>
						<artifactId>slf4j-api</artifactId>
						<groupId>org.slf4j</groupId>
					</exclusion>
					<exclusion>
						<groupId>com.baomidou</groupId>
						<artifactId>mybatis-plus-annotation</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.hibernate.validator</groupId>
						<artifactId>hibernate-validator</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			
			<dependency>
				<groupId>com.howbuy.act</groupId>
				<artifactId>howbuy-act-client</artifactId>
				<version>${com.howbuy.howbuy-act-client.version}</version>
				<exclusions>
					<exclusion>
						<artifactId>log4j</artifactId>
						<groupId>log4j</groupId>
					</exclusion>
					<exclusion>
						<artifactId>slf4j-log4j12</artifactId>
						<groupId>org.slf4j</groupId>
					</exclusion>
					<exclusion>
						<artifactId>slf4j-api</artifactId>
						<groupId>org.slf4j</groupId>
					</exclusion>
				</exclusions>
			</dependency>
		
			<dependency>
				<groupId>com.howbuy.crm</groupId>
				<artifactId>crm-td-client</artifactId>
				<version>${com.howbuy.crm-td-client.version}</version>
				<exclusions>
					<exclusion>
						<artifactId>log4j</artifactId>
						<groupId>log4j</groupId>
					</exclusion>
					<exclusion>
						<artifactId>slf4j-log4j12</artifactId>
						<groupId>org.slf4j</groupId>
					</exclusion>
					<exclusion>
						<artifactId>slf4j-api</artifactId>
						<groupId>org.slf4j</groupId>
					</exclusion>
				</exclusions>
			</dependency>

			<dependency>
				<groupId>com.howbuy.crm</groupId>
				<artifactId>crm-nt-client</artifactId>
				<version>${com.howbuy.crm-nt-client.version}</version>
			</dependency>

			<dependency>
				<groupId>com.howbuy.tms</groupId>
				<artifactId>high-order-center-client</artifactId>
				<version>${com.howbuy.high-order-center-client.version}</version>
			</dependency>

			<dependency>
				<groupId>com.howbuy.tms</groupId>
				<artifactId>high-batch-center-client</artifactId>
				<version>${com.howbuy.high-batch-center-client.version}</version>
			</dependency>

			<dependency>
				<groupId>com.howbuy.tms</groupId>
				<artifactId>regular-order-center-client</artifactId>
				<version>${com.howbuy.regular-order-center-client.version}</version>
			</dependency>
		
			<dependency>
				<groupId>com.howbuy</groupId>
				<artifactId>howbuy-fund-client</artifactId>
				<version>${com.howbuy.howbuy-fund-client.version}</version>
				<exclusions>
					<exclusion>
						<artifactId>log4j</artifactId>
						<groupId>log4j</groupId>
					</exclusion>
					<exclusion>
						<artifactId>slf4j-log4j12</artifactId>
						<groupId>org.slf4j</groupId>
					</exclusion>
					<exclusion>
						<artifactId>slf4j-api</artifactId>
						<groupId>org.slf4j</groupId>
					</exclusion>
				</exclusions>
			</dependency>

			<dependency>
				<groupId>com.howbuy</groupId>
				<artifactId>howbuy-simu-client</artifactId>
				<version>${com.howbuy.howbuy-simu-client.version}</version>
			</dependency>
		
			<dependency>
				<groupId>com.howbuy.payonline</groupId>
				<artifactId>pay-online-facade</artifactId>
				<version>${com.howbuy.pay-online-facade.version}</version>
			</dependency>
			
			<dependency>
				<groupId>com.howbuy</groupId>
				<artifactId>pay-common-model</artifactId>
				<version>${com.howbuy.pay-common-model.version}</version>
			</dependency>
		
			<dependency>
				<groupId>com.howbuy.common</groupId>
				<artifactId>common-facade</artifactId>
				<version>${com.howbuy.common-facade.version}</version>
			</dependency>
		
			<dependency>
				<groupId>com.howbuy.tms</groupId>
				<artifactId>csdc-pre-client</artifactId>
				<version>${com.howbuy.csdc-pre-client.version}</version>
			</dependency>
		
			<dependency>
				<groupId>com.howbuy</groupId>
				<artifactId>gateway-bxebank-client</artifactId>
				<version>${com.howbuy.gateway-bxebank-client.version}</version>
			</dependency>
			
			<dependency>
				<groupId>com.howbuy</groupId>
				<artifactId>gateway-captcha-client</artifactId>
				<version>${com.howbuy.gateway-captcha-client.version}</version>
			</dependency>

			<dependency>
				<groupId>com.howbuy.pdc</groupId>
				<artifactId>pdc-online-facade</artifactId>
				<version>${com.howbuy.pdc-online-facade.version}</version>
			</dependency>
		
			<dependency>
				<groupId>com.howbuy.finonline</groupId>
				<artifactId>fin-online-facade</artifactId>
				<version>${com.howbuy.fin-online-facade.version}</version>
			</dependency>
		
			<dependency>
				<groupId>com.howbuy.acccenter</groupId>
				<artifactId>acc-center-facade</artifactId>
				<version>${com.howbuy.acc-center-facade.version}</version>
				<exclusions>
					<exclusion>
						<groupId>*</groupId>
						<artifactId>*</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			
			<dependency>
				<groupId>com.howbuy.acccenter</groupId>
				<artifactId>acc-center-common</artifactId>
				<version>${com.howbuy.acc-center-common.version}</version>
				<exclusions>
					<exclusion>
						<groupId>*</groupId>
						<artifactId>*</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			
			<dependency>
	            <groupId>com.howbuy.acc</groupId>
	            <artifactId>acc-common-utils</artifactId>
	            <version>${com.howbuy.acc-common-utils.version}</version>
	            <exclusions>
	               <exclusion>
	                  <groupId>*</groupId>
	                  <artifactId>*</artifactId>
	               </exclusion>
	            </exclusions>
	        </dependency>
		
			<dependency>
				<groupId>com.howbuy.fbs</groupId>
				<artifactId>fbs-online-facade</artifactId>
				<version>${com.howbuy.fbs-online-facade.version}</version>
			</dependency>
			
			<dependency>
				<groupId>com.howbuy.fbs</groupId>
				<artifactId>fbs-online-search-facade</artifactId>
				<version>${com.howbuy.fbs-online-search-facade.version}</version>
			</dependency>

			<dependency>
				<groupId>com.howbuy.fbs</groupId>
				<artifactId>fbs-common-facade</artifactId>
				<version>${com.howbuy.fbs-common-facade.version}</version>
			</dependency>

			<dependency>
				<groupId>com.howbuy</groupId>
				<artifactId>ftx-order-facade</artifactId>
				<version>${com.howbuy.ftx-order-facade.version}</version>
			</dependency>

			<dependency>
				<groupId>com.howbuy.tms</groupId>
				<artifactId>batch-center-client</artifactId>
				<version>${com.howbuy.batch-center-client.version}</version>
			</dependency>

			<dependency>
				<groupId>com.howbuy.tms</groupId>
				<artifactId>elasticsearch-center-client</artifactId>
				<version>${com.howbuy.elasticsearch-center-client.version}</version>
			</dependency>

			<dependency>
				<groupId>com.howbuy.interlayer</groupId>
				<artifactId>product-center-model</artifactId>
				<version>${com.howbuy.product-center-model.version}</version>
			</dependency>

			<dependency>
				<groupId>com.howbuy.interlayer</groupId>
				<artifactId>product-center-client</artifactId>
				<version>${com.howbuy.product-center-client.version}</version>
			</dependency>

			<dependency>
				<groupId>com.howbuy.tms</groupId>
				<artifactId>order-center-client</artifactId>
				<version>${com.howbuy.order-center-client.version}</version>
			</dependency>

			<dependency>
				<groupId>com.howbuy.tms</groupId>
				<artifactId>robot-order-center-client</artifactId>
				<version>${com.howbuy.robot-order-center-client.version}</version>
			</dependency>

			
			<dependency>
				<groupId>com.howbuy</groupId>
				<artifactId>howbuy-asyncqueue</artifactId>
				<version>${howbuy.middleware.version}</version>
			</dependency>

			<dependency>
				<groupId>com.howbuy</groupId>
				<artifactId>howbuy-session</artifactId>
				<version>${com.howbuy.howbuy-session.version}</version>
				<exclusions>
					<exclusion>
						<artifactId>log4j</artifactId>
						<groupId>log4j</groupId>
					</exclusion>
					<exclusion>
						<artifactId>slf4j-log4j12</artifactId>
						<groupId>org.slf4j</groupId>
					</exclusion>
					<exclusion>
						<artifactId>slf4j-api</artifactId>
						<groupId>org.slf4j</groupId>
					</exclusion>
					<exclusion>
						<groupId>com.howbuy</groupId>
						<artifactId>howbuy-cachemanagement</artifactId>
					</exclusion>
				</exclusions>
			</dependency>

			<dependency>
				<groupId>com.howbuy</groupId>
				<artifactId>howbuy-judger</artifactId>
				<version>${howbuy.middleware.version}</version>
				<exclusions>
					<exclusion>
						<groupId>javax.servlet</groupId>
						<artifactId>servlet-api</artifactId>
					</exclusion>
					<exclusion>
						<artifactId>log4j</artifactId>
						<groupId>log4j</groupId>
					</exclusion>
					<exclusion>
						<artifactId>slf4j-log4j12</artifactId>
						<groupId>org.slf4j</groupId>
					</exclusion>
					<exclusion>
						<artifactId>slf4j-api</artifactId>
						<groupId>org.slf4j</groupId>
					</exclusion>
				</exclusions>
			</dependency>

			<dependency>
				<groupId>com.howbuy</groupId>
				<artifactId>HowbuyServiceBus</artifactId>
				<version>1.0.0</version>
			</dependency>

			<dependency>
				<groupId>com.howbuy</groupId>
				<artifactId>HowbuyServiceCommon</artifactId>
				<version>1.0.1</version>
			</dependency>

			
			<dependency>
				<groupId>com.howbuy</groupId>
				<artifactId>utils</artifactId>
				<version>${howbuy.client.version}</version>
				<exclusions>
					<exclusion>
						<artifactId>log4j</artifactId>
						<groupId>log4j</groupId>
					</exclusion>
					<exclusion>
						<artifactId>slf4j-log4j12</artifactId>
						<groupId>org.slf4j</groupId>
					</exclusion>
					<exclusion>
						<artifactId>slf4j-api</artifactId>
						<groupId>org.slf4j</groupId>
					</exclusion>
					<exclusion>
						<artifactId>tomcat-jdbc</artifactId>
						<groupId>org.apache.tomcat</groupId>
					</exclusion>
					<exclusion>
						<artifactId>juli</artifactId>
						<groupId>org.apache.tomcat</groupId>
					</exclusion>
				</exclusions>
			</dependency>

			<dependency>
				<groupId>com.howbuy.extend</groupId>
				<artifactId>Howbuy-security</artifactId>
				<version>1.0.0</version>
			</dependency>

			<dependency>
				<groupId>com.howbuy</groupId>
				<artifactId>howbuyNotification</artifactId>
				<version>1.0.3</version>
			</dependency>

			<dependency>
				<groupId>com.howbuy.extend</groupId>
				<artifactId>HowbuyValidator</artifactId>
				<version>1.0.0</version>
			</dependency>

			
			<dependency>
				<groupId>org.apache.zookeeper</groupId>
				<artifactId>zookeeper</artifactId>
				<version>3.4.5</version>
				<exclusions>
					<exclusion>
						<artifactId>log4j</artifactId>
						<groupId>log4j</groupId>
					</exclusion>
					<exclusion>
						<artifactId>slf4j-log4j12</artifactId>
						<groupId>org.slf4j</groupId>
					</exclusion>
					<exclusion>
						<artifactId>slf4j-api</artifactId>
						<groupId>org.slf4j</groupId>
					</exclusion>
				</exclusions>
			</dependency>

			<dependency>
				<groupId>com.github.sgroschupf</groupId>
				<artifactId>zkclient</artifactId>
				<version>0.1</version>
				<exclusions>
					<exclusion>
						<artifactId>log4j</artifactId>
						<groupId>log4j</groupId>
					</exclusion>
					<exclusion>
						<artifactId>slf4j-log4j12</artifactId>
						<groupId>org.slf4j</groupId>
					</exclusion>
					<exclusion>
						<artifactId>slf4j-api</artifactId>
						<groupId>org.slf4j</groupId>
					</exclusion>
				</exclusions>
			</dependency>

			<dependency>
				<groupId>org.apache.activemq</groupId>
				<artifactId>activemq-client</artifactId>
				<version>5.10.0</version>
				<exclusions>
					<exclusion>
						<artifactId>slf4j-api</artifactId>
						<groupId>org.slf4j</groupId>
					</exclusion>
				</exclusions>
			</dependency>

			<dependency>
				<groupId>commons-lang</groupId>
				<artifactId>commons-lang</artifactId>
				<version>2.6</version>
			</dependency>

			
			<dependency>
				<groupId>org.apache.dubbo</groupId>
				<artifactId>dubbo</artifactId>
				<version>${dubbo.version}</version>
				<exclusions>
					<exclusion>
						<artifactId>spring</artifactId>
						<groupId>org.springframework</groupId>
					</exclusion>
					<exclusion>
						<artifactId>spring</artifactId>
						<groupId>org.springframework</groupId>
					</exclusion>
					<exclusion>
						<groupId>org.slf4j</groupId>
						<artifactId>slf4j-log4j12</artifactId>
					</exclusion>
					<exclusion>
						<groupId>log4j</groupId>
						<artifactId>log4j</artifactId>
					</exclusion>
				</exclusions>
			</dependency>

			<dependency>
				<groupId>com.danga</groupId>
				<artifactId>Memcached-Java-Client</artifactId>
				<version>2.x</version>
			</dependency>

			
			<dependency>
				<groupId>org.apache.kafka</groupId>
				<artifactId>kafka_2.10</artifactId>
				<version>0.8.0</version>
				<exclusions>
					<exclusion>
						<groupId>net.sf.jopt-simple</groupId>
						<artifactId>jopt-simple</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.slf4j</groupId>
						<artifactId>slf4j-simple</artifactId>
					</exclusion>
					<exclusion>
						<artifactId>zkclient</artifactId>
						<groupId>com.101tec</groupId>
					</exclusion>
					<exclusion>
						<groupId>org.apache.zookeeper</groupId>
						<artifactId>zookeeper</artifactId>
					</exclusion>
				</exclusions>
			</dependency>

			
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-dependencies</artifactId>
				<version>${spring-cloud.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
			<dependency>
				<groupId>org.springframework</groupId>
				<artifactId>spring-core</artifactId>
				<version>${spring.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework</groupId>
				<artifactId>spring-beans</artifactId>
				<version>${spring.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework</groupId>
				<artifactId>spring-aop</artifactId>
				<version>${spring.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework</groupId>
				<artifactId>spring-context</artifactId>
				<version>${spring.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework</groupId>
				<artifactId>spring-context-support</artifactId>
				<version>${spring.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework</groupId>
				<artifactId>spring-test</artifactId>
				<version>${spring.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework</groupId>
				<artifactId>spring-oxm</artifactId>
				<version>${spring.version}</version>
			</dependency>

			<dependency>
				<groupId>org.springframework</groupId>
				<artifactId>spring-web</artifactId>
				<version>${spring.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework</groupId>
				<artifactId>spring-webmvc</artifactId>
				<version>${spring.version}</version>
			</dependency>

			<dependency>
				<groupId>org.springframework</groupId>
				<artifactId>spring-orm</artifactId>
				<version>${spring.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework</groupId>
				<artifactId>spring-jdbc</artifactId>
				<version>${spring.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework</groupId>
				<artifactId>spring-tx</artifactId>
				<version>${spring.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework</groupId>
				<artifactId>spring-aspects</artifactId>
				<version>${spring.version}</version>
			</dependency>
			<dependency>
				<groupId>net.unicon.springframework</groupId>
				<artifactId>springframework-addons</artifactId>
				<version>1.0-SNAPSHOT</version>
				<exclusions>
					<exclusion>
						<artifactId>log4j</artifactId>
						<groupId>log4j</groupId>
					</exclusion>
					<exclusion>
						<artifactId>slf4j-log4j12</artifactId>
						<groupId>org.slf4j</groupId>
					</exclusion>
					<exclusion>
						<artifactId>slf4j-api</artifactId>
						<groupId>org.slf4j</groupId>
					</exclusion>
					<exclusion>
						<groupId>org.apache.zookeeper</groupId>
						<artifactId>zookeeper</artifactId>
					</exclusion>
				</exclusions>
			</dependency>

			
			<dependency>
				<groupId>org.aspectj</groupId>
				<artifactId>aspectjweaver</artifactId>
				<version>1.8.9</version>
			</dependency>
			<dependency>
				<groupId>aspectj</groupId>
				<artifactId>aspectjrt</artifactId>
				<version>1.5.4</version>
			</dependency>

			<dependency>
				<groupId>org.softamis</groupId>
				<artifactId>cluster4spring</artifactId>
				<version>0.85</version>
			</dependency>

			
			<dependency>
				<groupId>com.fasterxml.jackson.core</groupId>
				<artifactId>jackson-core</artifactId>
				<version>${jackson2.version}</version>
			</dependency>
			<dependency>
				<groupId>com.fasterxml.jackson.core</groupId>
				<artifactId>jackson-databind</artifactId>
				<version>${jackson2.version}</version>
			</dependency>
			<dependency>
				<groupId>com.fasterxml.jackson.core</groupId>
				<artifactId>jackson-annotations</artifactId>
				<version>${jackson2.version}</version>
			</dependency>

			<dependency>
				<groupId>net.sf.json-lib</groupId>
				<artifactId>json-lib</artifactId>
				<classifier>jdk15</classifier>
				<version>2.2.3</version>
			</dependency>

			<dependency>
				<groupId>net.sf.oval</groupId>
				<artifactId>oval</artifactId>
				<version>1.84</version>
			</dependency>

			<dependency>
				<groupId>com.lmax</groupId>
				<artifactId>disruptor</artifactId>
				<version>3.4.2</version>
			</dependency>

			<dependency>
				<groupId>dom4j</groupId>
				<artifactId>dom4j</artifactId>
				<version>1.6.1</version>
			</dependency>

			
			<dependency>
				<groupId>org.apache.httpcomponents</groupId>
				<artifactId>httpclient</artifactId>
				<version>4.5.2</version>
			</dependency>

			
			<dependency>
				<groupId>org.apache.logging.log4j</groupId>
				<artifactId>log4j-api</artifactId>
				<version>${log4j.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.logging.log4j</groupId>
				<artifactId>log4j-core</artifactId>
				<version>${log4j.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.logging.log4j</groupId>
				<artifactId>log4j-web</artifactId>
				<version>${log4j.version}</version>
			</dependency>

			<dependency>
				<groupId>org.apache.logging.log4j</groupId>
				<artifactId>log4j-slf4j-impl</artifactId>
				<version>${log4j.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.logging.log4j</groupId>
				<artifactId>log4j-1.2-api</artifactId>
				<version>${log4j.version}</version>
			</dependency>

			<dependency>
				<groupId>javax.servlet</groupId>
				<artifactId>javax.servlet-api</artifactId>
				<version>3.1.0</version>
				<scope>provided</scope>
			</dependency>

			<dependency>
				<groupId>junit</groupId>
				<artifactId>junit</artifactId>
				<version>4.12</version>
				<scope>test</scope>
			</dependency>
			<dependency>
				<groupId>com.howbuy</groupId>
				<artifactId>howbuy-cachemanagement</artifactId>
				<version>${com.howbuy.howbuy-cachemanagement.version}</version>
				<exclusions>
					<exclusion>
						<artifactId>servlet-api</artifactId>
						<groupId>javax.servlet</groupId>
					</exclusion>
					<exclusion>
						<groupId>log4j</groupId>
						<artifactId>log4j</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.slf4j</groupId>
						<artifactId>slf4j-log4j12</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.slf4j</groupId>
						<artifactId>slf4j-api</artifactId>
					</exclusion>
					<exclusion>
						<artifactId>activemq-all</artifactId>
						<groupId>org.apache.activemq</groupId>
					</exclusion>
					<exclusion>
						<groupId>com.howbuy</groupId>
						<artifactId>HowbuyMessage</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.springframework</groupId>
						<artifactId>spring-beans</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.springframework</groupId>
						<artifactId>spring-core</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>com.howbuy.tms</groupId>
				<artifactId>pension-order-client</artifactId>
				<version>${com.howbuy.pension-order-client.version}</version>
				<exclusions>
					<exclusion>
						<groupId>org.hibernate.validator</groupId>
						<artifactId>hibernate-validator</artifactId>
					</exclusion>
				</exclusions>
			</dependency>

			
	        <dependency>
	            <groupId>org.jacoco</groupId>
	            <artifactId>jacoco-maven-plugin</artifactId>
			    <version>0.8.4</version>
	            <scope>test</scope>
	        </dependency>

	        <dependency>
	            <groupId>org.mockito</groupId>
	            <artifactId>mockito-core</artifactId>
			    <version>${com.howbuy.mockito-core.version}</version>
	            <scope>test</scope>
	        </dependency>

        	<dependency>
            	<groupId>org.powermock</groupId>
	            <artifactId>powermock-module-junit4</artifactId>
			    <version>${com.howbuy.powermock-module-junit4.version}</version>
	            <scope>test</scope>
	            <exclusions>
	            	<exclusion>
	            		<groupId>org.javassist</groupId>
	            		<artifactId>javassist</artifactId>
	            	</exclusion>
	            </exclusions>
	        </dependency>
	        <dependency>
	            <groupId>org.powermock</groupId>
	            <artifactId>powermock-api-mockito2</artifactId>
			    <version>${com.howbuy.powermock-api-mockito2.version}</version>
	            <scope>test</scope>
	        </dependency>

	        <dependency>
	            <groupId>org.jacoco</groupId>
	            <artifactId>org.jacoco.agent</artifactId>
			    <version>${com.howbuy.org.jacoco.agent.version}</version>
	            <classifier>runtime</classifier>
	        </dependency>

		</dependencies>
	</dependencyManagement>

	<dependencies>
		
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-api</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-core</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-web</artifactId>
		</dependency>

		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-slf4j-impl</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-1.2-api</artifactId>
		</dependency>
	</dependencies>

	<distributionManagement>
		<repository>
			<id>howbuy-release</id>
			<name>howbuy-release</name>
			<url>http://nx-java.howbuy.pa/repository/howbuy-release/</url>
		</repository>
		<snapshotRepository>
			<id>howbuy-snapshot</id>
			<name>howbuy-snapshot</name>
			<url>http://nx-java.howbuy.pa/repository/howbuy-snapshot/</url>
		</snapshotRepository>
	</distributionManagement>

	<repositories>
		<repository>
			<id>releases</id>
			<url>http://mvn.howbuy.pa/</url>
		</repository>
	</repositories>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.5.1</version>
				<configuration>
					<source>${java.version}</source>
					<target>${java.version}</target>
					<encoding>${project.encoding}</encoding>
					<compilerArguments>
						

					</compilerArguments>
				</configuration>
			</plugin>


			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-jar-plugin</artifactId>
				<version>3.0.0</version>
				<configuration>
					<archive>
						<manifestEntries>
							<Package-Stamp>${parelease}</Package-Stamp>
						</manifestEntries>
					</archive>
				</configuration>
			</plugin>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<version>2.9</version>
				<configuration>

					<includes>
						<include>**/*TestM.java</include>
					</includes>
				</configuration>

			</plugin>

		</plugins>
	</build>

	</project>