package com.howbuy.cgi.trade.simu.service.task;

import com.howbuy.cgi.trade.simu.model.dto.QueryBalanceContext;
import com.howbuy.interlayer.product.model.HighProductBaseInfoModel;
import com.howbuy.interlayer.product.service.HighProductService;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description:查询产品信息任务
 * @Author: yun.lu
 * Date: 2025/8/4 10:53
 */
public class QueryProductBaseInfoTask extends HowbuyBaseTask {
    private HighProductService highProductService;
    private List<String> fundCodes;
    private QueryBalanceContext queryBalanceContext;

    @Override
    protected void callTask() {
        List<HighProductBaseInfoModel> productInfoList = highProductService.getHighProductBaseInfo(fundCodes);

        Map<String, HighProductBaseInfoModel> productInfoMap = new HashMap<>();
        if (productInfoList != null) {
            for (HighProductBaseInfoModel highProductBaseInfoModel : productInfoList) {
                productInfoMap.put(highProductBaseInfoModel.getFundCode(), highProductBaseInfoModel);
            }
        }
        queryBalanceContext.setProductInfoMap(productInfoMap);
    }

    public QueryProductBaseInfoTask(HighProductService highProductService, List<String> fundCodes, QueryBalanceContext queryBalanceContext) {
        this.highProductService = highProductService;
        this.fundCodes = fundCodes;
        this.queryBalanceContext = queryBalanceContext;
    }

    public HighProductService getHighProductService() {
        return highProductService;
    }

    public void setHighProductService(HighProductService highProductService) {
        this.highProductService = highProductService;
    }

    public List<String> getFundCodes() {
        return fundCodes;
    }

    public void setFundCodes(List<String> fundCodes) {
        this.fundCodes = fundCodes;
    }

    public QueryBalanceContext getQueryBalanceContext() {
        return queryBalanceContext;
    }

    public void setQueryBalanceContext(QueryBalanceContext queryBalanceContext) {
        this.queryBalanceContext = queryBalanceContext;
    }
}
