package com.howbuy.cgi.trade.simu.service.task;

import com.howbuy.cgi.trade.simu.model.dto.QueryBalanceContext;
import com.howbuy.fund.service.base.product.JjxxService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description:产品分类
 * @Author: yun.lu
 * Date: 2025/8/4 11:14
 */
public class QueryCpflTask extends HowbuyBaseTask {
    private static final Logger log = LogManager.getLogger(QueryCpflTask.class);

    private List<String> productCodeList;
    private QueryBalanceContext queryBalanceContext;
    private JjxxService jjxxService;

    @Override
    protected void callTask() {
        Map<String, String> cpflMap = new HashMap<>();
        if (CollectionUtils.isEmpty(productCodeList)) {
            queryBalanceContext.setCpflMap(new HashMap<>());
            return;
        }
        try {
            cpflMap = jjxxService.getCpflMap(productCodeList.toArray(new String[productCodeList.size()]));
            log.info("productCodeList.size:{}, cpflMap.size:{}", productCodeList.size(), cpflMap.size());
        } catch (Exception e) {
            log.error("jxxService.getCpflMap error:{}", e.getMessage(), e);
        }

        if (cpflMap == null) {
            cpflMap = new HashMap<>();
        }
        queryBalanceContext.setCpflMap(cpflMap);
    }

    public List<String> getProductCodeList() {
        return productCodeList;
    }

    public void setProductCodeList(List<String> productCodeList) {
        this.productCodeList = productCodeList;
    }

    public QueryBalanceContext getQueryBalanceContext() {
        return queryBalanceContext;
    }

    public void setQueryBalanceContext(QueryBalanceContext queryBalanceContext) {
        this.queryBalanceContext = queryBalanceContext;
    }

    public JjxxService getJjxxService() {
        return jjxxService;
    }

    public void setJjxxService(JjxxService jjxxService) {
        this.jjxxService = jjxxService;
    }

    public QueryCpflTask(List<String> productCodeList, QueryBalanceContext queryBalanceContext, JjxxService jjxxService) {
        this.productCodeList = productCodeList;
        this.queryBalanceContext = queryBalanceContext;
        this.jjxxService = jjxxService;
    }
}
