<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.howbuy.cgi</groupId>
        <artifactId>cgi-simu-server-parent</artifactId>
        <version>4.9.16-RELEASE</version>
    </parent>

    <packaging>pom</packaging>
    <artifactId>cgi-ehowbuy-simu</artifactId>

    <properties>
        <com.howbuy.cgi-ehowbuy-common-parent.version>4.9.16-RELEASE</com.howbuy.cgi-ehowbuy-common-parent.version>
        <com.howbuy.cgi-service.version>4.9.16-RELEASE</com.howbuy.cgi-service.version>
        <com.howbuy.cgi-ehowbuy-server-parent.version>4.9.16-RELEASE</com.howbuy.cgi-ehowbuy-server-parent.version>
    </properties>

    <dependencyManagement>
        <dependencies>

            <dependency>
                <groupId>eu.bitwalker</groupId>
                <artifactId>UserAgentUtils</artifactId>
                <version>${UserAgentUtils.version}</version>
            </dependency>

            <dependency>
                <groupId>com.howbuy</groupId>
                <artifactId>howbuy-member-client</artifactId>
                <version>${com.howbuy.howbuy-member-client.version}</version>
            </dependency>

            <dependency>
                <groupId>org.javassist</groupId>
                <artifactId>javassist</artifactId>
                <version>${org.javassist.version}</version>
            </dependency>

            <!-- 单元测试 -->
            <dependency>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>${com.howbuy.powermock-module-junit4.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-core</artifactId>
                <version>${com.howbuy.mockito-core.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.powermock</groupId>
                <artifactId>powermock-module-junit4</artifactId>
                <version>${com.howbuy.powermock-module-junit4.version}</version>
                <scope>test</scope>
                <exclusions>
                    <exclusion>
                        <groupId>org.javassist</groupId>
                        <artifactId>javassist</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.powermock</groupId>
                <artifactId>powermock-api-mockito2</artifactId>
                <version>${com.howbuy.powermock-api-mockito2.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.jacoco</groupId>
                <artifactId>org.jacoco.agent</artifactId>
                <version>${com.howbuy.org.jacoco.agent.version}</version>
                <classifier>runtime</classifier>
            </dependency>

            <dependency>
                <groupId>com.howbuy.tms</groupId>
                <artifactId>tms-common-log-pattern</artifactId>
                <version>${com.howbuy.tms-common-log-pattern.version}</version>
            </dependency>

            <dependency>
                <groupId>com.howbuy</groupId>
                <artifactId>param-server-facade</artifactId>
                <version>${com.howbuy.param-server-facade.version}</version>
            </dependency>

            <dependency>
                <groupId>com.howbuy.trade.coop</groupId>
                <artifactId>howbuy-trade-coop-web</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.howbuy.tms</groupId>
                <artifactId>fin-center-client</artifactId>
                <version>${howbuy.middleware.version}</version>
            </dependency>

            <dependency>
                <groupId>com.howbuy.cgi</groupId>
                <artifactId>cgi-aspect</artifactId>
                <version>${com.howbuy.cgi-ehowbuy-common-parent.version}</version>
            </dependency>

            <dependency>
                <groupId>com.howbuy.cgi</groupId>
                <artifactId>cgi-common</artifactId>
                <version>${com.howbuy.cgi-ehowbuy-common-parent.version}</version>
            </dependency>

            <dependency>
                <groupId>com.howbuy.cgi</groupId>
                <artifactId>cgi-trade</artifactId>
                <version>${com.howbuy.cgi-ehowbuy-server-parent.version}</version>
            </dependency>

            <!-- ehowbuy-cgi service 层 begin-->
            <dependency>
                <groupId>com.howbuy.cgi</groupId>
                <artifactId>service-common</artifactId>
                <version>${com.howbuy.cgi-service.version}</version>
            </dependency>

            <dependency>
                <groupId>com.howbuy.cgi</groupId>
                <artifactId>service-account</artifactId>
                <version>${com.howbuy.cgi-service.version}</version>
            </dependency>

            <dependency>
                <groupId>com.howbuy.cgi</groupId>
                <artifactId>service-asset</artifactId>
                <version>${com.howbuy.cgi-service.version}</version>
            </dependency>

            <dependency>
                <groupId>com.howbuy.cgi</groupId>
                <artifactId>service-dc</artifactId>
                <version>${com.howbuy.cgi-service.version}</version>
            </dependency>

            <dependency>
                <groupId>com.howbuy.cgi</groupId>
                <artifactId>service-fincenter</artifactId>
                <version>${com.howbuy.cgi-service.version}</version>
            </dependency>

            <dependency>
                <groupId>com.howbuy.cgi</groupId>
                <artifactId>service-kyc</artifactId>
                <version>${com.howbuy.cgi-service.version}</version>
            </dependency>

            <dependency>
                <groupId>com.howbuy.cgi</groupId>
                <artifactId>service-piggy</artifactId>
                <version>${com.howbuy.cgi-service.version}</version>
            </dependency>

            <dependency>
                <groupId>com.howbuy.cgi</groupId>
                <artifactId>service-regular</artifactId>
                <version>${com.howbuy.cgi-service.version}</version>
            </dependency>

            <dependency>
                <groupId>com.howbuy.cgi</groupId>
                <artifactId>service-robot</artifactId>
                <version>${com.howbuy.cgi-service.version}</version>
            </dependency>

            <dependency>
                <groupId>com.howbuy.cgi</groupId>
                <artifactId>service-trade</artifactId>
                <version>${com.howbuy.cgi-service.version}</version>
            </dependency>
            <!-- ehowbuy-cgi service 层 end-->

            <dependency>
                <groupId>com.howbuy</groupId>
                <artifactId>webUtil</artifactId>
                <version>${com.howbuy.webUtil.version}</version>
            </dependency>

            <dependency>
                <groupId>com.howbuy</groupId>
                <artifactId>howbuyUtil</artifactId>
                <version>1.0.2</version>
            </dependency>

            <dependency>
                <groupId>com.howbuy</groupId>
                <artifactId>howbuy-judger</artifactId>
                <version>1.0.0-release</version>
            </dependency>

            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>15.0-rc1</version>
            </dependency>

            <dependency>
                <groupId>com.octo.captcha</groupId>
                <artifactId>jcaptcha-all</artifactId>
                <version>1.0-RC6-modify</version>
            </dependency>

            <dependency>
                <groupId>joda-time</groupId>
                <artifactId>joda-time</artifactId>
                <version>2.9.4</version>
            </dependency>

            <dependency>
                <groupId>javax.xml.bind</groupId>
                <artifactId>jaxb-api</artifactId>
                <version>2.2.12</version>
            </dependency>

            <dependency>
                <groupId>commons-fileupload</groupId>
                <artifactId>commons-fileupload</artifactId>
                <version>1.3.2</version>
            </dependency>

            <dependency>
                <groupId>com.howbuy.hystrix</groupId>
                <artifactId>dubbo-hystrix-support</artifactId>
                <version>${howbuy.client.version}</version>
            </dependency>

            <dependency>
                <groupId>com.netflix.hystrix</groupId>
                <artifactId>hystrix-metrics-event-stream</artifactId>
                <version>1.1.2</version>
            </dependency>

            <dependency>
                <groupId>com.howbuy</groupId>
                <artifactId>howbuy-persistence</artifactId>
                <version>${com.howbuy.howbuy-persistence.version}</version>
            </dependency>

            <dependency>
                <groupId>org.xhtmlrenderer</groupId>
                <artifactId>core-renderer</artifactId>
                <version>R8</version>
            </dependency>

            <dependency>
                <groupId>com.howbuy</groupId>
                <artifactId>howbuy-ccms-watcher</artifactId>
                <version>${com.howbuy.howbuy-ccms.version}</version>
            </dependency>
            <dependency>
                <groupId>com.howbuy</groupId>
                <artifactId>howbuy-auth-facade</artifactId>
                <version>${com.howbuy.howbuy-auth-facade.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-core</artifactId>
                <version>3.5.7</version> <!-- 请使用最新版本或者你需要的版本 -->
            </dependency>
        </dependencies>
    </dependencyManagement>


    <modules>
        <module>cgi-container</module>
        <module>cgi-simu</module>
    </modules>
</project>