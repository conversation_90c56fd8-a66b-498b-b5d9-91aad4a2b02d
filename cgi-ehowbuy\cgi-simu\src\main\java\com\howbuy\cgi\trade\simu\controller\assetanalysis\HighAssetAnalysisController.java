/**
 * Copyright (c) 2025, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.cgi.trade.simu.controller.assetanalysis;

import com.howbuy.cgi.trade.simu.controller.AbstractSimuCGIController;

/**
 * 理财分析2.0
 *
 * <AUTHOR>
 * @date 2025/9/1 14:21
 * @since JDK 1.8
 */
public class HighAssetAnalysisController extends AbstractSimuCGIController {

    /**
     * @api {GET} /simu/assetanalysis/querytotalassetwithtrend.htm 查询总资产及资产变动走势
     * @apiVersion 2.0.0
     * @apiGroup assetAnalysis
     * @apiName queryTotalAssetWithTrend
     * @apiDescription 查询总资产及资产变动走势
     * @apiParam (请求参数) {String} hboneNo 一账通号(必填)
     * @apiParam (请求参数) {Array} [fundCodes] 基金代码列表(可选)
     * @apiParam (请求参数) {String} intervalType 区间类型(必填) CY-今年以来,6M-近6个月,1Y-近一年,3Y-近3年,ALL-投资以来
     * @apiParam (请求参数) {String} [returnIntervalTypeTabListFlag] 是否返回可选区间类型tab列表标记(可选) Y-是,N-否
     * @apiParamExample 请求参数示例
     * hboneNo=123456789&fundCodes=["000001","000002"]&intervalType=1Y&returnIntervalTypeTabListFlag=Y
     * @apiSuccess (响应结果) {String} code 结果码 0000
     * @apiSuccess (响应结果) {String} desc 结果码描述 成功
     * @apiSuccess (响应结果) {Object} body 响应数据体
     * @apiSuccess (响应结果) {String} body.currentDt 系统当前日期(yyyyMMdd)
     * @apiSuccess (响应结果) {String} body.statDt 统计日期(yyyyMMdd)
     * @apiSuccess (响应结果) {String} body.totalAsset 总资产
     * @apiSuccess (响应结果) {Array} [body.intervalTypeList] 可选区间类型列表(上送返回区间列表标记才返回) CY-今年以来,6M-近6个月,1Y-近一年,3Y-近3年,ALL-投资以来
     * @apiSuccess (响应结果) {String} [body.defaultIntervalType] 默认选中区间类型(上送返回区间列表标记才返回) CY-今年以来,6M-近6个月,1Y-近一年,3Y-近3年,ALL-投资以来
     * @apiSuccess (响应结果) {String} body.inTransitAsset 在途资产
     * @apiSuccess (响应结果) {String} body.holdHaoZhenOrHongKongFlag 当前是否持有好臻或者香港产品标记(Y-是,N-否)
     * @apiSuccess (响应结果) {Array} body.assetTrendList 资产变动走势图数据列表
     * @apiSuccess (响应结果) {String} body.assetTrendList.dt 日期(yyyyMMdd)
     * @apiSuccess (响应结果) {String} body.assetTrendList.totalAsset 总资产
     * @apiSuccess (响应结果) {String} timestampServer 服务器时间戳
     * @apiSuccessExample 响应结果示例
     * {
     *   "code": "0000",
     *   "desc": "成功",
     *   "body": {
     *     "currentDt": "20250901",
     *     "statDt": "20250831",
     *     "totalAsset": "1000000.00",
     *     "intervalTypeList": ["CY", "6M", "1Y", "3Y", "ALL"],
     *     "defaultIntervalType": "1Y",
     *     "inTransitAsset": "50000.00",
     *     "holdHaoZhenOrHongKongFlag": "Y",
     *     "assetTrendList": [
     *       {
     *         "dt": "20250801",
     *         "totalAsset": "950000.00"
     *       },
     *       {
     *         "dt": "20250815",
     *         "totalAsset": "980000.00"
     *       },
     *       {
     *         "dt": "20250831",
     *         "totalAsset": "1000000.00"
     *       }
     *     ]
     *   },
     *   "timestampServer": "1756711162758"
     * }
     */

    /**
     * @api {GET} /simu/assetanalysis/queryassetdistribution.htm 查询资产分布
     * @apiVersion 2.0.0
     * @apiGroup assetAnalysis
     * @apiName queryAssetDistribution
     * @apiDescription 查询资产分布
     * @apiParam (请求参数) {String} hboneNo 一账通号(必填)
     * @apiParam (请求参数) {Array} [fundCodes] 基金代码列表(可选)
     * @apiParamExample 请求参数示例
     * hboneNo=123456789&fundCodes=["000001","000002"]
     * @apiSuccess (响应结果) {String} code 结果码 0000
     * @apiSuccess (响应结果) {String} desc 结果码描述 成功
     * @apiSuccess (响应结果) {Object} body 响应数据体
     * @apiSuccess (响应结果) {String} body.holdDlpzFofFlag 当前是否持有大类配置FOF标记(Y-是,N-否)
     * @apiSuccess (响应结果) {Array} body.distributionList 资产分布配比对象列表
     * @apiSuccess (响应结果) {String} body.distributionList.strategyType 策略类型 DLPZFOF-多策略,101-股票型,102-股权型,201-CTA策略,202-另类策略,301-固收与中性,303-收益型保险,999-其他
     * @apiSuccess (响应结果) {String} body.distributionList.strategyName 策略名称
     * @apiSuccess (响应结果) {String} body.distributionList.strategyTotalValue 策略总市值
     * @apiSuccess (响应结果) {String} body.distributionList.strategyRatio 策略占比
     * @apiSuccess (响应结果) {String} body.distributionList.strategyRatioStr 策略占比字符串
     * @apiSuccess (响应结果) {String} timestampServer 服务器时间戳
     * @apiSuccessExample 响应结果示例
     * {
     *   "code": "0000",
     *   "desc": "成功",
     *   "body": {
     *     "holdDlpzFofFlag": "Y",
     *     "distributionList": [
     *       {
     *         "strategyType": "DLPZFOF",
     *         "strategyName": "多策略",
     *         "strategyTotalValue": "500000.00",
     *         "strategyRatio": "0.50",
     *         "strategyRatioStr": "50%"
     *       },
     *       {
     *         "strategyType": "101",
     *         "strategyName": "股票型",
     *         "strategyTotalValue": "300000.00",
     *         "strategyRatio": "0.30",
     *         "strategyRatioStr": "30%"
     *       }
     *     ]
     *   },
     *   "timestampServer": "1756711162758"
     * }
     */

    /**
     * @api {GET} /simu/assetanalysis/querymyholdinglist.htm 查询我的持有列表
     * @apiVersion 2.0.0
     * @apiGroup assetAnalysis
     * @apiName queryMyHoldingList
     * @apiDescription 查询我的持有列表
     * @apiParam (请求参数) {String} hboneNo 一账通号(必填)
     * @apiParam (请求参数) {Array} [fundCodes] 基金代码列表(可选)
     * @apiParam (请求参数) {String} sortBy 排序方式(必填) asset-按资产,income-按收益
     * @apiParam (请求参数) {String} sortType 排序类型(必填) asc-正序,desc-倒序
     * @apiParamExample 请求参数示例
     * hboneNo=123456789&fundCodes=["000001","000002"]&sortBy=asset&sortType=desc
     * @apiSuccess (响应结果) {String} code 结果码 0000
     * @apiSuccess (响应结果) {String} desc 结果码描述 成功
     * @apiSuccess (响应结果) {Object} body 响应数据体
     * @apiSuccess (响应结果) {Integer} body.holdingCount 持仓产品个数
     * @apiSuccess (响应结果) {Array} body.holdingList 持仓详情对象列表
     * @apiSuccess (响应结果) {String} body.holdingList.fundCode 基金代码
     * @apiSuccess (响应结果) {String} body.holdingList.fundShortName 基金简称
     * @apiSuccess (响应结果) {String} body.holdingList.strategyType 产品策略类型 DLPZFOF-多策略,101-股票型,102-股权型,201-CTA策略,202-另类策略,301-固收与中性,303-收益型保险,999-其他
     * @apiSuccess (响应结果) {String} body.holdingList.strategyName 产品策略名称
     * @apiSuccess (响应结果) {String} body.holdingList.fixedIncomeType 固定收益类型,0-股权固收,1-正常固收,2-现金管理,3-券商集合,4-纯债产品
     * @apiSuccess (响应结果) {String} body.holdingList.balanceAmt 市值
     * @apiSuccess (响应结果) {String} body.holdingList.balanceIncomeExFee 持仓收益(不含费)
     * @apiSuccess (响应结果) {String} body.holdingList.accumCollection 累计总回款
     * @apiSuccess (响应结果) {String} body.holdingList.accumCostExFeeNew 投资总成本_新(不含费)
     * @apiSuccess (响应结果) {String} body.holdingList.currency 币种 如840-美元
     * @apiSuccess (响应结果) {String} timestampServer 服务器时间戳
     * @apiSuccessExample 响应结果示例
     * {
     *   "code": "0000",
     *   "desc": "成功",
     *   "body": {
     *     "holdingCount": 3,
     *     "holdingList": [
     *       {
     *         "fundCode": "000001",
     *         "fundShortName": "好买基金1号",
     *         "strategyType": "DLPZFOF",
     *         "strategyName": "多策略",
     *         "fixedIncomeType": "1",
     *         "balanceAmt": "500000.00",
     *         "balanceIncomeExFee": "50000.00",
     *         "accumCollection": "100000.00",
     *         "accumCostExFeeNew": "400000.00",
     *         "currency": "840"
     *       }
     *     ]
     *   },
     *   "timestampServer": "1756711162758"
     * }
     */

    /**
     * @api {GET} /simu/assetanalysis/queryincomeoverviewwithtrend.htm 查询收益总览+区间收益走势
     * @apiVersion 2.0.0
     * @apiGroup assetAnalysis
     * @apiName queryIncomeOverviewWithTrend
     * @apiDescription 查询收益总览+区间收益走势
     * @apiParam (请求参数) {String} hboneNo 一账通号(必填)
     * @apiParam (请求参数) {Array} [fundCodes] 基金代码列表(可选)
     * @apiParam (请求参数) {String} intervalType 区间类型(必填) CY-今年以来,6M-近6个月,1Y-近一年,3Y-近3年,ALL-投资以来
     * @apiParam (请求参数) {String} [returnIntervalTypeTabListFlag] 是否返回可选区间类型tab列表标记(可选) Y-是,N-否
     * @apiParam (请求参数) {String} [defaultIntervalType] 默认选中区间类型(可选) CY-今年以来,6M-近6个月,1Y-近一年,3Y-近3年,ALL-投资以来
     * @apiParam (请求参数) {Array} [excludeIntervalTypeList] 不下发的区间类型列表(可选,持仓详情页的走势无3Y-近三年)
     * @apiParam (请求参数) {String} [transferToRmbFlag] 转换为人民币标记(必选) Y-是,N-否,默认转换（单产品时才可以不转换）
     * @apiParamExample 请求参数示例
     * hboneNo=123456789&fundCodes=["000001","000002"]&intervalType=1Y&returnIntervalTypeTabListFlag=Y&transferToRmbFlag=Y
     * @apiSuccess (响应结果) {String} code 结果码 0000
     * @apiSuccess (响应结果) {String} desc 结果码描述 成功
     * @apiSuccess (响应结果) {Object} body 响应数据体
     * @apiSuccess (响应结果) {String} body.currentDt 系统当前日期(yyyyMMdd)
     * @apiSuccess (响应结果) {String} body.statStartDt 统计开始日期(yyyyMMdd)
     * @apiSuccess (响应结果) {String} body.statEndDt 统计结束日期(yyyyMMdd)
     * @apiSuccess (响应结果) {String} body.intervalAccumIncome 区间累计收益
     * @apiSuccess (响应结果) {String} body.latestIncomeDt 最新收益日期(yyyyMMdd)
     * @apiSuccess (响应结果) {String} body.latestAccumIncome 最新累计收益
     * @apiSuccess (响应结果) {String} body.intervalIncomeRate 区间收益率(时间加权算法)
     * @apiSuccess (响应结果) {String} body.defaultBenchmarkChangeRate 默认的基准区间涨跌幅
     * @apiSuccess (响应结果) {Array} body.incomeTrendList 区间收益率走势图数据列表
     * @apiSuccess (响应结果) {String} body.incomeTrendList.intervalIncomeRate 区间收益率
     * @apiSuccess (响应结果) {String} body.incomeTrendList.intervalAccumIncome 区间累计收益
     * @apiSuccess (响应结果) {String} body.incomeTrendList.dailyIncome 当日收益
     * @apiSuccess (响应结果) {String} body.incomeTrendList.dt 日期(yyyyMMdd)
     * @apiSuccess (响应结果) {Array} body.benchmarkTrendList 默认的区间基准涨跌幅走势图数据列表
     * @apiSuccess (响应结果) {String} body.benchmarkTrendList.rate 基准涨跌幅(%)
     * @apiSuccess (响应结果) {String} body.benchmarkTrendList.dt 日期(yyyyMMdd)
     * @apiSuccess (响应结果) {String} timestampServer 服务器时间戳
     * @apiSuccessExample 响应结果示例
     * {
     *   "code": "0000",
     *   "desc": "成功",
     *   "body": {
     *     "currentDt": "20250901",
     *     "statStartDt": "20240901",
     *     "statEndDt": "20250831",
     *     "intervalAccumIncome": "50000.00",
     *     "latestIncomeDt": "20250831",
     *     "latestAccumIncome": "50000.00",
     *     "intervalIncomeRate": "0.05",
     *     "defaultBenchmarkChangeRate": "0.93",
     *     "incomeTrendList": [
     *       {
     *         "intervalIncomeRate": "0.05",
     *         "intervalAccumIncome": "50000.00",
     *         "dailyIncome": "2000.00",
     *         "dt": "20250831"
     *       }
     *     ],
     *     "benchmarkTrendList": [
     *       {
     *         "rate": "0.03",
     *         "dt": "20250831"
     *       }
     *     ]
     *   },
     *   "timestampServer": "1756711162758"
     * }
     */

    /**
     * @api {GET} /simu/assetanalysis/queryincomecalendar.htm 查询收益日历
     * @apiVersion 2.0.0
     * @apiGroup assetAnalysis
     * @apiName queryIncomeCalendar
     * @apiDescription 查询收益日历
     * @apiParam (请求参数) {String} hboneNo 一账通号(必填)
     * @apiParam (请求参数) {Array} [fundCodes] 基金代码列表(可选)
     * @apiParam (请求参数) {String} calendarType 日历类型(必填) Y-年,M-月,D-日
     * @apiParam (请求参数) {String} [dt] 日期,年份时可为空,月份时为 yyyy 日期时为 yyyyMM
     * @apiParam (请求参数) {String} [transferToRmbFlag] 转换为人民币标记 Y-是,N-否,单产品时才可以不转换
     * @apiParamExample 请求参数示例
     * hboneNo=123456789&fundCodes=["000001","000002"]&calendarType=M&dt=2025&transferToRmbFlag=Y
     * @apiSuccess (响应结果) {String} code 结果码 0000
     * @apiSuccess (响应结果) {String} desc 结果码描述 成功
     * @apiSuccess (响应结果) {Object} body 响应数据体
     * @apiSuccess (响应结果) {String} body.currentDt 系统当前日期(yyyyMMdd)
     * @apiSuccess (响应结果) {String} body.statStartDt 统计开始日期(yyyyMMdd)
     * @apiSuccess (响应结果) {String} body.statEndDt 统计结束日期(yyyyMMdd)
     * @apiSuccess (响应结果) {Array} body.calendarList 日历对象列表
     * @apiSuccess (响应结果) {String} body.calendarList.startDt 起始日期(yyyyMMdd)
     * @apiSuccess (响应结果) {String} body.calendarList.endDt 结束日期(yyyyMMdd)
     * @apiSuccess (响应结果) {String} body.calendarList.intervalIncome 区间收益
     * @apiSuccess (响应结果) {String} body.calendarList.intervalIncomeRate 区间收益率
     * @apiSuccess (响应结果) {String} timestampServer 服务器时间戳
     * @apiSuccessExample 响应结果示例
     * {
     *   "code": "0000",
     *   "desc": "成功",
     *   "body": {
     *     "currentDt": "20250901",
     *     "statStartDt": "20250101",
     *     "statEndDt": "20251231",
     *     "calendarList": [
     *       {
     *         "startDt": "20250101",
     *         "endDt": "20250131",
     *         "intervalIncome": "5000.00",
     *         "intervalIncomeRate": "0.05"
     *       },
     *       {
     *         "startDt": "20250201",
     *         "endDt": "20250228",
     *         "intervalIncome": "6000.00",
     *         "intervalIncomeRate": "0.06"
     *       }
     *     ]
     *   },
     *   "timestampServer": "1756711162758"
     * }
     */

    /**
     * @api {GET} /simu/assetanalysis/queryintervalproductincomedetail.htm 查询区间产品收益明细列表
     * @apiVersion 2.0.0
     * @apiGroup assetAnalysis
     * @apiName queryIntervalProductIncomeDetail
     * @apiDescription 查询区间产品收益明细列表
     * @apiParam (请求参数) {String} hboneNo 一账通号(必填)
     * @apiParam (请求参数) {Array} [fundCodes] 基金代码列表(可选)
     * @apiParam (请求参数) {String} startDt 开始日期(必填) yyyyMMdd
     * @apiParam (请求参数) {String} endDt 结束日期(必填) yyyyMMdd
     * @apiParam (请求参数) {String} sortBy 排序方式(必填) intervalIncome-区间收益,accumIncome-累计收益
     * @apiParam (请求参数) {String} sortType 排序类型(必填) asc-正序,desc-倒序
     * @apiParamExample 请求参数示例
     * hboneNo=123456789&fundCodes=["000001","000002"]&startDt=20250101&endDt=20250131&sortBy=intervalIncome&sortType=desc
     * @apiSuccess (响应结果) {String} code 结果码 0000
     * @apiSuccess (响应结果) {String} desc 结果码描述 成功
     * @apiSuccess (响应结果) {Object} body 响应数据体
     * @apiSuccess (响应结果) {String} body.currentDt 系统当前日期(yyyyMMdd)
     * @apiSuccess (响应结果) {String} body.intervalTotalIncome 区间收益合计
     * @apiSuccess (响应结果) {Array} body.productIncomeDetailList 产品区间收益明细列表
     * @apiSuccess (响应结果) {String} body.productIncomeDetailList.fundCode 基金代码
     * @apiSuccess (响应结果) {String} body.productIncomeDetailList.fundShortName 基金简称
     * @apiSuccess (响应结果) {String} body.productIncomeDetailList.strategyCode 策略code
     * @apiSuccess (响应结果) {String} body.productIncomeDetailList.strategyName 策略名称
     * @apiSuccess (响应结果) {String} body.productIncomeDetailList.intervalIncome 区间收益
     * @apiSuccess (响应结果) {String} body.productIncomeDetailList.accumIncome 累计收益
     * @apiSuccess (响应结果) {String} body.productIncomeDetailList.clearFlag 清仓标记(Y-是,N-否)
     * @apiSuccess (响应结果) {String} timestampServer 服务器时间戳
     * @apiSuccessExample 响应结果示例
     * {
     *   "code": "0000",
     *   "desc": "成功",
     *   "body": {
     *     "currentDt": "20250901",
     *     "intervalTotalIncome": "50000.00",
     *     "productIncomeDetailList": [
     *       {
     *         "fundCode": "000001",
     *         "fundShortName": "好买基金1号",
     *         "strategyCode": "DLPZFOF",
     *         "strategyName": "多策略",
     *         "intervalIncome": "20000.00",
     *         "accumIncome": "50000.00",
     *         "clearFlag": "N"
     *       }
     *     ]
     *   },
     *   "timestampServer": "1756711162758"
     * }
     */

    /**
     * @api {GET} /simu/assetanalysis/querybehaviorreview.htm 查询行为回顾
     * @apiVersion 2.0.0
     * @apiGroup assetAnalysis
     * @apiName queryBehaviorReview
     * @apiDescription 查询行为回顾
     * @apiParam (请求参数) {String} hboneNo 一账通号(必填)
     * @apiParam (请求参数) {Array} [fundCodes] 基金代码列表(可选)
     * @apiParamExample 请求参数示例
     * hboneNo=123456789&fundCodes=["000001","000002"]
     * @apiSuccess (响应结果) {String} code 结果码 0000
     * @apiSuccess (响应结果) {String} desc 结果码描述 成功
     * @apiSuccess (响应结果) {Object} body 响应数据体
     * @apiSuccess (响应结果) {String} body.startDt 开始日期(yyyyMMdd)
     * @apiSuccess (响应结果) {String} body.endDt 结束日期(yyyyMMdd)
     * @apiSuccess (响应结果) {Integer} body.buyCount 买入次数
     * @apiSuccess (响应结果) {Integer} body.sellCount 卖出次数
     * @apiSuccess (响应结果) {Integer} body.avgHoldDays 平均持有时长(天)
     * @apiSuccess (响应结果) {Array} body.holdDurationDistributionList 累计持有时长分布列表
     * @apiSuccess (响应结果) {String} body.holdDurationDistributionList.code 持有时长代码 1-1年以下,2-1-3年,3-3-5年,4-5年以上
     * @apiSuccess (响应结果) {Integer} body.holdDurationDistributionList.productCount 产品个数
     * @apiSuccess (响应结果) {Array} body.tradingBehaviorTrendList 交易行为走势列表
     * @apiSuccess (响应结果) {String} body.tradingBehaviorTrendList.dt 日期(yyyyMMdd)
     * @apiSuccess (响应结果) {Array} body.tradingBehaviorTrendList.operationTypeList 操作类型列表 BUY-买入,SELL-卖出
     * @apiSuccess (响应结果) {Array} body.tradingBehaviorTrendList.buyFundCodeList 买入基金代码列表
     * @apiSuccess (响应结果) {Array} body.tradingBehaviorTrendList.buyFundShortNameList 买入基金简称列表
     * @apiSuccess (响应结果) {Array} body.tradingBehaviorTrendList.sellFundCodeList 卖出基金代码列表
     * @apiSuccess (响应结果) {Array} body.tradingBehaviorTrendList.sellFundShortNameList 卖出基金简称列表
     * @apiSuccess (响应结果) {String} body.tradingBehaviorTrendList.netBuyAmount 净买入金额
     * @apiSuccess (响应结果) {Array} body.benchmarkReturnTrendList 默认的基准回报走势列表
     * @apiSuccess (响应结果) {String} body.benchmarkReturnTrendList.dt 日期(yyyyMMdd)
     * @apiSuccess (响应结果) {String} body.benchmarkReturnTrendList.closePrice 收盘价
     * @apiSuccess (响应结果) {String} timestampServer 服务器时间戳
     * @apiSuccessExample 响应结果示例
     * {
     *   "code": "0000",
     *   "desc": "成功",
     *   "body": {
     *     "startDt": "20240101",
     *     "endDt": "20250831",
     *     "buyCount": 15,
     *     "sellCount": 8,
     *     "avgHoldDays": 180,
     *     "holdDurationDistributionList": [
     *       {
     *         "code": "1",
     *         "productCount": 3
     *       }
     *     ],
     *     "tradingBehaviorTrendList": [
     *       {
     *         "dt": "20240115",
     *         "operationTypeList": ["BUY"],
     *         "buyFundCodeList": ["000001"],
     *         "buyFundShortNameList": ["好买基金1号"],
     *         "sellFundCodeList": [],
     *         "sellFundShortNameList": [],
     *         "netBuyAmount": "100000.00"
     *       }
     *     ],
     *     "benchmarkReturnTrendList": [
     *       {
     *         "dt": "20240101",
     *         "closePrice": "100.00"
     *       }
     *     ]
     *   },
     *   "timestampServer": "1756711162758"
     * }
     */

}
