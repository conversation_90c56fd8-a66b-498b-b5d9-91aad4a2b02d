package com.howbuy.cgi.trade.simu.service.relatedaccount;

import com.alibaba.fastjson.JSON;
import com.howbuy.acccenter.common.enums.ExamTypeEnum;
import com.howbuy.acccenter.facade.query.queryacccustinfo.QueryAccCustInfoFacade;
import com.howbuy.acccenter.facade.query.queryacccustinfo.QueryAccCustInfoRequest;
import com.howbuy.acccenter.facade.query.queryacccustinfo.QueryAccCustInfoResponse;
import com.howbuy.acccenter.facade.query.queryacccustinfo.bean.AccCustInfoBean;
import com.howbuy.acccenter.facade.trade.kycinfo.*;
import com.howbuy.acccenter.facade.trade.signdataauthagreement.SignDataAuthAgreementFacade;
import com.howbuy.acccenter.facade.trade.signdataauthagreement.SignDataAuthAgreementRequest;
import com.howbuy.acccenter.facade.trade.signdataauthagreement.SignDataAuthAgreementResponse;
import com.howbuy.acccenter.facade.trade.signelectxagreement.SignElecTxAgreementFacade;
import com.howbuy.acccenter.facade.trade.signelectxagreement.SignElecTxAgreementRequest;
import com.howbuy.acccenter.facade.trade.signelectxagreement.SignElecTxAgreementResponse;
import com.howbuy.cgi.common.enums.BizErrorEnum;
import com.howbuy.cgi.common.exception.BizException;
import com.howbuy.cgi.common.remote.param.RemoteParametersProvider;
import com.howbuy.cgi.common.util.DateUtil;
import com.howbuy.cgi.trade.ccms.simu.SimuCcmsServiceRegister;
import com.howbuy.cgi.trade.simu.common.CGISimuConstants;
import com.howbuy.cgi.trade.simu.model.cmd.CustSignCmd;
import com.howbuy.cgi.trade.simu.model.dto.*;
import com.howbuy.cgi.trade.simu.model.vo.*;
import com.howbuy.cgi.trade.simu.service.FormService;
import com.howbuy.common.utils.StringUtil;
import com.howbuy.es.digestsign.handsign.*;
import com.howbuy.es.digestsign.handsign.bean.HandSignSealBean;
import com.howbuy.interlayer.product.model.HighProductBaseInfoModel;
import com.howbuy.interlayer.product.service.HighProductService;
import com.howbuy.kyc.common.RiskTestUtil;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.enums.busi.DisCodeEnum;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.common.enums.database.ManagerAttributeEnum;
import com.howbuy.tms.common.enums.database.ProductTypeEnum;
import com.howbuy.tms.common.utils.StringUtils;
import com.howbuy.trade.account.model.account.CustDisAndTxAcctModel;
import com.howbuy.trade.account.service.account.OpenAccountService;
import com.howbuy.trade.common.basecommon.remote.DisCodeInvokerUtils;
import com.howbuy.trade.common.enums.InvestorTypeEnum;
import com.howbuy.trade.common.session.model.TradeSession;
import com.howbuy.trade.common.session.model.UserInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description:账户中心服务
 * @Author: yun.lu
 * Date: 2023/6/29 17:02
 */
@Service
@Slf4j
public class AccCenterService {
    @Autowired
    @Qualifier("queryAccCustInfoFacade")
    private QueryAccCustInfoFacade queryAccCustInfoFacade;
    @Autowired
    @Qualifier("signDataAuthAgreementFacade")
    private SignDataAuthAgreementFacade signDataAuthAgreementFacade;
    @Autowired
    private OpenAccountService openAccountService;
    @Autowired
    @Qualifier("simu.kycInfoFacade")
    private KycInfoFacade kycInfoFacade;
    @Autowired
    @Qualifier("uploadHandSignSealFacade")
    private UploadHandSignSealFacade uploadHandSignSealFacade;
    @Autowired
    @Qualifier("queryHandSignSealFacade")
    private QueryHandSignSealFacade queryHandSignSealFacade;
    @Autowired
    @Qualifier("simu.signElecTxAgreementFacade")
    private SignElecTxAgreementFacade signElecTxAgreementFacade;
    @Autowired
    private SimuCcmsServiceRegister simuCcmsServiceRegister;
    @Autowired
    @Qualifier("simu.highProductService")
    private HighProductService highProductService;
    @Autowired
    private FormService formService;
    @Autowired
    @Qualifier("simu.investorConfirmFacade")
    private InvestorConfirmFacade investorConfirmFacade;

    public static final String SUCCESS_CODE = "0000000";

    private static final String SIMU_TEMPLATE_ID = "2";
    private static final String ZHIGUAN_TEMPLATE_ID = "3";


    /**
     * 查询手写签名base64
     *
     * @param idNo 用户证件号
     * @return 手写签名base64
     */
    public HandSignSealBean queryHandSignSealData(String idNo) {
        if (StringUtils.isBlank(idNo)) {
            log.info("queryHandSignSealData-查询手写签名,idNo是空的");
            return null;
        }
        QueryHandSignSealRequest queryHandSignSealRequest = new QueryHandSignSealRequest();
        queryHandSignSealRequest.setIdNo(idNo);
        queryHandSignSealRequest.setPageSize(1);
        QueryHandSignSealResponse response = queryHandSignSealFacade.execute(queryHandSignSealRequest);
        if (SUCCESS_CODE.equals(response.getRetCode())) {
            List<HandSignSealBean> sealInfoList = response.getSealInfoList();
            if (CollectionUtils.isNotEmpty(sealInfoList)) {
                return sealInfoList.get(0);
            }
            return null;
        } else {
            throw new BizException(ExceptionCodes.FIN_CENTER_SYSTEM_ERROR, "查询手写签名异常");
        }
    }

    /**
     * 好臻电子交易服务协议签署
     */
    public void signElecTxAgreement(String hboneNo, String disCode, String outCode) {
        SignElecTxAgreementRequest signElecTxAgreementRequest = new SignElecTxAgreementRequest();
        signElecTxAgreementRequest.setHboneNo(hboneNo);
        signElecTxAgreementRequest.setDisCode(disCode);
        signElecTxAgreementRequest.setOutletCode(outCode);
        signElecTxAgreementRequest.setElecTxAgreementSignDate(DateUtil.getCurrentDate());
        signElecTxAgreementRequest.setElecTxAgreementSign(YesOrNoEnum.YES.getCode());
        signElecTxAgreementRequest.setAppDt(DateUtil.date2String(new Date(), DateUtil.YYYYMMDD));
        signElecTxAgreementRequest.setAppTm(DateUtil.date2String(new Date(), DateUtil.HHMMSS));
        log.info("AccCenterService-signElecTxAgreement-好臻电子交易服务协议签署,request={}", JSON.toJSONString(signElecTxAgreementRequest));
        SignElecTxAgreementResponse response = signElecTxAgreementFacade.execute(signElecTxAgreementRequest);
        log.info("AccCenterService-signElecTxAgreement-好臻电子交易服务协议签署结果,response={}", JSON.toJSONString(response));
        if (!SUCCESS_CODE.equals(response.getReturnCode())) {
            throw new BizException(ExceptionCodes.FIN_CENTER_SYSTEM_ERROR, "好臻电子交易服务协议签署异常");
        }
    }

    /**
     * 上传手写签名
     *
     * @param signData 签名数据
     * @param idNo     身份证号
     * @return 签名id
     */
    public String uploadHandSignSealFacade(String signData, String idNo) {
        UploadHandSignSealRequest request = new UploadHandSignSealRequest();
        request.setIdNo(idNo);
        request.setSealData(signData);
        UploadHandSignSealResponse response = uploadHandSignSealFacade.execute(request);
        log.info("AccCenterService-uploadHandSignSealFacade-上传手写签名结果,response={}", JSON.toJSONString(response));
        if (SUCCESS_CODE.equals(response.getRetCode()) && response.getSealId() != null) {
            return response.getSealId();
        } else {
            throw new BizException(ExceptionCodes.FIN_CENTER_SYSTEM_ERROR, "上传手写签名异常");
        }
    }

    /**
     * 获取香港数据隔离状态
     *
     * @param hbOneNo 一账通
     * @return 数据授权信息
     */
    public AcctDataAuthDto getAcctDataAuthInfo(String hbOneNo) {
        QueryAccCustInfoRequest queryAccCustInfoRequest = new QueryAccCustInfoRequest();
        queryAccCustInfoRequest.setHboneNo(hbOneNo);
        log.info("查询用户数据权限信息,hbOneNo={}", hbOneNo);
        QueryAccCustInfoResponse response = queryAccCustInfoFacade.execute(queryAccCustInfoRequest);
        log.info("查询用户数据权限信息结果:response={}", JSON.toJSONString(response));
        AcctDataAuthDto acctDataAuthDto = new AcctDataAuthDto();
        if (SUCCESS_CODE.equals(response.getReturnCode()) && response.getAccCustInfo() != null) {
            String isAuth = response.getAccCustInfo().getDataAuthAgreementSign() == null ? YesOrNoEnum.NO.getCode() : response.getAccCustInfo().getDataAuthAgreementSign();
            acctDataAuthDto.setIsDataAuth(isAuth);
            String hkFlag = response.getAccCustInfo().getHkAssetIsolateFlag() == null ? YesOrNoEnum.NO.getCode() : response.getAccCustInfo().getHkAssetIsolateFlag();
            acctDataAuthDto.setIsHkDataQuarantine(hkFlag);
        } else {
            throw new BizException(ExceptionCodes.FIN_CENTER_SYSTEM_ERROR, "校验用户是否授权信息失败");
        }
        log.info("AccCenterService-getDataAuthStatus，检验是否授权结果,hBoneNo={},dataAuthInfo={}", hbOneNo, JSON.toJSONString(acctDataAuthDto));
        return acctDataAuthDto;
    }

    /**
     * 校验用户是否授权
     *
     * @param hBoneNo 一账通账号
     * @return 数据授权协议签署标志 0-未签署；1-已签署
     * @throws BizException
     */
    public String getDataAuthStatus(String hBoneNo) throws BizException {
        log.info("AccCenterService-getDataAuthStatus,检验是否授权,hBoneNo={}", hBoneNo);
        QueryAccCustInfoRequest queryAccCustInfoRequest = new QueryAccCustInfoRequest();
        queryAccCustInfoRequest.setHboneNo(hBoneNo);
        QueryAccCustInfoResponse response = queryAccCustInfoFacade.execute(queryAccCustInfoRequest);
        log.info("AccCenterService-getDataAuthStatus-查询授权结果:queryAccCustInfoFacade={}", JSON.toJSONString(response));
        String isAuth;
        if (SUCCESS_CODE.equals(response.getReturnCode()) && response.getAccCustInfo() != null) {
            isAuth = response.getAccCustInfo().getDataAuthAgreementSign() == null ? YesOrNoEnum.NO.getCode() : response.getAccCustInfo().getDataAuthAgreementSign();
        } else {
            throw new BizException(ExceptionCodes.FIN_CENTER_SYSTEM_ERROR, "校验用户是否授权失败");
        }
        log.info("AccCenterService-getDataAuthStatus，检验是否授权结果,hBoneNo={},isAuth={}", hBoneNo, isAuth);
        return isAuth;
    }

    public Map<String, String> getDataAuthStatusResult(String hBoneNo) throws BizException {
        log.info("AccCenterService-getDataAuthStatusResult,检验是否授权,hBoneNo={}", hBoneNo);
        QueryAccCustInfoRequest queryAccCustInfoRequest = new QueryAccCustInfoRequest();
        queryAccCustInfoRequest.setHboneNo(hBoneNo);
        QueryAccCustInfoResponse response = queryAccCustInfoFacade.execute(queryAccCustInfoRequest);
        log.info("AccCenterService-queryAccCustInfoFacade-查询授权结果:queryAccCustInfoFacade={}", JSON.toJSONString(response));
        Map<String, String> resultMap = new HashMap<>(2);
        if (SUCCESS_CODE.equals(response.getReturnCode()) && response.getAccCustInfo() != null) {
            String isAuth = response.getAccCustInfo().getDataAuthAgreementSign() == null ? YesOrNoEnum.NO.getCode() : response.getAccCustInfo().getDataAuthAgreementSign();
            resultMap.put("isAuth", isAuth);
            resultMap.put("dataSignDate", response.getAccCustInfo().getDataAuthAgreementSignDate());
        } else {
            throw new BizException(ExceptionCodes.FIN_CENTER_SYSTEM_ERROR, "校验用户是否授权失败");
        }
        log.info("AccCenterService-getDataAuthStatusResult，检验是否授权结果,hBoneNo={},resultMap={}", hBoneNo, resultMap);
        return resultMap;
    }

    /**
     * 数据授权
     *
     * @param hBoneNo 一账通号
     */
    public SignDataAuthResult signDataAuth(String hBoneNo) {
        log.info("AccCenterService-signDataAuth,申请授权,hBoneNo={}", hBoneNo);
        SignDataAuthAgreementRequest request = new SignDataAuthAgreementRequest();
        request.setHboneNo(hBoneNo);
        request.setAppDt(DateUtil.date2String(new Date(), DateUtil.YYYYMMDD));
        request.setAppTm(DateUtil.date2String(new Date(), DateUtil.HHMMSS));
        request.setDataAuthAgreementSign(YesOrNoEnum.YES.getCode());
        request.setDataAuthAgreementSignDate(DateUtil.date2String(new Date(), DateUtil.YYYYMMDD));
        SignDataAuthAgreementResponse response = signDataAuthAgreementFacade.execute(request);
        log.info("AccCenterService-signDataAuth,申请结果,response={}", JSON.toJSON(response));
        SignDataAuthResult signDataAuthResult = new SignDataAuthResult();
        signDataAuthResult.setResultCode(response.getReturnCode());
        signDataAuthResult.setResultDesc(response.getDescription());
        return signDataAuthResult;
    }

    /**
     * 查询用户基本信息
     *
     * @param hBoneNo 一账通
     * @return 持仓基本信息
     */
    public AccCustInfoBean queryAccCustBaseInfo(String hBoneNo) {
        if (StringUtils.isBlank(hBoneNo)) {
            log.error("AccCenterService-queryAccCustBaseInfo,查询用户基本信息,一账通是空的");
            return null;
        }
        log.info("AccCenterService-queryAccCustBaseInfo,查询用户基本信息,hBoneNo={}", hBoneNo);
        QueryAccCustInfoRequest queryAccCustInfoRequest = new QueryAccCustInfoRequest();
        queryAccCustInfoRequest.setHboneNo(hBoneNo);
        QueryAccCustInfoResponse response = queryAccCustInfoFacade.execute(queryAccCustInfoRequest);
        log.info("AccCenterService-queryAccCustBaseInfo-查询用户基本信息:response={}", JSON.toJSONString(response));
        if (SUCCESS_CODE.equals(response.getReturnCode()) && response.getAccCustInfo() != null) {
            return response.getAccCustInfo();
        }
        return null;

    }

    /**
     * 查询账户开户信息
     *
     * @param hbOneNo 一账通
     */
    public AccountStatusInfo getAccountStatusInfo(String hbOneNo) {
        log.info("getAccountStatusInfo-查询账户开户信息,hbOneNo={}", hbOneNo);
        AccountStatusInfo accountStatusInfo = new AccountStatusInfo();
        // 1.查询好臻,好买开户状态
        List<String> disCodeList = new ArrayList<>();
        disCodeList.add(DisCodeEnum.HM.getCode());
        disCodeList.add(DisCodeEnum.HZ.getCode());
        Map<String, CustDisAndTxAcctModel> customerDisAndTxAcctModelMap = openAccountService.queryBatchCustDisAndTxAcctInfo(hbOneNo, disCodeList);
        // 2.好臻状态信息检查
        CustDisAndTxAcctModel hzCustomerDisAndTxAcctModel = customerDisAndTxAcctModelMap.get(DisCodeEnum.HZ.getCode());
        if (hzCustomerDisAndTxAcctModel != null && hzCustomerDisAndTxAcctModel.getDisTxAcctModel() != null) {
            // 2.1.好臻账户是否激活
            if (YesOrNoEnum.YES.getCode().equals(hzCustomerDisAndTxAcctModel.getDisCustModel().getActiveStat())) {
                accountStatusInfo.setHasHzAccount(YesOrNoEnum.YES.getCode());
                accountStatusInfo.setHasHzAccountActive(YesOrNoEnum.YES.getCode());
            } else {
                accountStatusInfo.setHasHzAccount(YesOrNoEnum.YES.getCode());
                accountStatusInfo.setHasHzAccountActive(YesOrNoEnum.NO.getCode());
            }
        } else {
            log.info("getAccountStatusInfo-没有好臻账户信息,hbOneNo={},customerDisAndTxAcctModelMap={}", hbOneNo, JSON.toJSONString(customerDisAndTxAcctModelMap));
            accountStatusInfo.setHasHzAccount(YesOrNoEnum.NO.getCode());
            accountStatusInfo.setHasHzAccountActive(YesOrNoEnum.NO.getCode());
        }
        // 3.好买账户状态信息检查
        CustDisAndTxAcctModel hmCustDisAndTxAcctModel = customerDisAndTxAcctModelMap.get(DisCodeEnum.HM.getCode());
        if (hmCustDisAndTxAcctModel == null || hmCustDisAndTxAcctModel.getDisTxAcctModel() == null) {
            log.info("getAccountStatusInfo-没有好买账户信息,hbOneNo={},customerDisAndTxAcctModelMap={}", hbOneNo, JSON.toJSONString(customerDisAndTxAcctModelMap));
            accountStatusInfo.setHasHmAccount(YesOrNoEnum.NO.getCode());
            accountStatusInfo.setHasHmAccountActive(YesOrNoEnum.NO.getCode());
        } else {
            // 3.1.好买账户是否激活
            if (YesOrNoEnum.YES.getCode().equals(hmCustDisAndTxAcctModel.getDisCustModel().getActiveStat())) {
                accountStatusInfo.setHasHmAccount(YesOrNoEnum.YES.getCode());
                accountStatusInfo.setHasHmAccountActive(YesOrNoEnum.YES.getCode());
            } else {
                accountStatusInfo.setHasHmAccount(YesOrNoEnum.YES.getCode());
                accountStatusInfo.setHasHmAccountActive(YesOrNoEnum.NO.getCode());
            }
        }
        log.info("getAccountStatusInfo-查询账户开户信息,hbOneNo={},accountCheckInfo={}", hbOneNo, JSON.toJSONString(accountStatusInfo));
        return accountStatusInfo;
    }

    /**
     * 查询合规融合信息
     *
     * @param hbOneNo 一账通
     * @return 合规融合信息
     */
    public CustomerComplianceStateVo queryComplianceState(String hbOneNo, String disCode) {
        log.info("queryComplianceState-查询合规融合信息-start,hbOneNo={}", hbOneNo);
        if (simuCcmsServiceRegister.getNeedShowHz() == null || YesOrNoEnum.NO.getCode().equals(simuCcmsServiceRegister.getNeedShowHz())) {
            log.info("查询合规信息-开关不展示好臻,仅展示好买,hbOneNo={}", hbOneNo);
            disCode = DisCodeEnum.HM.getCode();
        }
        List<CustomerComplianceStateVo.ComplianceStateInfo> complianceStateInfoLis = new ArrayList<>();
        // 1.查询账户开户信息
        AccountStatusInfo accountStatusInfo = getAccountStatusInfo(hbOneNo);
        // 2.如果好臻开户了,查询好臻合规信息
        if (YesOrNoEnum.YES.getCode().equals(accountStatusInfo.getHasHzAccount()) && YesOrNoEnum.YES.getCode().equals(accountStatusInfo.getHasHzAccountActive()) && (StringUtils.isEmpty(disCode) || DisCodeEnum.HZ.getCode().equals(disCode))) {
            QueryComplianceStateDto queryComplianceStateDto = queryQueryComplianceState(hbOneNo, DisCodeEnum.HZ.getCode());
            if (queryComplianceStateDto != null) {
                CustomerComplianceStateVo.ComplianceStateInfo complianceStateInfo = new CustomerComplianceStateVo.ComplianceStateInfo();
                complianceStateInfo.setQueryComplianceStateDto(queryComplianceStateDto);
                complianceStateInfo.setDisCode(DisCodeEnum.HZ.getCode());
                complianceStateInfo.setSort(2);
                complianceStateInfoLis.add(complianceStateInfo);
            }
        }
        // 2.如果好买开户了,查询好买合规信息
        if (YesOrNoEnum.YES.getCode().equals(accountStatusInfo.getHasHmAccount()) && YesOrNoEnum.YES.getCode().equals(accountStatusInfo.getHasHmAccountActive()) && (StringUtils.isEmpty(disCode) || DisCodeEnum.HM.getCode().equals(disCode))) {
            QueryComplianceStateDto queryComplianceStateDto = queryQueryComplianceState(hbOneNo, DisCodeEnum.HM.getCode());
            if (queryComplianceStateDto != null) {
                CustomerComplianceStateVo.ComplianceStateInfo complianceStateInfo = new CustomerComplianceStateVo.ComplianceStateInfo();
                complianceStateInfo.setQueryComplianceStateDto(queryComplianceStateDto);
                complianceStateInfo.setDisCode(DisCodeEnum.HM.getCode());
                complianceStateInfo.setSort(1);
                complianceStateInfoLis.add(complianceStateInfo);
            }
        }
        if (CollectionUtils.isNotEmpty(complianceStateInfoLis)) {
            complianceStateInfoLis = complianceStateInfoLis.stream().sorted(Comparator.comparing(CustomerComplianceStateVo.ComplianceStateInfo::getSort)).collect(Collectors.toList());
        }
        CustomerComplianceStateVo customerComplianceStateVo = new CustomerComplianceStateVo();
        customerComplianceStateVo.setComplianceStateInfoList(complianceStateInfoLis);
        log.info("queryComplianceState-查询合规融合信息-end,hbOneNo={},customerComplianceStateVo={}", hbOneNo, JSON.toJSONString(customerComplianceStateVo));
        return customerComplianceStateVo;

    }

    /**
     * 查询合规信息
     *
     * @param hbOneNo 一账通
     * @param disCode 分销渠道
     * @return 合规信息
     */
    public QueryComplianceStateDto queryQueryComplianceState(String hbOneNo, String disCode) {
        KycInfoRequest kycInfoRequest = new KycInfoRequest();
        kycInfoRequest.setHboneNo(hbOneNo);
        kycInfoRequest.setDisCode(disCode);
        KycInfoResponse kycInfoResponse = kycInfoFacade.execute(kycInfoRequest);
        QueryComplianceStateDto dto = null;
        if (null != kycInfoResponse && SUCCESS_CODE.equals(kycInfoResponse.getReturnCode())) {
            dto = new QueryComplianceStateDto();
            String elecSignFlag;
            String signFlag;
            String riskFlag;
            String riskLevel = null;
            // 投资者类型
            dto.setInvestorType(kycInfoResponse.getInvestorType());
            dto.setInvestorQualifiedDate(kycInfoResponse.getInvestorQualifiedDate());
            if (!StringUtils.isBlank(disCode) && DisCodeEnum.HZ.getCode().equals(disCode)) {
                signFlag = kycInfoResponse.getSignFlag();
            } else {
                // 私募/资管合格投资者承诺书签署状态
                if (!YesOrNoEnum.YES.getCode().equals(kycInfoResponse.getSignFlag()) || !YesOrNoEnum.YES.getCode().equals(kycInfoResponse.getFundFlag())) {
                    signFlag = YesOrNoEnum.NO.getCode();
                } else {
                    signFlag = YesOrNoEnum.YES.getCode();
                }
            }
            // 电子签名约定书签署标识
            if (null == kycInfoResponse.geteSignatureConfirmation() || !kycInfoResponse.geteSignatureConfirmation()) {
                elecSignFlag = YesOrNoEnum.NO.getCode();
            } else {
                elecSignFlag = YesOrNoEnum.YES.getCode();
            }
            // 风险评测
            if (ExamTypeEnum.HIGH_END.getValue().equals(kycInfoResponse.getRiskToleranceExamType()) || ExamTypeEnum.INSTITUTION.getValue().equals(kycInfoResponse.getRiskToleranceExamType())) {
                if (!StringUtils.isBlank(kycInfoResponse.getRiskToleranceLevel())) {
                    dto.setRiskToleranceLevel(kycInfoResponse.getRiskToleranceLevel());
                    riskLevel = RiskTestUtil.userRiskMap.get(kycInfoResponse.getRiskToleranceLevel());
                }
                dto.setRiskToleranceDate(kycInfoResponse.getRiskToleranceDate());
                if (kycInfoResponse.getRiskToleranceExpire() == null || kycInfoResponse.getRiskToleranceExpire()) {
                    // 风险评测到期
                    riskFlag = "2";
                } else {
                    // 风险评测有效
                    riskFlag = "1";
                }
            } else {
                // 未做过风险评测
                riskFlag = "0";
            }

            dto.setElecSignFlag(elecSignFlag);
            dto.setSignFlag(signFlag);
            dto.setRiskFlag(riskFlag);
            dto.setRiskLevel(riskLevel);
        }
        return dto;
    }

    /**
     * 查询用户风险测评融合信息
     *
     * @param hbOneNo 一账通信息
     * @param dataTypeList 数据类型字段列表，HB000A001 好买  HZ000N001 好甄，为空时返回所有数据
     * @return 用户风险测评融合信息
     */
    public CustomerRiskInfoVo queryCustomerRiskInfo(String hbOneNo, List<String> dataTypeList) {
        log.info("queryCustomerRiskInfo-查询用户风险测评融合信息,hbOneNo={},dataTypeList={}", hbOneNo, JSON.toJSONString(dataTypeList));
        List<CustomerRiskInfoVo.RiskInfoDto> riskInfoDtoLis = new ArrayList<>();

        // 1.查询账户开户信息
        AccountStatusInfo accountStatusInfo = getAccountStatusInfo(hbOneNo);

        // 如果 dataTypeList 是空，返回好买，好甄数据，维持产线逻辑
        if (CollectionUtils.isEmpty(dataTypeList)) {
            // 2.如果好臻开户了,查询好臻账户风险等级信息
            addRiskInfoIfAccountActive(hbOneNo, accountStatusInfo, DisCodeEnum.HZ.getCode(),
                accountStatusInfo.getHasHzAccount(), accountStatusInfo.getHasHzAccountActive(), 2, riskInfoDtoLis);
            // 3.如果好买开户了,查询好买账户风险等级信息
            addRiskInfoIfAccountActive(hbOneNo, accountStatusInfo, DisCodeEnum.HM.getCode(),
                accountStatusInfo.getHasHmAccount(), accountStatusInfo.getHasHmAccountActive(), 1, riskInfoDtoLis);
        } else {
            // 如果 dataTypeList 不为空，根据指定的数据类型查询
            // dataTypeList 包含 DisCodeEnum.HM.getCode()，查询好买账户风险等级信息
            if (dataTypeList.contains(DisCodeEnum.HM.getCode())) {
                addRiskInfoIfAccountActive(hbOneNo, accountStatusInfo, DisCodeEnum.HM.getCode(),
                    accountStatusInfo.getHasHmAccount(), accountStatusInfo.getHasHmAccountActive(), 1, riskInfoDtoLis);
            }

            // dataTypeList 包含 DisCodeEnum.HZ.getCode()，查询好臻账户风险等级信息
            if (dataTypeList.contains(DisCodeEnum.HZ.getCode())) {
                addRiskInfoIfAccountActive(hbOneNo, accountStatusInfo, DisCodeEnum.HZ.getCode(),
                    accountStatusInfo.getHasHzAccount(), accountStatusInfo.getHasHzAccountActive(), 2, riskInfoDtoLis);
            }
        }

        if (CollectionUtils.isNotEmpty(riskInfoDtoLis)) {
            riskInfoDtoLis = riskInfoDtoLis.stream().sorted(Comparator.comparing(CustomerRiskInfoVo.RiskInfoDto::getSort)).collect(Collectors.toList());
        }
        CustomerRiskInfoVo customerRiskInfoVo = new CustomerRiskInfoVo();
        customerRiskInfoVo.setRiskInfoDtoList(riskInfoDtoLis);
        log.info("queryCustomerRiskInfo-查询用户风险测评融合信息-结果,hbOneNo={},dataTypeList={},customerRiskInfoVo={}", hbOneNo, JSON.toJSONString(dataTypeList), JSON.toJSONString(customerRiskInfoVo));
        return customerRiskInfoVo;
    }

    /**
     * 如果账户已开户且激活，添加风险信息到列表
     *
     * @param hbOneNo 一账通信息
     * @param accountStatusInfo 账户状态信息
     * @param disCode 分销代码
     * @param hasAccount 是否有账户
     * @param hasAccountActive 账户是否激活
     * @param sort 排序
     * @param riskInfoDtoList 风险信息列表
     */
    private void addRiskInfoIfAccountActive(String hbOneNo, AccountStatusInfo accountStatusInfo, String disCode,
                                          String hasAccount, String hasAccountActive, int sort,
                                          List<CustomerRiskInfoVo.RiskInfoDto> riskInfoDtoList) {
        if (YesOrNoEnum.YES.getCode().equals(hasAccount) && YesOrNoEnum.YES.getCode().equals(hasAccountActive)) {
            CustomerRiskInfoVo.RiskInfoDto riskInfoDto = getRiskInfoDto(hbOneNo, disCode);
            if (riskInfoDto != null) {
                riskInfoDto.setSort(sort);
                riskInfoDtoList.add(riskInfoDto);
            }
        }
    }

    private CustomerRiskInfoVo.RiskInfoDto getRiskInfoDto(String hbOneNo, String disCode) {
        log.info("getRiskInfoDto-查询用户风险等级信息,hbOneNo={},disCode={}", hbOneNo, disCode);
        KycInfoRequest kycInfoRequest = new KycInfoRequest();
        kycInfoRequest.setHboneNo(hbOneNo);
        kycInfoRequest.setDisCode(disCode);
        KycInfoResponse kycInfoResponse = kycInfoFacade.execute(kycInfoRequest);
        if (kycInfoResponse == null) {
            log.info("getRiskInfoDto-查询用户风险等级信息-查不到kyc信息,hbOneNo={},disCode={}", hbOneNo, disCode);
            return null;
        }
        CustomerRiskInfoVo.RiskInfoDto riskInfoDto = new CustomerRiskInfoVo.RiskInfoDto();
        riskInfoDto.setCustRiskLevel(kycInfoResponse.getRiskToleranceLevel());
        String custRiskLevelStr = RiskTestUtil.userRiskMap.get(kycInfoResponse.getRiskToleranceLevel());
        riskInfoDto.setInvestorType(getInvestorType(kycInfoResponse));
        riskInfoDto.setCustRiskLevelStr(custRiskLevelStr);
        riskInfoDto.setRiskToleranceTerm(kycInfoResponse.getRiskToleranceTerm());
        riskInfoDto.setDisCode(disCode);
        log.info("getRiskInfoDto-查询用户风险等级信息-结果,hbOneNo={},disCode={},riskInfoDto={}", hbOneNo, disCode, JSON.toJSONString(riskInfoDto));
        return riskInfoDto;
    }

    private String getInvestorType(KycInfoResponse kycInfoResponse) {
        //1 普通投资者，2 专业投资者
        String investorType = "";
        if (kycInfoResponse != null) {
            if (InvestorTypeEnum.NORMAL.getValue().equals(kycInfoResponse.getInvestorType())) {
                investorType = "1";
            } else if (InvestorTypeEnum.PRO.getValue().equals(kycInfoResponse.getInvestorType())) {
                investorType = "2";
            }
        }
        return investorType;
    }


    public QueryFormTemplateVo queryQualifiedInvestorCertification(TradeSession loginInfo, String fundCode) {
        String disCode = RemoteParametersProvider.getDistributionCode();
        // 1.先查询产品所属管理人属性
        HighProductBaseInfoModel highProductBaseInfo = highProductService.getHighProductBaseInfo(fundCode);
        if (highProductBaseInfo == null) {
            throw new BizException(ExceptionCodes.HIGH_PRODUCT_PROPERTY_IS_NULL, "根据产品编码查不到产品信息");
        }
        String templateId = getTemplateByFundType(highProductBaseInfo.getFundType());
        if (StringUtils.isBlank(templateId)) {
            throw new BizException(ExceptionCodes.HIGH_PRODUCT_PROPERTY_IS_NULL, "根据产品类型获取不到模版");
        }
        // 2.先查询form表单信息
        QueryFormTemplateVo queryFormTemplateVo = formService.queryFormTemplate(templateId, loginInfo);
        // 3.将选中的选项初始化,查询账户中心,初始化选项
        // 3.1.查询kyc信息,获取合格投资者选项
        KycInfoRequest kycInfoRequest = new KycInfoRequest();
        kycInfoRequest.setHboneNo(loginInfo.getUser().getHboneNo());
        kycInfoRequest.setDisCode(disCode);
        KycInfoResponse kycInfoResponse = kycInfoFacade.execute(kycInfoRequest);
        FromContentInfoDto fromContentInfoDto = getFromContentInfoDto(queryFormTemplateVo, kycInfoResponse);
        queryFormTemplateVo.setFormContent(JSON.toJSONString(fromContentInfoDto));
        // 4.返回json信息
        return queryFormTemplateVo;
    }

    private FromContentInfoDto getFromContentInfoDto(QueryFormTemplateVo queryFormTemplateVo, KycInfoResponse kycInfoResponse) {
        // 1.解析模版,模版内容初始化
        FromContentInfoDto fromContentInfoDto = new FromContentInfoDto(queryFormTemplateVo.getFormContent());
        // 2.模版信息写入
        Map<String, FromCommonComponentInfoDto> componentInfoDtoMap = fromContentInfoDto.getComponentList().stream().collect(Collectors.toMap(FromCommonComponentInfoDto::getBusiCode, x -> x));
        FromCommonComponentInfoDto fromCommonComponentInfoDto = componentInfoDtoMap.get("3");
        FromSelectComponentInfoDto fromSelectComponentInfoDto = (FromSelectComponentInfoDto) fromCommonComponentInfoDto;
        List<FromSelectComponentInfoDto> selectList = fromSelectComponentInfoDto.getSelectList();
        if (SIMU_TEMPLATE_ID.equals(queryFormTemplateVo.getTemplateId())) {
            setSimuTemplateInfo(kycInfoResponse, selectList);

        } else if (ZHIGUAN_TEMPLATE_ID.equals(queryFormTemplateVo.getTemplateId())) {
            setZhiGuanTemplateInfo(kycInfoResponse, selectList);
        }
        return fromContentInfoDto;

    }

    /**
     * 设置资管信息
     */
    private void setZhiGuanTemplateInfo(KycInfoResponse kycInfoResponse, List<FromSelectComponentInfoDto> selectList) {
        for (FromSelectComponentInfoDto selectComponentInfoDto : selectList) {
            if (StringUtils.isNotBlank(kycInfoResponse.getQualifyFlag())) {
                if ("4".equals(selectComponentInfoDto.getBusiCode())) {
                    if ("1".equals(kycInfoResponse.getQualifyFlag())) {
                        selectComponentInfoDto.setSelected(YesOrNoEnum.YES.getCode());
                    } else {
                        selectComponentInfoDto.setSelected(YesOrNoEnum.NO.getCode());
                    }
                } else if ("5".equals(selectComponentInfoDto.getBusiCode())) {
                    if ("2".equals(kycInfoResponse.getQualifyFlag())) {
                        selectComponentInfoDto.setSelected(YesOrNoEnum.YES.getCode());
                    } else {
                        selectComponentInfoDto.setSelected(YesOrNoEnum.NO.getCode());
                    }
                } else if ("6".equals(selectComponentInfoDto.getBusiCode())) {
                    if ("3".equals(kycInfoResponse.getQualifyFlag())) {
                        selectComponentInfoDto.setSelected(YesOrNoEnum.YES.getCode());
                    } else {
                        selectComponentInfoDto.setSelected(YesOrNoEnum.NO.getCode());
                    }
                }
            } else {
                selectComponentInfoDto.setSelected(YesOrNoEnum.NO.getCode());
            }

        }
    }

    /**
     * 设置私募信息
     */
    private void setSimuTemplateInfo(KycInfoResponse kycInfoResponse, List<FromSelectComponentInfoDto> selectList) {
        for (FromSelectComponentInfoDto selectComponentInfoDto : selectList) {
            if ("4".equals(selectComponentInfoDto.getBusiCode())) {
                if (kycInfoResponse.getFinancialAssetConfirmation() != null && kycInfoResponse.getFinancialAssetConfirmation()) {
                    selectComponentInfoDto.setSelected(YesOrNoEnum.YES.getCode());
                } else {
                    selectComponentInfoDto.setSelected(YesOrNoEnum.NO.getCode());
                }
            } else if ("5".equals(selectComponentInfoDto.getBusiCode())) {
                if (kycInfoResponse.getAnnualIncomeConfirmation() != null && kycInfoResponse.getAnnualIncomeConfirmation()) {
                    selectComponentInfoDto.setSelected(YesOrNoEnum.YES.getCode());
                } else {
                    selectComponentInfoDto.setSelected(YesOrNoEnum.NO.getCode());
                }
            } else {
                selectComponentInfoDto.setSelected(YesOrNoEnum.NO.getCode());
            }
        }
    }

    /**
     * 根据产品管理人属性,获取模版id
     */
    private String getTemplateByFundType(String fundType) {
        if (StringUtils.isBlank(fundType)) {
            return null;
        }
        // 私募
        if (ProductTypeEnum.SM.getCode().equals(fundType)) {
            return "2";
        }
        // 资管
        if (ProductTypeEnum.ZHUANHU.getCode().equals(fundType) || ProductTypeEnum.QUANSHANG_XIAOJIHE.getCode().equals(fundType)) {
            return "3";
        }
        return null;
    }

    public SubmitFormResponseVo submitQualifiedInvestorCertification(TradeSession loginInfo, SubmitFormVo submitFormVo) {
        // 1.先调账户中心保存选项结果
        // 解析:FromContentInfoDto
        FromContentInfoDto fromContentInfoDto = new FromContentInfoDto(submitFormVo.getFormContent());
        CustSignCmd custSignCmd = buildCustSignCmd(submitFormVo.getTemplateId(), fromContentInfoDto);
        custSignCmd.setDisCode(RemoteParametersProvider.getDistributionCode());
        if (StringUtils.isBlank(custSignCmd.getQualifyChooseId()) && StringUtils.isBlank(custSignCmd.getSimuChooseId())) {
            throw new BizException(BizErrorEnum.REQUEST_PARAMS_ERROR.getCode(), "合格投资者确认信息表单选中信息为空");
        }
        investorConfirmV2(custSignCmd, loginInfo.getUser());
        // 2.保存表单信息
        return formService.submitForm(loginInfo, submitFormVo);
    }

    private CustSignCmd buildCustSignCmd(String templateId, FromContentInfoDto fromContentInfoDto) {
        List<FromCommonComponentInfoDto> componentList = fromContentInfoDto.getComponentList();
        if (CollectionUtils.isEmpty(componentList)) {
            throw new BizException(BizErrorEnum.REQUEST_PARAMS_ERROR.getCode(), "合格投资者确认信息表单信息为空");
        }
        Map<String, FromCommonComponentInfoDto> componentInfoDtoMap = componentList.stream().collect(Collectors.toMap(FromCommonComponentInfoDto::getBusiCode, x -> x));
        FromCommonComponentInfoDto fromCommonComponentInfoDto = componentInfoDtoMap.get("3");
        CustSignCmd custSignCmd = new CustSignCmd();
        custSignCmd.setDisCode(RemoteParametersProvider.getDistributionCode());
        FromSelectComponentInfoDto fromSelectComponentInfoDto = (FromSelectComponentInfoDto) fromCommonComponentInfoDto;
        List<FromSelectComponentInfoDto> selectList = fromSelectComponentInfoDto.getSelectList();
        if (SIMU_TEMPLATE_ID.equals(templateId)) {
            for (FromSelectComponentInfoDto selectComponentInfoDto : selectList) {
                if ("4".equals(selectComponentInfoDto.getBusiCode())) {
                    if (YesOrNoEnum.YES.getCode().equals(selectComponentInfoDto.getSelected())) {
                        custSignCmd.setSimuChooseId("1");
                    }
                } else if ("5".equals(selectComponentInfoDto.getBusiCode())) {
                    if (YesOrNoEnum.YES.getCode().equals(selectComponentInfoDto.getSelected())) {
                        custSignCmd.setSimuChooseId("2");
                    }
                }
            }

        } else if (ZHIGUAN_TEMPLATE_ID.equals(templateId)) {
            setZhiguanId(custSignCmd, selectList);
        }
        return custSignCmd;
    }

    private void setZhiguanId(CustSignCmd custSignCmd, List<FromSelectComponentInfoDto> selectList) {
        for (FromSelectComponentInfoDto selectComponentInfoDto : selectList) {
            if ("4".equals(selectComponentInfoDto.getBusiCode())) {
                if (YesOrNoEnum.YES.getCode().equals(selectComponentInfoDto.getSelected())) {
                    custSignCmd.setQualifyChooseId("1");
                }
            } else if ("5".equals(selectComponentInfoDto.getBusiCode())) {
                if (YesOrNoEnum.YES.getCode().equals(selectComponentInfoDto.getSelected())) {
                    custSignCmd.setQualifyChooseId("2");
                }
            } else if ("6".equals(selectComponentInfoDto.getBusiCode())) {
                if (YesOrNoEnum.YES.getCode().equals(selectComponentInfoDto.getSelected())) {
                    custSignCmd.setQualifyChooseId("3");
                }
            }

        }
    }


    /**
     * 提交合格投资者确认信息
     *
     * @param custSignCmd
     * @param user
     */
    public void investorConfirm(CustSignCmd custSignCmd, UserInfo user) {
        InvestorConfirmRequest investorConfirmRequest = new InvestorConfirmRequest();
        investorConfirmRequest.setHboneNo(user.getHboneNo());
        // 私募合格投资者
        if (validateChooseId(custSignCmd.getSimuChooseId(), CGISimuConstants.simuChooseArr)) {
            if ("1".equals(custSignCmd.getSimuChooseId())) {
                investorConfirmRequest.setFinancialAssetConfirmation(true);
            } else {
                investorConfirmRequest.setFinancialAssetConfirmation(false);
            }
            if ("2".equals(custSignCmd.getSimuChooseId())) {
                investorConfirmRequest.setAnnualIncomeConfirmation(true);
            } else {
                investorConfirmRequest.setAnnualIncomeConfirmation(false);
            }
        } else {
            throw new BizException(BizErrorEnum.REQUEST_PARAMS_ERROR.getCode(), BizErrorEnum.REQUEST_PARAMS_ERROR.getDesc());
        }
        String disCode = RemoteParametersProvider.getDistributionCode();
        if (!DisCodeEnum.HZ.getCode().equals(disCode)) {
            // 资管合格投资者
            if (validateChooseId(custSignCmd.getQualifyChooseId(), CGISimuConstants.qualifyChooseArr)) {
                investorConfirmRequest.setQualifyFlag(custSignCmd.getQualifyChooseId());
                investorConfirmRequest.setFundFlag("1");
            } else {
                throw new BizException(BizErrorEnum.REQUEST_PARAMS_ERROR.getCode(), BizErrorEnum.REQUEST_PARAMS_ERROR.getDesc());
            }
        }
        DisCodeInvokerUtils.setCommonParameters(investorConfirmRequest);
        investorConfirmFacade.execute(investorConfirmRequest);
    }

    public void investorConfirmV2(CustSignCmd custSignCmd, UserInfo user) {
        InvestorConfirmRequest investorConfirmRequest = new InvestorConfirmRequest();
        investorConfirmRequest.setHboneNo(user.getHboneNo());
        // 私募合格投资者
        if (StringUtils.isNotBlank(custSignCmd.getSimuChooseId())) {
            if ("1".equals(custSignCmd.getSimuChooseId())) {
                investorConfirmRequest.setFinancialAssetConfirmation(true);
            } else {
                investorConfirmRequest.setFinancialAssetConfirmation(false);
            }
            if ("2".equals(custSignCmd.getSimuChooseId())) {
                investorConfirmRequest.setAnnualIncomeConfirmation(true);
            } else {
                investorConfirmRequest.setAnnualIncomeConfirmation(false);
            }
        }else {
            String disCode = RemoteParametersProvider.getDistributionCode();
            KycInfoRequest kycInfoRequest = new KycInfoRequest();
            kycInfoRequest.setHboneNo(user.getHboneNo());
            kycInfoRequest.setDisCode(disCode);
            KycInfoResponse kycInfoResponse = kycInfoFacade.execute(kycInfoRequest);
            investorConfirmRequest.setFinancialAssetConfirmation(kycInfoResponse.getFinancialAssetConfirmation());
            investorConfirmRequest.setAnnualIncomeConfirmation(kycInfoResponse.getAnnualIncomeConfirmation());
        }
        if (StringUtils.isNotBlank(custSignCmd.getQualifyChooseId())) {
            investorConfirmRequest.setQualifyFlag(custSignCmd.getQualifyChooseId());
            investorConfirmRequest.setFundFlag("1");
        }
        DisCodeInvokerUtils.setCommonParameters(investorConfirmRequest);
        investorConfirmFacade.execute(investorConfirmRequest);
    }

    /**
     * 验证合格投资者选项
     *
     * @param chooseId
     * @param chooseArr
     * @return boolean
     */
    public boolean validateChooseId(String chooseId, String[] chooseArr) {
        if (StringUtil.isEmpty(chooseId)) {
            return false;
        }

        // 资管合格投资者条件
        for (int i = 0; i < chooseArr.length; i++) {
            if (chooseId.equals(chooseArr[i])) {
                return true;
            }
        }
        return false;
    }
}
