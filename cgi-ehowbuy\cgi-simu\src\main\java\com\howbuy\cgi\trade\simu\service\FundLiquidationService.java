/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.cgi.trade.simu.service;

import com.howbuy.cgi.common.enums.BizErrorEnum;
import com.howbuy.cgi.common.exception.BizException;
import com.howbuy.cgi.trade.simu.model.dto.FundLiquidationListDTO;
import com.howbuy.cgi.trade.simu.model.dto.FundLiquidationDetailDto;
import com.howbuy.cgi.trade.simu.util.BigDecimalUtils;
import com.howbuy.tms.high.orders.facade.search.queryfundliquidation.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

/**
 * @description: 清仓列表查询服务
 * <AUTHOR>
 * @date 2025/9/4 13:50
 * @since JDK 1.8
 */
@Service
public class FundLiquidationService {

    private static final Logger LOG = LogManager.getLogger(FundLiquidationService.class);

    @Resource
    @Qualifier("simu.queryFundLiquidationListFacade")
    private QueryFundLiquidationListFacade queryFundLiquidationListFacade;

    @Resource
    @Qualifier("simu.queryFundLiquidationDetailFacade")
    private QueryFundLiquidationDetailFacade queryFundLiquidationDetailFacade;

    /**
     * 查询清仓列表
     *
     * @param txAcctNo 交易账号
     * @param hboneNo 一账通号
     * @param channelCodeList 渠道编码列表
     * @return 清仓列表
     */
    public FundLiquidationListDTO queryFundLiquidationList(String txAcctNo, String hboneNo, List<String> channelCodeList) {
        LOG.info("查询清仓列表开始，txAcctNo={}, hboneNo={}, channelCodeList={}", txAcctNo, hboneNo, channelCodeList);
        
        // 构建请求参数
        QueryFundLiquidationListRequest request = new QueryFundLiquidationListRequest();
        request.setTxAcctNo(txAcctNo);
        request.setHbOneNo(hboneNo);
        request.setChannelCodeList(channelCodeList);
        
        try {
            // 调用DUBBO接口
            QueryFundLiquidationListResponse response = queryFundLiquidationListFacade.execute(request);
            
            if (response == null) {
                LOG.error("查询清仓列表响应为空");
                return new FundLiquidationListDTO();
            }

            // 转换响应结果
            FundLiquidationListDTO result = new FundLiquidationListDTO();
            List<FundLiquidationListDTO.FundLiquidationInfo> fundLiquidationList = new ArrayList<>();
            
            if (CollectionUtils.isNotEmpty(response.getFundLiquidationList())) {
                for (QueryFundLiquidationListResponse.FundLiquidationInfo item : response.getFundLiquidationList()) {
                    FundLiquidationListDTO.FundLiquidationInfo info = new FundLiquidationListDTO.FundLiquidationInfo();
                    info.setFundCode(item.getFundCode());
                    info.setFundName(item.getFundName());
                    info.setProductAsset(BigDecimalUtils.formatToThousandths(item.getProductAsset(),2, RoundingMode.HALF_UP));
                    info.setTotalDays(item.getTotalDays());
                    info.setUnit(item.getUnit());
                    fundLiquidationList.add(info);
                }
            }
            // 默认按“清仓收益/累计回款”列倒序排序，即金额大的排在前面
            fundLiquidationList.sort(Comparator.comparing(FundLiquidationListDTO.FundLiquidationInfo::getProductAsset, Comparator.reverseOrder()));
            
            result.setFundLiquidationList(fundLiquidationList);
            
            LOG.info("查询清仓列表成功，返回{}条记录", fundLiquidationList.size());
            return result;
            
        } catch (Exception e) {
            LOG.error("查询清仓列表异常", e);
            throw new BizException(BizErrorEnum.SYSTEM_ERROR.getCode(), BizErrorEnum.SYSTEM_ERROR.getDesc());
        }
    }

    /**
     * 查询清仓产品详情
     *
     * @param mainFundCode 主基金代码
     * @param hboneNo 一账通号
     * @return 清仓产品详情
     */
    public FundLiquidationDetailDto queryFundLiquidationDetail(String mainFundCode, String hboneNo) {
        LOG.info("查询清仓产品详情开始，mainFundCode={}, hboneNo={}", mainFundCode, hboneNo);

        // 构建请求参数
        QueryFundLiquidationDetailRequest request = new QueryFundLiquidationDetailRequest();
        request.setMainFundCode(mainFundCode);
        request.setHbOneNo(hboneNo);

        try {
            // 调用DUBBO接口
            QueryFundLiquidationDetailResponse response = queryFundLiquidationDetailFacade.execute(request);

            if (response == null) {
                LOG.error("查询清仓产品详情响应为空");
                throw new BizException(BizErrorEnum.SYSTEM_ERROR.getCode(), BizErrorEnum.SYSTEM_ERROR.getDesc());
            }
            // 转换响应结果
            FundLiquidationDetailDto result = new FundLiquidationDetailDto();
            result.setFundName(response.getFundName());
            result.setFundCode(response.getFundCode());
            result.setUnit(response.getUnit());
            result.setClearUpIncome(BigDecimalUtils.formatToThousandths(response.getClearUpIncome(),2, RoundingMode.HALF_UP));
            result.setClearUpRate(BigDecimalUtils.formatToThousandths(response.getClearUpRate(),2, RoundingMode.HALF_UP));
            result.setProductType(response.getProductType());
            result.setProductSubType(response.getProductSubType());
            result.setTotalHoldDays(response.getTotalHoldDays());
            result.setAccumIncomeRate(BigDecimalUtils.formatToString(response.getAccumIncomeRate(),4, RoundingMode.DOWN));
            result.setIncomeStatus(response.getIncomeStatus());
            result.setCashCollection(BigDecimalUtils.formatToThousandths(response.getCashCollection(),2, RoundingMode.DOWN));
            result.setCashCollectionProgress(BigDecimalUtils.formatToThousandths(response.getCashCollectionProgress(),4, RoundingMode.DOWN));
            result.setInitInvestCost(BigDecimalUtils.formatToThousandths(response.getInitInvestCost(),2, RoundingMode.DOWN));

            // 转换清仓明细列表
            List<FundLiquidationDetailDto.ClearDetailInfo> clearDetailList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(response.getClearDetailList())) {
                for (QueryFundLiquidationDetailResponse.ClearDetailInfo item : response.getClearDetailList()) {
                    FundLiquidationDetailDto.ClearDetailInfo detailInfo = new FundLiquidationDetailDto.ClearDetailInfo();
                    detailInfo.setFundName(item.getFundName());
                    detailInfo.setFundCode(item.getFundCode());
                    detailInfo.setUnit(item.getUnit());
                    detailInfo.setClearUpIncome(BigDecimalUtils.formatToString(response.getClearUpIncome(),2, RoundingMode.HALF_UP));
                    detailInfo.setClearUpRate(BigDecimalUtils.formatToString(response.getClearUpRate(),2, RoundingMode.HALF_UP));
                    detailInfo.setProductType(item.getProductType());
                    detailInfo.setProductSubType(item.getProductSubType());
                    detailInfo.setTotalHoldDays(item.getTotalHoldDays());
                    detailInfo.setAccumIncomeRate(BigDecimalUtils.formatToString(response.getAccumIncomeRate(),4, RoundingMode.DOWN));
                    detailInfo.setIncomeStatus(item.getIncomeStatus());
                    clearDetailList.add(detailInfo);
                }
            }
            result.setClearDetailList(clearDetailList);

            LOG.info("查询清仓产品详情成功，返回{}条明细记录", clearDetailList.size());
            return result;

        } catch (Exception e) {
            LOG.error("查询清仓产品详情异常", e);
            if (e instanceof BizException) {
                throw e;
            }
            throw new BizException(BizErrorEnum.SYSTEM_ERROR.getCode(), BizErrorEnum.SYSTEM_ERROR.getDesc());
        }
    }
}
