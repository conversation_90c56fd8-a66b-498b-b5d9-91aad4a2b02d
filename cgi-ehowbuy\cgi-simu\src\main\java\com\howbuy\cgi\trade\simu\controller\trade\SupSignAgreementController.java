/**
 * Copyright (c) 2020, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.cgi.trade.simu.controller.trade;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.howbuy.acccenter.facade.trade.validatetxpassword.ValidateTxPasswordFacade;
import com.howbuy.acccenter.facade.trade.validatetxpassword.ValidateTxPasswordRequest;
import com.howbuy.acccenter.facade.trade.validatetxpassword.ValidateTxPasswordResponse;
import com.howbuy.cachemanagement.service.CacheService;
import com.howbuy.cachemanagement.service.CacheServiceImpl;
import com.howbuy.cc.message.SendMsgResult;
import com.howbuy.cgi.common.CGIConstants;
import com.howbuy.cgi.common.enums.AuthMobileVerifyFlagEnum;
import com.howbuy.cgi.common.enums.BizErrorEnum;
import com.howbuy.cgi.common.enums.ReturnCodeEnum;
import com.howbuy.cgi.common.exception.BizException;
import com.howbuy.cgi.common.remote.param.RemoteParametersProvider;
import com.howbuy.cgi.common.util.RequestUtil;
import com.howbuy.cgi.common.validate.ValidateUtil;
import com.howbuy.cgi.trade.ccms.simu.SimuCcmsServiceRegister;
import com.howbuy.cgi.trade.simu.common.CGISimuConstants;
import com.howbuy.cgi.trade.simu.controller.AbstractSimuCGIController;
import com.howbuy.cgi.trade.simu.model.cmd.SendAuthCodeCmd;
import com.howbuy.cgi.trade.simu.model.cmd.SupSignSubmitCmd;
import com.howbuy.cgi.trade.simu.model.dto.*;
import com.howbuy.cgi.trade.simu.service.SendMessageService;
import com.howbuy.cgi.trade.simu.service.relatedaccount.AccCenterService;
import com.howbuy.cgi.trade.simu.util.CacheKeyUtil;
import com.howbuy.cgi.trade.simu.util.RandomUtil;
import com.howbuy.common.security.encrypt.EncryptAlgEnum;
import com.howbuy.common.security.encrypt.EncryptUtil;
import com.howbuy.es.digestsign.authentication.CheckMobileVerificationCodeFacade;
import com.howbuy.es.digestsign.authentication.CheckMobileVerificationCodeRequest;
import com.howbuy.es.webcommon.facade.facade.EsResponse;
import com.howbuy.interlayer.product.enums.YesOrNoEnum;
import com.howbuy.paycommon.model.enums.BusiCodeEnum;
import com.howbuy.payonline.facade.auth.quick.QuickCardAuthRsltFacade;
import com.howbuy.payonline.facade.auth.quick.QuickCardAuthRsltRequest;
import com.howbuy.payonline.facade.auth.quick.QuickCardAuthRsltResponse;
import com.howbuy.tms.common.constant.CacheKeyPrefix;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.enums.busi.IdTypeEnum;
import com.howbuy.tms.common.message.MessageSource;
import com.howbuy.tms.high.orders.facade.search.querysupsignagreementlist.QuerySupSignAgreementListFacade;
import com.howbuy.tms.high.orders.facade.search.querysupsignagreementlist.QuerySupSignAgreementListRequest;
import com.howbuy.tms.high.orders.facade.search.querysupsignagreementlist.QuerySupSignAgreementListResponse;
import com.howbuy.tms.high.orders.facade.trade.sendauthmsg.SendAuthMsgFacade;
import com.howbuy.tms.high.orders.facade.trade.sendauthmsg.SendAuthMsgRequest;
import com.howbuy.tms.high.orders.facade.trade.sendauthmsg.SendAuthMsgResponse;
import com.howbuy.tms.high.orders.facade.trade.sendauthmsg.bean.MessageCenterConterxtBean;
import com.howbuy.tms.high.orders.facade.trade.sendauthmsg.bean.QuickCardAuthContextBean;
import com.howbuy.tms.high.orders.facade.trade.submitsupsignagreement.SubmitSupSignAgreementFacade;
import com.howbuy.tms.high.orders.facade.trade.submitsupsignagreement.SubmitSupSignAgreementRequest;
import com.howbuy.tms.high.orders.facade.trade.submitsupsignagreement.SubmitSupSignAgreementResponse;
import com.howbuy.trade.common.session.model.TradeSession;
import com.howbuy.trade.common.session.model.UserInfo;
import com.howbuy.web.util.WebUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.*;

/**
 * 补签协议
 *
 * <AUTHOR>
 * @date 2020/12/2 9:58
 * @since JDK 1.8
 */
@Controller
public class SupSignAgreementController extends AbstractSimuCGIController {

    private static final Logger log = LogManager.getLogger(SupSignAgreementController.class);
    // e签宝校验短信验证码成功
    private static final String ES_VALIDATE_CODE_SUCC = "0";

    @Autowired
    @Qualifier("simu.querySupSignAgreementListFacade")
    private QuerySupSignAgreementListFacade querySupSignAgreementListFacade;
    @Autowired
    @Qualifier("simu.submitSupSignAgreementFacade")
    private SubmitSupSignAgreementFacade submitSupSignAgreementFacade;
    @Autowired
    @Qualifier("simu.validateTxPasswordFacade")
    private ValidateTxPasswordFacade validateTxPasswordFacade;
    private CacheService cacheService = CacheServiceImpl.getInstance();
    @Autowired
    private SimuCcmsServiceRegister simuCcmsServiceRegister;
    @Autowired
    @Qualifier("simu.sendAuthMsgFacade")
    private SendAuthMsgFacade sendAuthMsgFacade;
    @Autowired
    @Qualifier("simu.checkMobileVerificationCodeFacade")
    private CheckMobileVerificationCodeFacade checkMobileVerificationCodeFacade;
    @Autowired
    @Qualifier("simu.quickCardAuthRsltFacade")
    private QuickCardAuthRsltFacade quickCardAuthRsltFacade;

    @Autowired
    private SendMessageService sendMessageService;
    @Autowired
    private AccCenterService accCenterService;
    private static final String SUCCESS_CODE = "********";

    /**
     * @api {GET} /simu/trade/waitSupSignAgreementList.htm waitSupSignAgreementList
     * @apiVersion 1.0.0
     * @apiGroup SupSignAgreementController
     * @apiName waitSupSignAgreementList
     * @apiDescription 查询待补签协议列表
     * @apiSuccess (响应结果) {Array} fundList 待补签协议列表
     * @apiSuccess (响应结果) {String} fundList.fundCode 产品编码
     * @apiSuccess (响应结果) {String} fundList.fundName 产品名称
     * @apiSuccess (响应结果) {String} fundList.taCode ta编码
     * @apiSuccess (响应结果) {String} fundList.mobile 手机号
     * @apiSuccess (响应结果) {String} fundList.disCode 分销编码
     * @apiSuccess (响应结果) {String} fundList.hkSaleFlag 是否香港产品,1:是;0:不是
     * @apiSuccess (响应结果) {String} fundList.bankCode 银行code
     * @apiSuccess (响应结果) {String} fundList.custBankId 银行id
     * @apiSuccess (响应结果) {Array} fundList.agreementList 协议列表
     * @apiSuccess (响应结果) {String} fundList.agreementList.agreementCode 协议编码
     * @apiSuccess (响应结果) {String} fundList.agreementList.agreementName 协议名称
     * @apiSuccess (响应结果) {String} fundList.agreementList.signEndDtm 签署截止时间
     * @apiSuccess (响应结果) {String} fundList.agreementList.signReason 签署原因
     * @apiSuccess (响应结果) {String} isHasHkProduct 是否有香港产品
     * @apiSuccess (响应结果) {String} isHasHzProduct 是否有好臻产品
     * @apiSuccess (响应结果) {String} isAuth 是否有授权,1:有授权,0:没有授权
     * @apiSuccessExample 响应结果示例
     * {"fundList":[{"bankCode":"MHJ","taCode":"qyXdS2jOC2","fundCode":"8uIwB","mobile":"Z","agreementList":[{"signReason":"khEBVwIwmO","agreementName":"GZ","agreementCode":"lsh7ymI7In","signEndDtm":"fry"}],"custBankId":"i9S","fundName":"PoD"}],"isHasHzProduct":"ob","isHasHkProduct":"Dv"}
     */
    @RequestMapping("/simu/trade/waitSupSignAgreementList.htm")
    public void waitSupSignAgreementList(HttpServletRequest request, HttpServletResponse response) throws Exception {
        // 登录信息
        TradeSession loginDto = getCustSession();
        // 用户信息
        UserInfo user = loginDto.getUser();

        // 查询待补签协议列表
        QuerySupSignAgreementListRequest req = new QuerySupSignAgreementListRequest();
        AcctDataAuthDto acctDataAuthInfo = accCenterService.getAcctDataAuthInfo(user.getHboneNo());
        req.setTxAcctNo(user.getTxAcctNo());
        req.setIsHzAuth(acctDataAuthInfo.getIsDataAuth());
        QuerySupSignAgreementListResponse res = querySupSignAgreementListFacade.execute(req);

        // 构建返回列表
        List<SupSignAgreementListRespDto.SupSignFund> waitSignList;
        if (res == null || res.getFundList().isEmpty()) {
            waitSignList = Collections.emptyList();
        } else {
            waitSignList = buildWaitSignList(res.getFundList());
            // 卡号放缓存
            setSessionBankAcct(user.getTxAcctNo(), res.getFundList());
        }

        // 返回
        SupSignAgreementListRespDto respDto = new SupSignAgreementListRespDto();
        respDto.setFundList(waitSignList);
        respDto.setIsAuth(acctDataAuthInfo.getIsDataAuth());
        if (res != null) {
            respDto.setHasHKProduct(res.getIsHasHkProduct());
            respDto.setHasHZProduct(res.getIsHasHzProduct());
        }
        write(respDto, CGIConstants.RESPONSE_CONTENT_TYPE_CIPHERTEXT, response);
    }

    /**
     * 银行卡号放session
     *
     * @param txAcctNo
     * @param fundList
     * @return void
     * @author: huaqiang.liu
     * @date: 2021/1/12 13:55
     * @since JDK 1.8
     */
    private void setSessionBankAcct(String txAcctNo, List<QuerySupSignAgreementListResponse.SupSignFund> fundList) {
        if (fundList == null || fundList.isEmpty()) {
            return;
        }
        Map<String, String> bankAcctMap = new HashMap<>(fundList.size());
        for (QuerySupSignAgreementListResponse.SupSignFund fund : fundList) {
            bankAcctMap.put(fund.getCustBankId(), fund.getBankAcct());
        }
        String key = CacheKeyUtil.getSupSignBankInfoKey(txAcctNo);
        cacheService.put(key, JSON.toJSONString(bankAcctMap));
    }

    /**
     * 构建待补签协议列表
     *
     * @param fundList
     * @return java.util.List<com.howbuy.cgi.trade.simu.model.dto.SupSignAgreementListRespDto.SupSignFund>
     * @author: huaqiang.liu
     * @date: 2020/12/10 16:38
     * @since JDK 1.8
     */
    private List<SupSignAgreementListRespDto.SupSignFund> buildWaitSignList(List<QuerySupSignAgreementListResponse.SupSignFund> fundList) {
        List<SupSignAgreementListRespDto.SupSignFund> waitSupSignList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(fundList)) {
            for (QuerySupSignAgreementListResponse.SupSignFund fund : fundList) {
                // 基金信息
                SupSignAgreementListRespDto.SupSignFund dto = new SupSignAgreementListRespDto.SupSignFund();
                BeanUtils.copyProperties(fund, dto);
                List<SupSignAgreementListRespDto.SupSignAgreement> agreementList = new ArrayList<>(fund.getAgreementList().size());
                dto.setAgreementList(agreementList);
                waitSupSignList.add(dto);

                // 协议信息
                for (QuerySupSignAgreementListResponse.SupSignAgreement agreement : fund.getAgreementList()) {
                    SupSignAgreementListRespDto.SupSignAgreement agree = new SupSignAgreementListRespDto.SupSignAgreement();
                    BeanUtils.copyProperties(agreement, agree);
                    agreementList.add(agree);
                }
                Collections.sort(agreementList, Comparator.comparing(SupSignAgreementListRespDto.SupSignAgreement::getSignEndDtm).reversed());
            }
        }
        return waitSupSignList;
    }

    /**
     * @api {post} /simu/trade/submitSupSignAgreement.htm 签署待补签协议
     * @apiGroup Sup_Sign_Agreement
     * @APIName /simu/trade/submitSupSignAgreement.htm
     * @apiDescription 签署待补签协议
     * @apiParam {String} fundCode 基金代码
     * @apiParam {String} [sealId] 好臻产品,手写签名id
     * @apiParam {List} agreementCodeList 签署协议代码列表，例：["SS20201218SEE0278256","SS20201218SEE0278252"]
     * @apiParam {String} txPassword 密码
     * @APIParam {String} mobile 手机号
     * @APIParam {String} authenticateCode 快捷鉴权验证码
     * @APIParam {String} authenticateSeriousNo 快捷鉴权流水号
     * @apiSuccess {String} desc 返回描述
     * @apiSuccess {String} code 返回码
     * @apiSuccess {Array} body 数据
     * @apiSuccessExample {json} Response Example
     * { "body": { }, "desc": "成功", "code": "0000" }
     */
    @RequestMapping("/simu/trade/submitSupSignAgreement.htm")
    public void submitSupSignAgreement(HttpServletRequest request, HttpServletResponse response) throws Exception {
        SupSignSubmitDto dto = new SupSignSubmitDto();
        TradeSession loginInfo = getCustSession();
        // 请求cmd
        SupSignSubmitCmd cmd = getCommand(SupSignSubmitCmd.class);
        ValidateUtil.assertValid(cmd);
        String fundCode = cmd.getFundCode();
        String txAcctNo = loginInfo.getUser().getTxAcctNo();
        if (StringUtils.isBlank(cmd.getMobile())) {
            cmd.setMobile(null);
        }
        if(StringUtils.isBlank(cmd.getDisCode())){
            String disCode = RemoteParametersProvider.getDistributionCode();
            cmd.setDisCode(disCode);
        }
        // 验证码校验
        validateAuthCode(cmd, loginInfo, cmd.getAuthenticateCode(), cmd.getAuthenticateSeriousNo());
        // 验证交易密码
        boolean pwdPass = validateTxPassword(txAcctNo, cmd.getTxPassword(), cmd.getDisCode());
        if (!pwdPass) {
            throw new BizException("5220046","密码出错");
        } else {
            // 提交补签
            SubmitSupSignAgreementRequest signReq = new SubmitSupSignAgreementRequest();
            signReq.setTxAcctNo(txAcctNo);
            signReq.setOperIp(WebUtil.getCustIP(request));
            signReq.setDataTrack(UUID.randomUUID().toString());
            signReq.setExternalDealNo(UUID.randomUUID().toString());
            signReq.setFundCode(fundCode);
            signReq.setSealId(cmd.getSealId());
            signReq.setAgreementCodeList(cmd.getAgreementCodeList());
            SubmitSupSignAgreementResponse signRes = submitSupSignAgreementFacade.execute(signReq);
            if (signRes != null) {
                if (SUCCESS_CODE.equals(signRes.getReturnCode())) {
                    dto.setCode("0000");
                    dto.setDesc("成功");
                    dto.setSubsSignResponse(signRes);
                } else {
                    throw new BizException(signRes.getReturnCode(),signRes.getDescription());
                }
            }

        }

        write(dto, CGIConstants.RESPONSE_CONTENT_TYPE_CIPHERTEXT, response);
    }

    /**
     * 校验交易密码
     *
     * @param txAcctNo
     * @param txPwd
     * @return boolean
     * @author: huaqiang.liu
     * @date: 2020/12/14 13:23
     * @since JDK 1.8
     */
    boolean validateTxPassword(String txAcctNo, String txPwd,String disCode) throws UnsupportedEncodingException {
        ValidateTxPasswordRequest request = new ValidateTxPasswordRequest();
        request.setTxAcctNo(txAcctNo);
        request.setTxPassword(EncryptUtil.getInstance(EncryptAlgEnum.Des).encMsg(txPwd.getBytes("utf-8")));
        request.setDisCode(disCode);
        ValidateTxPasswordResponse response = validateTxPasswordFacade.execute(request);
        return YesOrNoEnum.YES.getCode().equals(response.getCheckFlag());
    }

    /**
     * 校验验证码
     *
     * @param cmd
     * @param loginInfo
     * @param authenticateCode
     * @param reqId
     */
    private void validateAuthCode(SupSignSubmitCmd cmd, TradeSession loginInfo, String authenticateCode, String reqId) {
        // 缓存key
        String authCacheKey = CacheKeyUtil.getBuyAuthIdKey(reqId);
        String authRst = cacheService.get(authCacheKey);
        log.info("validateAuthCode|authRst：{}", authRst);
        if (authRst == null) {
            throw new BizException(BizErrorEnum.AUTHENTICATE_CODE_ERROR);
        }
        JSONObject jsonObject = JSON.parseObject(authRst);
        // 鉴权渠道
        String sendMsgChannel = jsonObject.get("sendMsgChannel").toString();
        // 实际验证码
        String authCode = jsonObject.get("authCode").toString();
        // 鉴权方式 2-快捷鉴权 1-修改手机号 3-好买 4-e签宝
        String authMoblieVerifyFlag = jsonObject.get("authMsgType").toString();
        // 鉴权流水
        String authenticateSeriousNo = jsonObject.get("authenticateSeriousNo").toString();
        // 验证码鉴权结果
        boolean authenticateFlag = false;
        if (StringUtils.isNotBlank(authenticateCode)) {
            if (CGISimuConstants.SEND_MSG_CHANNEL_ES.equals(sendMsgChannel)) {
                // e签宝鉴权
                CheckMobileVerificationCodeRequest checkReq = new CheckMobileVerificationCodeRequest();
                checkReq.setIdNo(loginInfo.getUser().getIdNo());
                checkReq.setIdType(loginInfo.getUser().getIdType());
                checkReq.setName(loginInfo.getUser().getCustName());
                checkReq.setCode(cmd.getAuthenticateCode());
                checkReq.setMobile(cmd.getMobile());
                EsResponse checkMobileVerificationCodeResp = checkMobileVerificationCodeFacade.execute(checkReq);
                if (checkMobileVerificationCodeResp != null && ES_VALIDATE_CODE_SUCC.equals(checkMobileVerificationCodeResp.getRetCode())) {
                    authenticateFlag = true;
                }

            } else if (CGISimuConstants.SEND_MSG_CHANNEL_PAY.equals(sendMsgChannel)) {
                // 支付机构短信校验
                log.info("buyInfo.getAuthMoblieVerifyFlag() :" + authMoblieVerifyFlag);
                String serialNo = UUID.randomUUID().toString();
                QuickCardAuthRsltRequest quickCardAuthRsltRequest = new QuickCardAuthRsltRequest();
                quickCardAuthRsltRequest.setApplyDealNo(authenticateSeriousNo);
                quickCardAuthRsltRequest.setDealNo(serialNo);
                quickCardAuthRsltRequest.setDealDate(new Date());
                quickCardAuthRsltRequest.setCaptcha(authenticateCode);
                QuickCardAuthRsltResponse quickCardAuthRsltResponse = quickCardAuthRsltFacade.execute(quickCardAuthRsltRequest);
                if (null != quickCardAuthRsltResponse && "0000000".equals(quickCardAuthRsltResponse.getReturnCode())
                        && "00".equals(quickCardAuthRsltResponse.getAuthState().toString())) {
                    authenticateFlag = true;
                    log.info("{} 快捷鉴权成功（支付）", new Object[]{loginInfo.getUser().getCustName()});
                } else {
                    log.info("{} 快捷鉴权失败（支付）", new Object[]{loginInfo.getUser().getCustName()});
                }
            } else if (CGISimuConstants.SEND_MSG_CHANNEL_HOWBUY.equals(sendMsgChannel)) {
                // 好买短信校验
                log.info("真实验证码：{},实际验证码：{}", authCode, authenticateCode);
                if (authenticateCode.equals(authCode)) {
                    authenticateFlag = true;
                    log.info("验证码验证成功");
                }
            }
        }
        if (!authenticateFlag) {
            throw new BizException(BizErrorEnum.AUTHENTICATE_CODE_ERROR);
        }

        // 清除缓存
        cacheService.remove(authCacheKey);
    }

    /**
     * @api {post} /simu/trade/sendAuthCode.htm 发送鉴权验证码
     * @apiGroup Sup_Sign_Agreement
     * @APIName /simu/trade/sendAuthCode.htm
     * @apiDescription 签署待补签协议
     * @apiParam {String} mobile 手机号
     * @apiParam {String} [bankCode] 银行编号，为空时不做银行鉴权
     * @apiParam {String} [custBankId] 客户银行卡编号
     * @apiSuccess {String} desc 返回描述
     * @apiSuccess {String} code 返回码
     * @apiSuccess {String} authenticateSeriousNo 快捷鉴权流水号，验证的时候需要回传
     * @apiSuccessExample {json} Response Example
     * { "body": { }, "desc": "成功", "code": "0000" }
     */
    @RequestMapping("/simu/trade/sendAuthCode.htm")
    public void sendAuthCode(HttpServletRequest request, HttpServletResponse response) throws IOException {
        // 0-短信、1-语音
        String msgType = RequestUtil.getParameter("msgType");
        if (msgType == null) {
            msgType = YesOrNoEnum.NO.getCode();
        }
        TradeSession loginInfo = getCustSession();
        // 解析请求request
        SendAuthCodeCmd cmd = getCommand(SendAuthCodeCmd.class);
        ValidateUtil.assertValid(cmd);
        String sendMsgReqId = UUID.randomUUID().toString();
        log.info("SupSignAgreementController|getAuthenticateCode|params|cmd:{}, loginInfo:{}, " + "reqId:{}", JSON.toJSONString(cmd),
                JSON.toJSONString(loginInfo), JSON.toJSONString(sendMsgReqId));

        String realAuthMsgType = null;
        if ("0".equals(msgType)) {
            // 获取鉴权验证码
            realAuthMsgType = getAuthenticateCode(loginInfo, cmd, sendMsgReqId);
        } else if ("1".equals(msgType)) {
            StringBuilder authenticateKey = new StringBuilder(CacheKeyPrefix.HIGH_SEND_AUTH_CODE_PREFIX).append("BUY|").append("AUTHID|").append("|").append(sendMsgReqId);
            // 随机数
            String verfyCode = RandomUtil.randomDigit(6, simuCcmsServiceRegister.getDigitsRank());
            // 默认手机号为好买手机号
            SendMsgResult sendMsgResult = sendMessageService.sendVoiceMsg(loginInfo, cmd.getMobile(), verfyCode, true);
            if (sendMsgResult != null && "0".equals(String.valueOf(sendMsgResult.getCode()))) {
                // 发送成功，验证码放入缓存
                Map<String, String> authRst = new HashMap<String, String>();
                // 鉴权通道 0-好买，在验证码校验时，会根据通道选择校验方式
                authRst.put("sendMsgChannel", "0");
                // 鉴权流水
                authRst.put("authenticateSeriousNo", UUID.randomUUID().toString());
                authRst.put("authCode", verfyCode);// 好买短信验证码
                // 鉴权类型 2-快捷鉴权1-修改手机号3-好买 4-e签宝 5-腾讯语音
                authRst.put("authMsgType", AuthMobileVerifyFlagEnum.TENCENT_VOICE_AUTH.getCode());
                // 实际发送鉴权信息类型 2-快捷鉴权1-修改手机号3-好买 4-e签宝 5-腾讯语音
                authRst.put("realAuthMsgType", AuthMobileVerifyFlagEnum.TENCENT_VOICE_AUTH.getCode());
                cacheService.put(authenticateKey.toString(), JSON.toJSONString(authRst));
            } else {
                // 发送失败，抛出异常
                log.error("SendMessageService|sendVoiceMsg|sendTencentVoiceMessage err, sendMsgResult:{}", sendMsgResult);
                throw new BizException(ExceptionCodes.HIGH_ORDER_SYSTEM_ERROR, "发生语音验证码失败");
            }
            // 好买
            realAuthMsgType = AuthMobileVerifyFlagEnum.TENCENT_VOICE_AUTH.getCode();
        } else {
            throw new BizException(ExceptionCodes.HIGH_ORDER_PARAMS_ERROR,
                    MessageSource.getMessageByCode(ExceptionCodes.HIGH_ORDER_PARAMS_ERROR));
        }
        // 返回对象
        AuthenticateDto dto = new AuthenticateDto();
        dto.setAuthenticateSeriousNo(sendMsgReqId);
        dto.setAuthenticateType("");
        dto.setRealAuthMsgType(realAuthMsgType);
        write(dto, CGIConstants.RESPONSE_CONTENT_TYPE_CIPHERTEXT, response);
    }

    /**
     * 获取短信验证码
     *
     * @param loginInfo
     * @param cmd
     * @param sendMsgReqId
     * @return
     */
    private String getAuthenticateCode(TradeSession loginInfo, SendAuthCodeCmd cmd, String sendMsgReqId) {
        // 开关打开,就直接用固定的验证码
        if (StringUtils.isNotBlank(simuCcmsServiceRegister.getMobileAuthMsgUseCfg()) && YesOrNoEnum.YES.getCode().equals(simuCcmsServiceRegister.getMobileAuthMsgUseCfg())) {
            String authCacheKey = CacheKeyUtil.getBuyAuthIdKey(sendMsgReqId);
            Map<String, String> authRst = new HashMap<String, String>();
            authRst.put("sendMsgChannel", CGISimuConstants.SEND_MSG_CHANNEL_HOWBUY);
            authRst.put("authenticateSeriousNo", sendMsgReqId);
            authRst.put("authCode", "123456");
            authRst.put("authMsgType", AuthMobileVerifyFlagEnum.HOWBUY_AUTH.getCode());
            authRst.put("realAuthMsgType", AuthMobileVerifyFlagEnum.HOWBUY_AUTH.getCode());
            cacheService.put(authCacheKey, JSON.toJSONString(authRst));
            return AuthMobileVerifyFlagEnum.HOWBUY_AUTH.getCode();
        }
        String txAcctNo = loginInfo.getUser().getCustNo();
        SendAuthMsgRequest sendAuthMsgRequest = new SendAuthMsgRequest();
        sendAuthMsgRequest.setTxAcctNo(txAcctNo);// 交易账号
        sendAuthMsgRequest.setSendMsgReqId(sendMsgReqId);//发送鉴权信息标识id
        sendAuthMsgRequest.setExternalDealNo(sendMsgReqId);// 外部订单单号
        sendAuthMsgRequest.setMobileExit(true);
        sendAuthMsgRequest.setBankCode(cmd.getBankCode());// 支付银行
        sendAuthMsgRequest.setIdType(loginInfo.getUser().getIdType());
        sendAuthMsgRequest.setMobile(cmd.getMobile());
        sendAuthMsgRequest.setCustName(loginInfo.getUser().getCustName());
        sendAuthMsgRequest.setIdNo(loginInfo.getUser().getIdNo());

        MessageCenterConterxtBean messageCenterConterxtBean = new MessageCenterConterxtBean();// 好买发送鉴权信息参数
        String verfyCode = RandomUtil.randomDigit(6, simuCcmsServiceRegister.getDigitsRank());// 随机数
        messageCenterConterxtBean.setBusinessId(CGISimuConstants.BUSINESSID);// 短信模板
        messageCenterConterxtBean.setCustNo(txAcctNo);// 客户号
        messageCenterConterxtBean.setMobile(cmd.getMobile());// 手机号
        messageCenterConterxtBean.setRandom(verfyCode);// 验证码
        messageCenterConterxtBean.setCustType(CGISimuConstants.CUST_TYPE_EHOWBUY);// 客户类型
        sendAuthMsgRequest.setMessageCenterConterxtBean(messageCenterConterxtBean);

        // 获取卡号
        String key = CacheKeyUtil.getSupSignBankInfoKey(loginInfo.getUser().getTxAcctNo());
        String bankInfo = cacheService.get(key);
        log.info("sendAuthCode|bankInfo：{}", bankInfo);
        String bankAcctNo = null;
        if (bankInfo != null) {
            bankAcctNo = JSON.parseObject(bankInfo).getString(cmd.getCustBankId());
        }

        // 获取银行对应短信鉴权方式
        String bankAuthType = getBankAuthType(cmd.getBankCode());
        if (CGISimuConstants.SEND_MSG_CHANNEL_ES.equals(bankAuthType)) {
            // e签宝
            sendAuthMsgRequest.setAuthMsgType(AuthMobileVerifyFlagEnum.E_AUTH.getCode());
        } else if (CGISimuConstants.SEND_MSG_CHANNEL_HOWBUY.equals(bankAuthType)
                || !IdTypeEnum.IDCARD.getValue().equals(loginInfo.getUser().getIdType())
                || StringUtils.isBlank(bankAcctNo)) {
            // 好买短信
            sendAuthMsgRequest.setAuthMsgType(AuthMobileVerifyFlagEnum.HOWBUY_AUTH.getCode());
        } else {
            // 快捷鉴权
            sendAuthMsgRequest.setAuthMsgType(AuthMobileVerifyFlagEnum.QUICK_AUTH.getCode());
            QuickCardAuthContextBean quickCardAuthContextBean = new QuickCardAuthContextBean();//快捷鉴权参数
            quickCardAuthContextBean.setAuthType(BusiCodeEnum.AUTH_RULA.name());
            quickCardAuthContextBean.setDealDate(new Date());
            quickCardAuthContextBean.setDealNo(UUID.randomUUID().toString());
            quickCardAuthContextBean.setTxAcctNo(loginInfo.getUser().getTxAcctNo());
            quickCardAuthContextBean.setMobile(cmd.getMobile());
            quickCardAuthContextBean.setCustName(loginInfo.getUser().getCustName());
            quickCardAuthContextBean.setIdNo(loginInfo.getUser().getIdNo());
            quickCardAuthContextBean.setIdType(loginInfo.getUser().getIdType());
            quickCardAuthContextBean.setBankAcct(bankAcctNo);
            quickCardAuthContextBean.setBankCode(cmd.getBankCode());
            sendAuthMsgRequest.setQuickCardAuthContextBean(quickCardAuthContextBean);
        }

        SendAuthMsgResponse sendAuthMsgResponse = sendAuthMsgFacade.execute(sendAuthMsgRequest);
        if (sendAuthMsgResponse != null && !ReturnCodeEnum.SUCC_TMS.getCode().equals(sendAuthMsgResponse.getReturnCode())) {
            throw new BizException(sendAuthMsgResponse.getReturnCode(), sendAuthMsgResponse.getDescription());
        }

        // 获取实际发送鉴权信息类型
        String authRstStr = null;
        try {
            authRstStr = cacheService.get(CacheKeyPrefix.HIGH_SEND_AUTH_CODE_PREFIX + "BUY|" + "AUTHID|" + "|" + sendMsgReqId);
        } catch (Exception e) {
            log.error("SupSignAgreementController|getAuthenticateCode|get authRstStr err :", e);
        }
        log.info("SupSignAgreementController|getAuthenticateCode|authRstStr：{}", authRstStr);
        if (authRstStr != null) {
            JSONObject jsonObject = JSON.parseObject(authRstStr);
            // 实际发送鉴权信息类型
            Object realAuthMsgType = jsonObject.get("realAuthMsgType");
            if (realAuthMsgType != null && StringUtils.isNotEmpty(realAuthMsgType.toString())) {
                return realAuthMsgType.toString();
            } else {
                return sendAuthMsgRequest.getAuthMsgType();
            }
        } else {
            return sendAuthMsgRequest.getAuthMsgType();
        }
    }

    /**
     * 获取鉴权方式
     *
     * @param bankCode
     * @return java.lang.String
     * @author: huaqiang.liu
     * @date: 2020/12/14 14:18
     * @since JDK 1.8
     */
    private String getBankAuthType(String bankCode) {
        // 未指定银行时，走好买鉴权
        if (StringUtils.isBlank(bankCode)) {
            return CGISimuConstants.SEND_MSG_CHANNEL_HOWBUY;
        }
        // 默认鉴权方式取配置
        String authType = simuCcmsServiceRegister.getAuthChannel();

        // 检查好买鉴权短信银行
        String bankCodeFilter = simuCcmsServiceRegister.getBankCodeFilter();
        log.info("SupSignAgreementController|getBankAuthType|bankCodeFilter:{}", bankCodeFilter);
        Set<BankCodeAndAuthTypeDto> bankCodeFilterSet = new HashSet<BankCodeAndAuthTypeDto>();
        if (StringUtils.isEmpty(bankCodeFilter)) {
            return authType;
        }
        String[] bankCodeArr = bankCodeFilter.trim().split("\\|");
        if (bankCodeArr.length <= 0) {
            return authType;
        }

        // 解析好买鉴权短信银行
        for (String code : bankCodeArr) {
            String[] bankCodeAndAuthTypeArr = code.trim().split("_");
            if (bankCodeAndAuthTypeArr.length >= 2) {
                BankCodeAndAuthTypeDto bankCodeAndAuthType = new BankCodeAndAuthTypeDto();
                bankCodeAndAuthType.setBankCode(bankCodeAndAuthTypeArr[0]);
                bankCodeAndAuthType.setAuthType(bankCodeAndAuthTypeArr[1]);
                bankCodeFilterSet.add(bankCodeAndAuthType);

            } else {
                log.info("bankCodeAndAuthType length < 2 , bankCode:{}", code);
            }
        }

        // 根据好买鉴权短信银行取鉴权方式
        if (CollectionUtils.isNotEmpty(bankCodeFilterSet)) {
            for (BankCodeAndAuthTypeDto bankCodeAndAuthType : bankCodeFilterSet) {
                if (bankCodeAndAuthType.getBankCode().equals(bankCode)) {
                    return bankCodeAndAuthType.getAuthType();
                }
            }
        }
        return authType;
    }
}