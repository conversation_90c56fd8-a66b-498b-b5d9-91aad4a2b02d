spring.application.name=cgi-simu-container
spring.cloud.nacos.config.server-addr=nacos1.inner.howbuy.com:8848,nacos2.inner.howbuy.com:8848,nacos3.inner.howbuy.com:8848
spring.cloud.nacos.config.namespace=it06
spring.cloud.nacos.config.group=4.8.19
spring.cloud.nacos.config.file-extension=properties
spring.profiles.active=dev


#健康检查
management.server.port=39999
management.health.db.enabled=false
management.health.redis.enabled=false
management.endpoints.web.exposure.include=*
management.endpoint.health.show-details=ALWAYS
dubbo.application.service-discovery.migration=FORCE_INTERFACE
spring.freemarker.checkTemplateLocation=false
spring.freemarker.prefer-file-system-access=false

# Dubbo序列化安全配置
# 设置序列化检查模式为WARN，允许反序列化但会记录警告日志
dubbo.application.serialize-check-status=WARN

#logging.config=classpath:log4j2.xml
