/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.cgi.trade.simu.controller.query.fundliquidation;

import com.howbuy.cgi.common.CGIConstants;
import com.howbuy.cgi.common.enums.BizErrorEnum;
import com.howbuy.cgi.common.exception.BizException;
import com.howbuy.cgi.trade.simu.controller.AbstractSimuCGIController;
import com.howbuy.cgi.trade.simu.model.dto.FundLiquidationListDTO;
import com.howbuy.cgi.trade.simu.model.dto.FundLiquidationDetailDto;
import com.howbuy.cgi.trade.simu.service.FundLiquidationService;
import com.howbuy.trade.common.session.model.TradeSession;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

/**
 * @description: 清仓列表查询控制器
 * <AUTHOR>
 * @date 2025/9/4 13:34
 * @since JDK 1.8
 */
@Controller
public class FundLiquidationController extends AbstractSimuCGIController {

    private static final Logger LOG = LogManager.getLogger(FundLiquidationController.class);

    @Autowired
    private FundLiquidationService fundLiquidationService;

    /**
     * @api {GET} /simu/fund/liquidation/list.htm list
     * @apiVersion 1.0.0
     * @apiGroup FundLiquidationController
     * @apiName list
     * @apiDescription 清仓列表查询接口
     * @apiParam {String} channelCodeList 渠道编码 1:好买分销 2:好臻分销 3:好买香港分销,支持多渠道查询
     * @apiParamExample 请求参数示例
     * channelCodeList=1,2,3
     * @apiSuccess (响应结果) {String} fundName 基金名称
     * @apiSuccess (响应结果) {String} fundCode 基金代码
     * @apiSuccess (响应结果) {String} productAsset 产品收益
     * @apiSuccess (响应结果) {String} totalDays 累计天数
     * @apiSuccessExample 响应结果示例
     * {"fundLiquidationList":[{"fundName":"测试基金","fundCode":"TEST001","productAsset":"1,000.00","totalDays":"365","unit":"元"}]}
     */
    @RequestMapping("/simu/fund/liquidation/list.htm")
    public void list(HttpServletRequest request, HttpServletResponse response) throws Exception {
        TradeSession loginInfo = this.getCustSession();
        if (null == loginInfo || null == loginInfo.getUser()) {
            LOG.info("loginInfo is null!");
            throw new BizException(BizErrorEnum.NEED_RELOGIN.getCode(), BizErrorEnum.NEED_RELOGIN.getDesc());
        }

        // 获取渠道编码参数
        String channelCodeListStr = getString("channelCodeList");
        if (StringUtils.isBlank(channelCodeListStr)) {
            LOG.error("channelCodeList参数不能为空");
            throw new BizException(BizErrorEnum.PARAMS_NULL_IS_ERROR.getCode(), "渠道编码不能为空");
        }

        // 解析渠道编码列表
        List<String> channelCodeList = Arrays.asList(channelCodeListStr.split(","));

        // 调用Service层查询清仓列表
        FundLiquidationListDTO result = fundLiquidationService.queryFundLiquidationList(
            loginInfo.getUser().getTxAcctNo(),
            loginInfo.getUser().getHboneNo(),
            channelCodeList
        );

        write(result, CGIConstants.RESPONSE_CONTENT_TYPE_CIPHERTEXT, response);
    }

    /**
     * @api {GET} /simu/fund/liquidation/detail.htm detail
     * @apiVersion 1.0.0
     * @apiGroup FundLiquidationController
     * @apiName detail
     * @apiDescription 清仓产品详情查询接口
     * @apiParam {String} mainFundCode 主基金代码
     * @apiParamExample 请求参数示例
     * mainFundCode=TEST001
     * @apiSuccess (响应结果) {String} fundName 基金名称
     * @apiSuccess (响应结果) {String} fundCode 基金代码
     * @apiSuccess (响应结果) {String} unit 单位
     * @apiSuccess (响应结果) {String} clearUpIncome 清仓收益
     * @apiSuccess (响应结果) {String} clearUpRate 清仓后涨跌幅
     * @apiSuccess (响应结果) {String} productType 产品类型
     * @apiSuccess (响应结果) {String} productSubType 产品子类型
     * @apiSuccess (响应结果) {String} totalHoldDays 累计持有天数
     * @apiSuccess (响应结果) {String} accumIncomeRate 累计收益率
     * @apiSuccess (响应结果) {String} incomeStatus 收益计算中标签 1:有计算中标签 0:无计算中标签
     * @apiSuccess (响应结果) {String} cashCollection 回款金额
     * @apiSuccess (响应结果) {String} cashCollectionProgress 回款进度
     * @apiSuccess (响应结果) {String} initInvestCost 初始投资成本
     * @apiSuccess (响应结果) {Array} clearDetailList 清仓明细列表
     * @apiSuccess (响应结果) {String} clearDetailList.fundName 基金名称
     * @apiSuccess (响应结果) {String} clearDetailList.fundCode 基金代码
     * @apiSuccess (响应结果) {String} clearDetailList.unit 单位
     * @apiSuccess (响应结果) {String} clearDetailList.clearUpIncome 清仓收益
     * @apiSuccess (响应结果) {String} clearDetailList.clearUpRate 清仓后涨跌幅
     * @apiSuccess (响应结果) {String} clearDetailList.productType 产品类型
     * @apiSuccess (响应结果) {String} clearDetailList.productSubType 产品子类型
     * @apiSuccess (响应结果) {Integer} clearDetailList.totalHoldDays 累计持有天数
     * @apiSuccess (响应结果) {String} clearDetailList.accumIncomeRate 累计收益率
     * @apiSuccess (响应结果) {String} clearDetailList.incomeStatus 收益计算中标签
     * @apiSuccessExample 响应结果示例
     * {"fundName":"测试基金","fundCode":"TEST001","unit":"元","clearUpIncome":"1000.00","clearUpRate":"10.50","productType":"11","productSubType":"4","totalHoldDays":"365","accumIncomeRate":"15.20","incomeStatus":"0","cashCollection":"5000.00","cashCollectionProgress":"80.00","initInvestCost":"10000.00","clearDetailList":[]}
     */
    @RequestMapping("/simu/fund/liquidation/detail.htm")
    public void detail(HttpServletRequest request, HttpServletResponse response) throws Exception {
        TradeSession loginInfo = this.getCustSession();
        if (null == loginInfo || null == loginInfo.getUser()) {
            LOG.info("loginInfo is null!");
            throw new BizException(BizErrorEnum.NEED_RELOGIN.getCode(), BizErrorEnum.NEED_RELOGIN.getDesc());
        }

        // 获取主基金代码参数
        String mainFundCode = getString("mainFundCode");
        if (StringUtils.isBlank(mainFundCode)) {
            LOG.error("mainFundCode参数不能为空");
            throw new BizException(BizErrorEnum.PARAMS_NULL_IS_ERROR.getCode(), "主基金代码不能为空");
        }

        // 调用Service层查询清仓产品详情
        FundLiquidationDetailDto result = fundLiquidationService.queryFundLiquidationDetail(
            mainFundCode,
            loginInfo.getUser().getHboneNo()
        );

        write(result, CGIConstants.RESPONSE_CONTENT_TYPE_CIPHERTEXT, response);
    }
}
