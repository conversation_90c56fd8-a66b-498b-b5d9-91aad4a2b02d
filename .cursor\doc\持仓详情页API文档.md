# 持仓详情页API文档

## 接口概述

持仓详情页接口用于查询用户指定基金的详细持仓信息，包括基金基本信息、收益情况、在途订单等。

## 接口信息

- **接口名称**: 持仓详情页查询
- **接口地址**: `/simu/user/balancedetail.htm`
- **请求方式**: POST
- **内容类型**: application/x-www-form-urlencoded
- **响应格式**: JSON (加密)
- **是否需要登录**: 是

## 请求参数

| 参数名 | 类型 | 必填 | 描述 | 示例值 |
|--------|------|------|------|--------|
| fundCode | String | 是 | 基金代码 | "HF000001" |
| hboneNo | String | 否 | 一账通号，不传则使用登录用户的 | "12345678" |
| custNo | String | 否 | 客户号，不传则使用登录用户的 | "CUST001" |

## 响应参数

### 主要字段

| 参数名 | 类型 | 描述 | 示例值 |
|--------|------|------|--------|
| abnormalState | String | 是否存在异常，1:是,0:否 | "0" |
| isNeedAuth | String | 是否需要授权，1:是,0:否 | "0" |
| serverDate | String | 服务器日期(yyyyMMdd) | "20250909" |
| isDataAuth | String | 是否签署数据授权，1:已授权,0:没有授权 | "1" |
| isHkDataQuarantine | String | 是否香港数据隔离，1:是,0:否 | "0" |
| isnologinBind | String | 是否不绑定自登录，1:不绑定,0:绑定 | "0" |
| highFundInvPlanFlag | String | 是否含有私募定投，1:是,0:否 | "0" |
| showShouYi | String | 是否展示收益分析入口 | "1" |
| showLiCai | String | 是否展示理财分析入口 | "1" |
| balanceFund | Object | 持仓基金信息 | 见下方详细说明 |
| noTxAcctNo | String | 公募未开户，1:是,0:否 | "0" |

### balanceFund 对象结构

| 参数名 | 类型 | 描述 | 示例值 |
|--------|------|------|--------|
| fundCode | String | 基金代码 | "HF000001" |
| strategy | String | 产品策略 | "股票型" |
| fundNameAbbr | String | 基金简称 | "测试私募基金" |
| currentAsset | BigDecimal | 当前收益(人民币) | 105000.00 |
| currentAssetStr | String | 当前收益展示字段 | "105,000.00" |
| totalAsset | BigDecimal | 总资产 | 1105000.00 |
| totalAssetStr | String | 总资产展示字段 | "1,105,000.00" |
| yieldRate | BigDecimal | 持仓收益率 | 0.05 |
| yieldRateStr | String | 持仓收益率展示字段 | "5.00%" |
| cashCollection | BigDecimal | 回款金额 | 50000.00 |
| cashCollectionStr | String | 回款金额展示字段 | "50,000.00" |
| cashCollectionRatio | String | 回款比例 | "4.52%" |
| currency | String | 币种 | "CNY" |
| currencyStr | String | 币种展示字段 | "人民币" |
| navDivFlag | String | 净值分红标识，0-否，1-是 | "0" |
| valueDate | String | 起息日 | "20240101" |
| dueDate | String | 到期日 | "20271231" |
| productType | String | 产品类型，7-一对多专户,11-私募 | "11" |
| productSubType | String | 产品二级类型 | "4" |
| abnormalState | String | 异常标识，0-否，1-是 | "0" |
| crisisFlag | String | 清算标识，0-否，1-是 | "0" |
| qianXiFlag | String | 千禧年待投产品标识，0-否，1-是 | "0" |
| marketValue | BigDecimal | 总市值 | 1105000.00 |
| marketValueStr | String | 总市值展示字段 | "1,105,000.00" |
| nav | BigDecimal | 最新净值 | 1.105 |
| navStr | String | 最新净值展示字段 | "1.1050" |
| navDt | String | 最新净值日期 | "0908" |
| incomeCalStat | String | 收益计算状态，0-计算中，1-计算成功 | "1" |
| productDuration | String | 产品存续期限 | "5+3+2" |
| cpqxsm | String | 产品期限说明 | "封闭期5年，退出期3年，延长期2年" |
| stageEstablishFlag | String | 是否分期成立，1-是，0-否 | "0" |
| paidTotalAmt | BigDecimal | 实缴金额 | 1000000.00 |
| paidTotalAmtStr | String | 实缴金额展示字段 | "1,000,000.00" |
| paidSubTotalRatio | String | 实缴百分比 | "100.00%" |
| dayIncome | BigDecimal | 日收益 | 500.00 |
| dayIncomeStr | String | 日收益展示字段 | "500.00" |
| accumIncome | BigDecimal | 累计收益 | 105000.00 |
| accumIncomeStr | String | 累计收益展示字段 | "105,000.00" |
| incomeLatestDay | String | 最新收益日期 | "0908" |
| balanceVol | BigDecimal | 持仓份额 | 1000000.00 |
| balanceVolStr | String | 持仓份额展示字段 | "1,000,000.00" |
| unitBalanceCostExFee | BigDecimal | 单位持仓成本(当前成本) | 1.00 |
| unitBalanceCostExFeeRmb | BigDecimal | 单位持仓成本(人民币) | 1.00 |
| unitBalanceCostExFeeRmbStr | String | 单位持仓成本展示字段 | "1.0000" |
| balanceFundItemDtlList | Array | 子产品信息列表(分期成立时) | [] |
| balanceFundUnConfirmInfo | Object | 在途订单信息 | null |
| balanceFundUnRefundInfo | Object | 待资金到账订单信息 | null |
| custRepurchaseProtocol | Object | 客户复购协议 | null |

## 请求示例

```bash
curl -X POST "https://api.example.com/simu/user/balancedetail.htm" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -H "Cookie: JSESSIONID=xxx" \
  -d "fundCode=HF000001&hboneNo=12345678&custNo=CUST001"
```

## 响应示例

### 成功响应
```json
{
  "abnormalState": "0",
  "isNeedAuth": "0",
  "serverDate": "20250909",
  "isDataAuth": "1",
  "isHkDataQuarantine": "0",
  "isnologinBind": "0",
  "highFundInvPlanFlag": "0",
  "showShouYi": "1",
  "showLiCai": "1",
  "balanceFund": {
    "fundCode": "HF000001",
    "strategy": "股票型",
    "fundNameAbbr": "测试私募基金",
    "currentAsset": 105000.00,
    "currentAssetStr": "105,000.00",
    "totalAsset": 1105000.00,
    "totalAssetStr": "1,105,000.00",
    "yieldRate": 0.05,
    "yieldRateStr": "5.00%",
    "cashCollection": 50000.00,
    "cashCollectionStr": "50,000.00",
    "cashCollectionRatio": "4.52%",
    "currency": "CNY",
    "currencyStr": "人民币",
    "navDivFlag": "0",
    "valueDate": "20240101",
    "dueDate": "20271231",
    "productType": "11",
    "productSubType": "4",
    "abnormalState": "0",
    "crisisFlag": "0",
    "qianXiFlag": "0",
    "marketValue": 1105000.00,
    "marketValueStr": "1,105,000.00",
    "nav": 1.105,
    "navStr": "1.1050",
    "navDt": "0908",
    "incomeCalStat": "1",
    "productDuration": "5+3+2",
    "cpqxsm": "封闭期5年，退出期3年，延长期2年",
    "stageEstablishFlag": "0",
    "paidTotalAmt": 1000000.00,
    "paidTotalAmtStr": "1,000,000.00",
    "paidSubTotalRatio": "100.00%",
    "dayIncome": 500.00,
    "dayIncomeStr": "500.00",
    "accumIncome": 105000.00,
    "accumIncomeStr": "105,000.00",
    "incomeLatestDay": "0908",
    "balanceVol": 1000000.00,
    "balanceVolStr": "1,000,000.00",
    "unitBalanceCostExFee": 1.00,
    "unitBalanceCostExFeeRmb": 1.00,
    "unitBalanceCostExFeeRmbStr": "1.0000",
    "balanceFundItemDtlList": [],
    "balanceFundUnConfirmInfo": null,
    "balanceFundUnRefundInfo": null,
    "custRepurchaseProtocol": null
  },
  "noTxAcctNo": "0"
}
```

### 分期成立产品响应示例
```json
{
  "abnormalState": "0",
  "isNeedAuth": "0",
  "serverDate": "20250909",
  "isDataAuth": "1",
  "isHkDataQuarantine": "0",
  "highFundInvPlanFlag": "0",
  "balanceFund": {
    "fundCode": "HF000002",
    "fundNameAbbr": "分期成立测试基金",
    "stageEstablishFlag": "1",
    "currentAsset": 210000.00,
    "totalAsset": 2210000.00,
    "yieldRate": 0.06,
    "balanceFundItemDtlList": [
      {
        "fundCode": "HF000002",
        "fundSubCode": "HF000002-1",
        "fundNameAbbr": "分期成立测试基金一期",
        "currentAsset": 105000.00,
        "totalAsset": 1105000.00,
        "yieldRate": 0.05
      },
      {
        "fundCode": "HF000002",
        "fundSubCode": "HF000002-2",
        "fundNameAbbr": "分期成立测试基金二期",
        "currentAsset": 105000.00,
        "totalAsset": 1105000.00,
        "yieldRate": 0.06
      }
    ]
  }
}
```

## 错误码说明

| 错误码 | 错误信息 | 说明 |
|--------|----------|------|
| 10001 | 需要重新登录 | 用户未登录或登录已过期 |
| 10002 | 参数错误 | 必填参数缺失或格式错误 |
| 10003 | 数据授权异常 | 用户数据授权状态异常 |
| 10004 | 系统异常 | 系统内部错误 |

## 业务规则说明

### 分期成立产品处理
- 当产品为分期成立时，`stageEstablishFlag` 为 "1"
- 母基金层面的数值字段为各子基金的汇总值
- 收益率字段取各子基金中的最大值
- `balanceFundItemDtlList` 包含所有子基金的详细信息

### 异常状态判断
- 只要有一个子基金存在异常，整体异常状态就为 "1"
- 收益计算状态只要有一个为计算中，整体状态就为计算中

### 数据授权
- 根据用户的数据授权状态过滤敏感数据
- 支持香港数据隔离机制

## 注意事项

1. **登录要求**: 接口需要用户登录，未登录会返回需要重新登录错误
2. **参数优先级**: 如果传入了 hboneNo 和 custNo，优先使用传入值；否则使用登录用户信息
3. **数据精度**: 金额字段保留2位小数，收益率字段保留4位小数
4. **响应加密**: 响应内容会进行加密处理，客户端需要相应解密
5. **缓存策略**: 产品基本信息等相对稳定的数据会有缓存，实时性要求不高

## 版本历史

| 版本 | 日期 | 变更内容 |
|------|------|----------|
| 1.0 | 2025-09-09 | 初始版本，实现基本功能 |
