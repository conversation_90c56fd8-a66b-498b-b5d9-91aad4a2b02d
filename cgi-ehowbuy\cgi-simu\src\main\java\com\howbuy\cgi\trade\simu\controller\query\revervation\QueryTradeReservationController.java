package com.howbuy.cgi.trade.simu.controller.query.revervation;


import com.github.pagehelper.util.StringUtil;
import com.howbuy.cgi.common.CGIConstants;
import com.howbuy.cgi.common.remote.param.RemoteParametersProvider;
import com.howbuy.cgi.trade.simu.controller.AbstractSimuCGIController;
import com.howbuy.cgi.trade.simu.model.dto.AcctDataAuthDto;
import com.howbuy.cgi.trade.simu.model.dto.CustReservationDto;
import com.howbuy.cgi.trade.simu.model.dto.PrebookDTO;
import com.howbuy.cgi.trade.simu.model.vo.DoubleTradeInfoVo;
import com.howbuy.cgi.trade.simu.service.DoubleTradeService;
import com.howbuy.cgi.trade.simu.service.relatedaccount.AccCenterService;
import com.howbuy.interlayer.product.model.HighProductBaseInfoModel;
import com.howbuy.interlayer.product.service.HighProductService;
import com.howbuy.simu.dto.base.product.SmxxDto;
import com.howbuy.tms.common.enums.busi.*;
import com.howbuy.tms.common.utils.DateUtils;
import com.howbuy.tms.common.utils.MoneyUtil;
import com.howbuy.tms.high.orders.facade.search.querybuyfundstatus.QueryBuyFundStatusFacade;
import com.howbuy.tms.high.orders.facade.search.querybuyfundstatus.QueryBuyFundStatusRequest;
import com.howbuy.tms.high.orders.facade.search.querybuyfundstatus.QueryBuyFundStatusResponse;
import com.howbuy.tms.high.orders.facade.search.querybuyfundstatus.QueryBuyFundStatusResponse.BuyFundStatusBean;
import com.howbuy.tms.high.orders.facade.search.queryprebooklist.QueryPreBookListFacade;
import com.howbuy.tms.high.orders.facade.search.queryprebooklist.QueryPreBookListRequest;
import com.howbuy.tms.high.orders.facade.search.queryprebooklist.QueryPreBookListResponse;
import com.howbuy.tms.high.orders.facade.search.queryprebooklist.QueryPreBookListResponse.PreBookListBean;
import com.howbuy.tms.high.orders.facade.search.queryredeemfundstatus.QueryRedeemFundStatusFacade;
import com.howbuy.tms.high.orders.facade.search.queryredeemfundstatus.QueryRedeemFundStatusRequest;
import com.howbuy.tms.high.orders.facade.search.queryredeemfundstatus.QueryRedeemFundStatusResponse;
import com.howbuy.tms.high.orders.facade.search.queryredeemfundstatus.QueryRedeemFundStatusResponse.RedeemFundStatusBean;
import com.howbuy.trade.account.model.account.DoubleTradeForPlatModel;
import com.howbuy.trade.account.service.account.CrmCustServiceImpl;
import com.howbuy.trade.common.session.model.TradeSession;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

@Controller
public class QueryTradeReservationController extends AbstractSimuCGIController {

    @Autowired
    @Qualifier("simu.queryPreBookListFacade")
    private QueryPreBookListFacade queryPreBookListFacade;

    @Autowired
    @Qualifier("simu.queryBuyFundStatusFacade")
    private QueryBuyFundStatusFacade queryBuyFundStatusFacade;

    @Autowired
    @Qualifier("simu.queryRedeemFundStatusFacade")
    private QueryRedeemFundStatusFacade queryRedeemFundStatusFacade;

    @Autowired
    @Qualifier("simu.highProductService")
    private HighProductService highProductService;
    @Autowired
    private DoubleTradeService doubleTradeService;

    @Autowired
    private CrmCustServiceImpl crmCustService;

    @Autowired
    private AccCenterService accCenterService;

    private static final List<String> buyAndSellTradeTypes = new ArrayList<>();

    static {
        buyAndSellTradeTypes.add(PreTradeTypeEnum.BUY.getCode());
        buyAndSellTradeTypes.add(PreTradeTypeEnum.SUB_BUY.getCode());
        buyAndSellTradeTypes.add(PreTradeTypeEnum.SELL.getCode());
    }

    private static final Set<String> BUY_TRADE_TYPES = new HashSet<>();

    static {
        BUY_TRADE_TYPES.add(PreTradeTypeEnum.BUY.getCode());
        BUY_TRADE_TYPES.add(PreTradeTypeEnum.SUB_BUY.getCode());
    }

    private static final List<String> EONTRACT_PRETYPE = new ArrayList<String>();

    static {
        EONTRACT_PRETYPE.add(OrderFormTypeEnum.ECONTRACT.getCode());
    }


    private static final List<String> CONFIRM_PREBOOKSTAT = new ArrayList<String>();

    static {
        CONFIRM_PREBOOKSTAT.add(PreBookStateEnum.CONFIRM.getCode());
    }

    private static final String IN_DIRECTBLACKLIST_YES = "1";

    private static final String IN_DIRECTBLACKLIST_NO = "0";

    private static final int ONE_YEAR = -12;


    /**
     * @api {POST} /simu/query/querydoubletradelist.htm 查询预约列表页面
     * @apiVersion 1.0.0
     * @apiGroup query
     * @apiName queryDoubleTradeList
     * @apiDescription 查询双录列表
     * @apiSuccess (body) {Array}  doubleTradeForPlatList 双录集合
     * @apiSuccess ((DoubleTradeInfoVo) {String} tradeId 双录单号
     * @apiSuccess (DoubleTradeInfoVo) {String} fundCode 产品编码
     * @apiSuccess (DoubleTradeInfoVo) {String} fundName 产品名称
     * @apiSuccess (DoubleTradeInfoVo) {String} buttonStatus 按钮状态,0:不展示按钮,1:显示按钮,可以点击,2:显示按钮,不可以点击
     * @apiSuccess (DoubleTradeInfoVo) {String} status 双录状态 0-无需双录；1-未双录；2-已双录,3-已处理待审核,4-审核不通过,5-待投顾处理,6-失效,7-视频生成中
     * @apiSuccess (DoubleTradeInfoVo) {String} noticeMsg 提示文案
     * @apiSuccess (DoubleTradeInfoVo) {String} doubleTradeGroupType 双录分组类型,1:待处理,2:待审核,3:已完成
     * @apiSuccess (DoubleTradeInfoVo) {String} disCode 分销机构编码
     * @apiSuccess (DoubleTradeInfoVo) {String} deadlineDt 双录截止日期
     * @apiSuccess (DoubleTradeInfoVo) {String} tempId 模版id
     * @apiSuccess (DoubleTradeInfoVo) {String} preId 预约id
     * @apiSuccess (DoubleTradeInfoVo) {String} doubleType 双录方式：1-人工双录 2-人机双录
     * @apiSuccess (DoubleTradeInfoVo) {String} uploadDt 上传日期
     * @apiSuccess (DoubleTradeInfoVo) {Number} amount 预约金额(元)
     * @apiSuccessExample 响应结果示例
     *{"doubleTradeForPlatList":[{"doubleType":"VOCiq","amount":2137.514354133816,"noticeMsg":"4O48uj","uploadDt":"H8yWm","disCode":"4Q","fundCode":"a5ZR","showButton":"IhTX4","buttonStatus":"wAJ","doubleTradeGroupType":"5SIwSlv","tempId":"w0","fundName":"WgZAU","tradeId":"yy","status":"ByXWpxuzJ","deadlineDt":"A"}
    ]}     */
    @RequestMapping("/simu/query/querydoubletradelist.htm")
    public void queryDoubleTradeList(HttpServletRequest request, HttpServletResponse response) throws Exception {
        // 一账通号
        String hboneNo = getString("hboneNo");
        Map<String, Object> ret = new HashMap<String, Object>(16);
        List<DoubleTradeInfoVo> doubleTradeInfoVos = null;
        if (!StringUtil.isEmpty(hboneNo)) {
            // 获取渠道信息
            String[] disCodeList = request.getParameterValues("disCodeList");
            List<String> disCodeLists = Arrays.asList(disCodeList);
            doubleTradeInfoVos = doubleTradeService.queryDoubleTradeListService(hboneNo,disCodeLists);
        }
        ret.put("doubleTradeForPlatList", doubleTradeInfoVos);
        write(ret, CGIConstants.RESPONSE_CONTENT_TYPE_CIPHERTEXT, response);
    }

    /**
     * @api {POST} /simu/query/reservation.htm 查询预约列表页面
     * @apiVersion 1.0.0
     * @apiGroup query
     * @apiName index
     * @apiDescription 查询预约列表(购买列表, 赎回列表)
     * @apiSuccess (响应结果) {String} code 返回码
     * @apiSuccess (响应结果) {String} desc 返回描述
     * @apiSuccess (响应结果) {String} hasHZProduct 是否持有好臻产品 0:没有,1:有
     * @apiSuccess (响应结果) {String} hasHKProduct 是否持有好买香港产品  0:没有,1:有
     * @apiSuccess (响应结果) {String} isAuth 是否授权  0:没有,1:有授权
     * @apiSuccess (响应结果) {Array} buylist 购买预约列表
     * @apiSuccess (响应结果) {String} buylist.id 标识
     * @apiSuccess (响应结果) {String} buylist.jjjc 基金简称
     * @apiSuccess (响应结果) {String} buylist.jjdm 基金代码
     * @apiSuccess (响应结果) {String} buylist.productType 基金类型
     * @apiSuccess (响应结果) {String} buylist.fundWebUrl 基金路径
     * @apiSuccess (响应结果) {String} buylist.prebookDt 预约时间
     * @apiSuccess (响应结果) {Number} buylist.bookAmount 预约金额
     * @apiSuccess (响应结果) {String} buylist.currency 币种
     * @apiSuccess (响应结果) {String} buylist.disCode 分销渠道
     * @apiSuccess (响应结果) {Number} buylist.appointSubscribeAmt 预约认缴金额(只有分次call才有)
     * @apiSuccess (响应结果) {Number} buylist.appointPaidAmt 预约实缴金额
     * @apiSuccess (响应结果) {Number} buylist.subscribeAmt 认缴金额(只有分次call才有)
     * @apiSuccess (响应结果) {Number} buylist.paidAmt 实缴金额
     * @apiSuccess (响应结果) {String} buylist.isFirstPay 是否首次实缴,1:是;0:不是
     * @apiSuccess (响应结果) {String} buylist.peDivideCallFlag 是否分次CALL款股权产品 0-否 1是
     * @apiSuccess (响应结果) {Number} buylist.fee 手续费
     * @apiSuccess (响应结果) {Number} buylist.discountfee 无折扣手续费
     * @apiSuccess (响应结果) {String} buylist.payEndDate 打款截止日
     * @apiSuccess (响应结果) {String} buylist.redeemDate 贖回開放日
     * @apiSuccess (响应结果) {String} buylist.activityDiscountEndDate 活动折扣截止日
     * @apiSuccess (响应结果) {String} buylist.preType 预约类型 1：纸质成单；2：电子成单；3：无纸化；4：异常流程
     * @apiSuccess (响应结果) {String} buylist.fundType 产品类型
     * @apiSuccess (响应结果) {Number} buylist.sellVol 赎回金额
     * @apiSuccess (响应结果) {String} buylist.canBuy 可追加 1.可追加，其他值不可追加
     * @apiSuccess (响应结果) {String} buylist.canRedeem 赎回状态  1.可赎回  ，其他值不可赎回
     * @apiSuccess (响应结果) {String} buylist.inDirectBlackList 黑名单
     * @apiSuccess (响应结果) {String} buylist.openStartDt 开放开始日
     * @apiSuccess (响应结果) {String} buylist.openEndDt 开放截止日
     * @apiSuccess (响应结果) {String} buylist.SupportAdvanceFlag 支持提前下单 0-不支持；1-支持
     * @apiSuccess (响应结果) {String} buylist.productSource 产品信息来源 SMOP，DB（仅SMOP未配置时，取DB）
     * @apiSuccess (响应结果) {String} buylist.feeCalMode 费用计算方式,0-外扣法；1-内扣法
     * @apiSuccess (响应结果) {Array} selllist 赎回预约列表
     * @apiSuccess (响应结果) {String} selllist.id 标识
     * @apiSuccess (响应结果) {String} selllist.jjjc 基金简称
     * @apiSuccess (响应结果) {String} selllist.jjdm 基金代码
     * @apiSuccess (响应结果) {String} selllist.productType 基金类型
     * @apiSuccess (响应结果) {String} selllist.fundWebUrl 基金路径
     * @apiSuccess (响应结果) {String} selllist.prebookDt 预约时间
     * @apiSuccess (响应结果) {Number} selllist.bookAmount 预约金额
     * @apiSuccess (响应结果) {String} selllist.currency 币种
     * @apiSuccess (响应结果) {String} selllist.disCode 分销渠道
     * @apiSuccess (响应结果) {Number} selllist.appointSubscribeAmt 认缴金额(只有分次call才有)
     * @apiSuccess (响应结果) {Number} selllist.appointPaidAmt 实缴金额
     * @apiSuccess (响应结果) {Number} selllist.subscribeAmt 认缴金额(只有分次call才有)
     * @apiSuccess (响应结果) {Number} selllist.paidAmt 实缴金额
     * @apiSuccess (响应结果) {String} selllist.isFirstPay 是否首次实缴,1:是;0:不是
     * @apiSuccess (响应结果) {String} selllist.peDivideCallFlag 是否分次CALL款股权产品 0-否 1是
     * @apiSuccess (响应结果) {Number} selllist.fee 手续费
     * @apiSuccess (响应结果) {Number} selllist.discountfee 无折扣手续费
     * @apiSuccess (响应结果) {String} selllist.payEndDate 打款截止日
     * @apiSuccess (响应结果) {String} selllist.redeemDate 贖回開放日
     * @apiSuccess (响应结果) {String} selllist.activityDiscountEndDate 活动折扣截止日
     * @apiSuccess (响应结果) {String} selllist.preType 预约类型 1：纸质成单；2：电子成单；3：无纸化；4：异常流程
     * @apiSuccess (响应结果) {String} selllist.fundType 产品类型
     * @apiSuccess (响应结果) {Number} selllist.sellVol 赎回金额
     * @apiSuccess (响应结果) {String} selllist.canBuy 可追加 1.可追加，其他值不可追加
     * @apiSuccess (响应结果) {String} selllist.canRedeem 赎回状态  1.可赎回  ，其他值不可赎回
     * @apiSuccess (响应结果) {String} selllist.inDirectBlackList 黑名单
     * @apiSuccess (响应结果) {String} selllist.openStartDt 开放开始日
     * @apiSuccess (响应结果) {String} selllist.openEndDt 开放截止日
     * @apiSuccess (响应结果) {String} selllist.SupportAdvanceFlag 支持提前下单 0-不支持；1-支持
     * @apiSuccess (响应结果) {String} selllist.productSource 产品信息来源 SMOP，DB（仅SMOP未配置时，取DB）
     * @apiSuccess (响应结果) {String} selllist.feeCalMode 费用计算方式,0-外扣法；1-内扣法
     * @apiSuccess (响应结果) {Array} confirmlist 无纸化预约列表
     * @apiSuccess (响应结果) {String} confirmlist.id 标识
     * @apiSuccess (响应结果) {String} confirmlist.jjjc 基金简称
     * @apiSuccess (响应结果) {String} confirmlist.jjdm 基金代码
     * @apiSuccess (响应结果) {String} confirmlist.productType 基金类型
     * @apiSuccess (响应结果) {String} confirmlist.fundWebUrl 基金路径
     * @apiSuccess (响应结果) {String} confirmlist.prebookDt 预约时间
     * @apiSuccess (响应结果) {Number} confirmlist.bookAmount 预约金额
     * @apiSuccess (响应结果) {String} confirmlist.currency 币种
     * @apiSuccess (响应结果) {String} confirmlist.disCode 分销渠道
     * @apiSuccess (响应结果) {Number} confirmlist.appointSubscribeAmt 认缴金额(只有分次call才有)
     * @apiSuccess (响应结果) {Number} confirmlist.appointPaidAmt 实缴金额
     * @apiSuccess (响应结果) {Number} confirmlist.subscribeAmt 认缴金额(只有分次call才有)
     * @apiSuccess (响应结果) {Number} confirmlist.paidAmt 实缴金额
     * @apiSuccess (响应结果) {String} confirmlist.isFirstPay 是否首次实缴,1:是;0:不是
     * @apiSuccess (响应结果) {String} confirmlist.peDivideCallFlag 是否分次CALL款股权产品 0-否 1是
     * @apiSuccess (响应结果) {Number} confirmlist.fee 手续费
     * @apiSuccess (响应结果) {Number} confirmlist.discountfee 无折扣手续费
     * @apiSuccess (响应结果) {String} confirmlist.payEndDate 打款截止日
     * @apiSuccess (响应结果) {String} confirmlist.redeemDate 贖回開放日
     * @apiSuccess (响应结果) {String} confirmlist.activityDiscountEndDate 活动折扣截止日
     * @apiSuccess (响应结果) {String} confirmlist.preType 预约类型 1：纸质成单；2：电子成单；3：无纸化；4：异常流程
     * @apiSuccess (响应结果) {String} confirmlist.fundType 产品类型
     * @apiSuccess (响应结果) {Number} confirmlist.sellVol 赎回金额
     * @apiSuccess (响应结果) {String} confirmlist.canBuy 可追加 1.可追加，其他值不可追加
     * @apiSuccess (响应结果) {String} confirmlist.canRedeem 赎回状态  1.可赎回  ，其他值不可赎回
     * @apiSuccess (响应结果) {String} confirmlist.inDirectBlackList 黑名单
     * @apiSuccess (响应结果) {String} confirmlist.openStartDt 开放开始日
     * @apiSuccess (响应结果) {String} confirmlist.openEndDt 开放截止日
     * @apiSuccess (响应结果) {String} confirmlist.SupportAdvanceFlag 支持提前下单 0-不支持；1-支持
     * @apiSuccess (响应结果) {String} confirmlist.productSource 产品信息来源 SMOP，DB（仅SMOP未配置时，取DB）
     * @apiSuccess (响应结果) {String} confirmlist.feeCalMode 费用计算方式,0-外扣法；1-内扣法
     * @apiSuccessExample 响应结果示例
     * {"buylist":[{"activityDiscountEndDate":"hDXoamVl","productSource":"ol20JjQuu","payEndDate":"I2IInk8","preType":"GdAzRn","fee":6010.6100842285,"openStartDt":"aljD","jjjc":"eTQ","disCode":"5oTI866A","sellVol":3272.142007994825,"fundWebUrl":"OW6iXwh","appointSubscribeAmt":3861.712774118823,"fundType":"QEe8M3M","isFirstPay":"hG","redeemDate":"W2n0S","subscribeAmt":6674.366974451103,"currency":"qVLogD","id":"7","openEndDt":"2bDGuB3Y4","feeCalMode":"0gjXigQg","productType":"N","discountfee":961.6267137916557,"bookAmount":96.68963427791444,"appointPaidAmt":4374.488022658122,"paidAmt":9443.9634093018,"peDivideCallFlag":"o83","prebookDt":"d","canRedeem":"6nKyT2","canBuy":"jgazYUOx5","SupportAdvanceFlag":"3f1api","inDirectBlackList":"ckSXIz3","jjdm":"nXsS"}],"isAuth":"13","code":"yv","hasHZProduct":"Xmrsw","hasHKProduct":"9pGxRzS","selllist":[{"activityDiscountEndDate":"v6IcvhkKN","productSource":"p","payEndDate":"vx","preType":"BuLZF","fee":5853.839610598978,"openStartDt":"mskAc","jjjc":"OjJRzAnwqq","disCode":"qV5X0dK","sellVol":5296.6270113461715,"fundWebUrl":"WTGXY","appointSubscribeAmt":6286.1017184350285,"fundType":"N6","isFirstPay":"ZvZaC","redeemDate":"e7XfX2LJQ","subscribeAmt":4497.230099835971,"currency":"QOJ","id":"KlMFha3Sl","openEndDt":"E7vDsWFbD","feeCalMode":"f","productType":"CCZ2","discountfee":8564.779293188087,"bookAmount":4692.725623307356,"appointPaidAmt":2253.7518949961286,"paidAmt":3501.4764696149946,"peDivideCallFlag":"da29Cg","prebookDt":"gnmFA3U3","canRedeem":"gd6mNKn","canBuy":"loQ","SupportAdvanceFlag":"ZRr0J","inDirectBlackList":"n8dQilr","jjdm":"7SRAVTW"}],"confirmlist":[{"activityDiscountEndDate":"UDpENKW","productSource":"xztV4","payEndDate":"Xhh","preType":"tHxMNdaL","fee":2305.3624695655426,"openStartDt":"8pgh1QW","jjjc":"i7Pluf4O","disCode":"x2jl2","sellVol":3055.8911404951496,"fundWebUrl":"WqOyVFIn1","appointSubscribeAmt":6062.931648778471,"fundType":"DyNsbvtRp1","isFirstPay":"aR","redeemDate":"17l","subscribeAmt":9920.096680749924,"currency":"uvjQskCMSr","id":"D2bBsbcGr","openEndDt":"UEj","feeCalMode":"QAmKQw7C","productType":"5a","discountfee":5166.317803202429,"bookAmount":2053.96407892954,"appointPaidAmt":4902.601379706421,"paidAmt":6676.4607924830225,"peDivideCallFlag":"KAUffDn7hs","prebookDt":"1rm","canRedeem":"UY1itcEEWJ","canBuy":"u3or8ekXGc","SupportAdvanceFlag":"9m","inDirectBlackList":"B7Zlz","jjdm":"wdmuwh"}],"desc":"fMu"}
     */
    @RequestMapping("/simu/query/reservation.htm")
    public void index(HttpServletRequest request, HttpServletResponse response) throws Exception {
        TradeSession loginInfo = this.getCustSession();
        // 获取分销渠道信息
        List<String> disCodeLists = new ArrayList<>();
        String[] disCodeList = request.getParameterValues("disCodeList");
        if (disCodeList != null && disCodeList.length > 0) {
            disCodeLists = Arrays.asList(disCodeList);
        }
        CustReservationDto rst = buildCustReservation(loginInfo,disCodeLists);
        write(rst, CGIConstants.RESPONSE_CONTENT_TYPE_CIPHERTEXT, response);
    }

    private CustReservationDto buildCustReservation(TradeSession loginInfo,List<String> disCodeLists) {
        CustReservationDto custReservationDto = new CustReservationDto();
        custReservationDto.setCode("0000");
        custReservationDto.setDesc("成功");
        custReservationDto.setBuylist(new ArrayList<>());
        custReservationDto.setSelllist(new ArrayList<>());
        custReservationDto.setConfirmlist(new ArrayList<>());
        // 查询是否授权
        AcctDataAuthDto acctDataAuthInfo = accCenterService.getAcctDataAuthInfo(loginInfo.getUser().getHboneNo());
        QueryPreBookListResponse queryPreBookListResponse = queryBuyAndSellPreBookList(loginInfo.getUser().getHboneNo(), acctDataAuthInfo,disCodeLists);
        if (tmsCallFail(queryPreBookListResponse)) {
            return custReservationDto;
        }
        Set<String> allPreProductCodes = new HashSet<>();
        List<PreBookListBean> preBookList = queryPreBookListResponse.getPreBookList();
        fill(preBookList, custReservationDto, loginInfo.getUser().getTxAcctNo(), allPreProductCodes);

        List<PreBookListBean> noPaperPreList = queryNoPaperPreBookList(loginInfo.getUser().getHboneNo());
        fillNoPaperList(noPaperPreList, custReservationDto, allPreProductCodes);

        fillFundWebUrl(custReservationDto, allPreProductCodes);
        custReservationDto.setHasHKProduct(queryPreBookListResponse.getHasHKProduct());
        custReservationDto.setHasHZProduct(queryPreBookListResponse.getHasHZProduct());
        custReservationDto.setIsAuth(acctDataAuthInfo.getIsDataAuth());
        return custReservationDto;
    }

    private void fillNoPaperList(List<PreBookListBean> noPaperPreList, CustReservationDto custReservationDto,
                                 Set<String> allPreProductCodes) {
        if (CollectionUtils.isEmpty(noPaperPreList)) {
            return;
        }
        PrebookDTO noPaperPrebookDTO = null;
        for (PreBookListBean preBookListBean : noPaperPreList) {
            noPaperPrebookDTO = buildPreBookDto(preBookListBean);
            custReservationDto.getConfirmlist().add(noPaperPrebookDTO);

            allPreProductCodes.add(preBookListBean.getFundCode());
        }
    }

    private QueryPreBookListResponse queryBuyAndSellPreBookList(String hbOneNo,
                                                                AcctDataAuthDto acctDataAuthInfo,
                                                                List<String> disCodeLists) {
        QueryPreBookListRequest queryPreBookListRequest = buildQueryBuyAndSellPreBooksRequest(hbOneNo, acctDataAuthInfo,disCodeLists);
        return queryPreBookListFacade.execute(queryPreBookListRequest);
    }

    private QueryPreBookListRequest buildQueryBuyAndSellPreBooksRequest(String hbOneNo,
                                                                        AcctDataAuthDto acctDataAuthInfo,
                                                                        List<String> disCodeLists) {
        String distributionCode = RemoteParametersProvider.getDistributionCode();
        QueryPreBookListRequest queryPreBookListRequest = new QueryPreBookListRequest();
        queryPreBookListRequest.setHbOneNo(hbOneNo);
        if(YesOrNoEnum.YES.getCode().equals(acctDataAuthInfo.getIsDataAuth())){
            queryPreBookListRequest.setNotFilterHkFund(YesOrNoEnum.YES.getCode());
            queryPreBookListRequest.setNotFilterHzFund(YesOrNoEnum.YES.getCode());
            if(YesOrNoEnum.YES.getCode().equals(acctDataAuthInfo.getIsHkDataQuarantine())){
                queryPreBookListRequest.setNotFilterHkFund(YesOrNoEnum.NO.getCode());
            }
        }else {
            queryPreBookListRequest.setNotFilterHkFund(YesOrNoEnum.NO.getCode());
            queryPreBookListRequest.setNotFilterHzFund(YesOrNoEnum.NO.getCode());
        }
        // 根据分销渠道过滤 只过滤”好买分销",”好臻分销"的预约双录,好买 不展示 海外的预约订单,默认展示好买预约订单
        if(!CollectionUtils.isEmpty(disCodeLists)){
            if(!disCodeLists.contains(DisCodeEnum.HZ.getCode())){
                queryPreBookListRequest.setNotFilterHzFund(YesOrNoEnum.NO.getCode());
            }
        }
        queryPreBookListRequest.setPreBookState(CONFIRM_PREBOOKSTAT);
        queryPreBookListRequest.setUseFlag(PreBookUseFlagEnum.NOT_USED.getCode());
        queryPreBookListRequest.setDisCode(distributionCode);
        queryPreBookListRequest.setTradeType(buyAndSellTradeTypes);
        queryPreBookListRequest.setPreType(EONTRACT_PRETYPE);
        return queryPreBookListRequest;
    }

    private List<PreBookListBean> queryNoPaperPreBookList(String hbOneNo) {
        return Collections.emptyList();

    }

    private void fill(List<PreBookListBean> preBookList, CustReservationDto custReservationDto, String txAcctNo, Set<String> productCodes) {
        if (CollectionUtils.isEmpty(preBookList)) {
            return;
        }

        Date endDate = DateUtils.addMonthOfYear(new Date(), ONE_YEAR);
        String endDateStr = DateUtils.formatToString(endDate, DateUtils.YYYYMMDD);
        Set<String> preBuyProductCodes = new HashSet<>();
        Set<String> preSellProductCodes = new HashSet<>();
        for (PreBookListBean preBookListBean : preBookList) {
            if (endDateStr.compareTo(preBookListBean.getCreDt()) <= 0) {
                if (isBuyTradePre(preBookListBean.getTradeType())) {
                    custReservationDto.getBuylist().add(buildPreBookDto(preBookListBean));

                    preBuyProductCodes.add(preBookListBean.getFundCode());
                } else if (isSellTradePre(preBookListBean.getTradeType())) {
                    custReservationDto.getSelllist().add(buildPreBookDto(preBookListBean));

                    preSellProductCodes.add(preBookListBean.getFundCode());
                }

                productCodes.add(preBookListBean.getFundCode());
            }
        }

        Map<String, BuyFundStatusBean> buyFundStatusBeanMap = queryBuyStatusMap(txAcctNo, new ArrayList<>(preBuyProductCodes));
        Map<String, HighProductBaseInfoModel> highProductBaseInfoModelMap = queryProductMap(new ArrayList<>(preBuyProductCodes));
        fillBuyStatusAndFeeCalMode(custReservationDto.getBuylist(), buyFundStatusBeanMap, highProductBaseInfoModelMap);

        Map<String, RedeemFundStatusBean> redeemFundStatusBeanMap = querRedeemStatusMap(txAcctNo, new ArrayList<>(preSellProductCodes));
        fillSellStatus(custReservationDto.getSelllist(), redeemFundStatusBeanMap);
    }


    private Map<String, HighProductBaseInfoModel> queryProductMap(List<String> fundCodes) {
        List<HighProductBaseInfoModel> productInfoList = highProductService.getHighProductBaseInfo(fundCodes);

        Map<String, HighProductBaseInfoModel> productInfoMap = new HashMap<>();
        if (productInfoList != null) {
            for (HighProductBaseInfoModel highProductBaseInfoModel : productInfoList) {
                productInfoMap.put(highProductBaseInfoModel.getFundCode(), highProductBaseInfoModel);
            }
        }

        return productInfoMap;
    }

    private Map<String, BuyFundStatusBean> queryBuyStatusMap(String txAcctNo, List<String> productCodes) {
        if (CollectionUtils.isEmpty(productCodes)) {
            return Collections.emptyMap();
        }

        QueryBuyFundStatusRequest qryRequest = new QueryBuyFundStatusRequest();
        qryRequest.setTxAcctNo(txAcctNo);
        qryRequest.setProductCodeList(new ArrayList<>(productCodes));
        QueryBuyFundStatusResponse qryResponse = queryBuyFundStatusFacade.execute(qryRequest);

        if (tmsCallFail(qryResponse)) {
            return Collections.emptyMap();
        }

        List<BuyFundStatusBean> buyFundStatusList = qryResponse.getBuyFundStatusList();
        if (CollectionUtils.isEmpty(buyFundStatusList)) {
            return Collections.emptyMap();
        }

        Map<String, BuyFundStatusBean> buyFundStatusBeanMap = new HashMap<>();
        for (BuyFundStatusBean buyFundStatusBean : buyFundStatusList) {
            buyFundStatusBeanMap.put(buyFundStatusBean.getProductCode(), buyFundStatusBean);
        }

        return buyFundStatusBeanMap;
    }

    private Map<String, RedeemFundStatusBean> querRedeemStatusMap(String txAcctNo, List<String> productCodes) {
        if (CollectionUtils.isEmpty(productCodes)) {
            return Collections.emptyMap();
        }

        QueryRedeemFundStatusRequest qryRequest = new QueryRedeemFundStatusRequest();
        qryRequest.setTxAcctNo(txAcctNo);
        qryRequest.setProductCodeList(new ArrayList<>(productCodes));
        QueryRedeemFundStatusResponse qryResponse = queryRedeemFundStatusFacade.execute(qryRequest);

        if (tmsCallFail(qryResponse)) {
            return Collections.emptyMap();
        }

        List<RedeemFundStatusBean> redeemFundStatusList = qryResponse.getRedeemFundStatusList();
        if (CollectionUtils.isEmpty(redeemFundStatusList)) {
            return Collections.emptyMap();
        }

        Map<String, RedeemFundStatusBean> redeemFundStatusBeanMap = new HashMap<>();
        for (RedeemFundStatusBean redeemFundStatusBean : redeemFundStatusList) {
            redeemFundStatusBeanMap.put(redeemFundStatusBean.getProductCode(), redeemFundStatusBean);
        }

        return redeemFundStatusBeanMap;
    }

    private void fillBuyStatusAndFeeCalMode(List<PrebookDTO> buylist, Map<String, BuyFundStatusBean> buyFundStatusMap, Map<String, HighProductBaseInfoModel> highProductBaseInfoModelMap) {
        if (CollectionUtils.isEmpty(buylist)) {
            return;
        }

        for (PrebookDTO prebookDTO : buylist) {
            BuyFundStatusBean buyFundStatusBean = buyFundStatusMap.get(prebookDTO.getJjdm());
            if (buyFundStatusBean != null) {
                prebookDTO.setCanBuy(buyFundStatusBean.getBuyStatus());
                prebookDTO.setInDirectBlackList(getInDirectBlackListFlag(buyFundStatusBean.getBuyStatusType()));
            }

            HighProductBaseInfoModel highProductBaseInfoModel = highProductBaseInfoModelMap.get(prebookDTO.getJjdm());
            if (highProductBaseInfoModel != null) {
                prebookDTO.setFeeCalMode(highProductBaseInfoModel.getFeeCalMode());
            }
        }
    }

    private String getInDirectBlackListFlag(String buyStatusType) {
        return BuyStatusTypeEnum.IN_BLANKLIST.getCode().equals(buyStatusType) ? IN_DIRECTBLACKLIST_YES : IN_DIRECTBLACKLIST_NO;
    }

    private void fillSellStatus(List<PrebookDTO> selllist, Map<String, RedeemFundStatusBean> redeemFundStatusMap) {
        if (CollectionUtils.isEmpty(selllist)) {
            return;
        }

        for (PrebookDTO prebookDTO : selllist) {
            RedeemFundStatusBean redeemFundStatusBean = redeemFundStatusMap.get(prebookDTO.getJjdm());
            if (redeemFundStatusBean != null) {
                prebookDTO.setCanRedeem(redeemFundStatusBean.getRedeemStatus());
            }
        }
    }

    private boolean isBuyTradePre(String tradeType) {
        return BUY_TRADE_TYPES.contains(tradeType);
    }

    private boolean isSellTradePre(String tradeType) {
        return PreTradeTypeEnum.SELL.getCode().equals(tradeType);
    }

    private void fillFundWebUrl(CustReservationDto custReservationDto, Set<String> allPreProductCodes) {
        Map<String, SmxxDto> smxxDtoMap = getSmxxMap(new ArrayList<>(allPreProductCodes));
        Map<String, String> cpflMap = getCpflMap(new ArrayList<>(allPreProductCodes));

        fillFundListWebUrl(custReservationDto.getBuylist(), smxxDtoMap, cpflMap);
        fillFundListWebUrl(custReservationDto.getSelllist(), smxxDtoMap, cpflMap);
        fillFundListWebUrl(custReservationDto.getConfirmlist(), smxxDtoMap, cpflMap);
    }

    private void fillFundListWebUrl(List<PrebookDTO> preList, Map<String, SmxxDto> smxxDtoMap, Map<String, String> cpflMap) {
        for (PrebookDTO prebookDTO : preList) {
            SmxxDto smxxDto = smxxDtoMap.get(prebookDTO.getJjdm());
            prebookDTO.setFundWebUrl(getWebUrl(smxxDto, prebookDTO.getJjdm(), cpflMap.get(prebookDTO.getJjdm())));
        }
    }


    /**
     * buildPreBookDto:(构建预约信息)
     *
     * @param preBookListBean
     * <AUTHOR>
     * @date 2018年1月19日 下午2:07:50
     */
    private PrebookDTO buildPreBookDto(PreBookListBean preBookListBean) {
        PrebookDTO prebookDTO = new PrebookDTO();
        prebookDTO.setId(preBookListBean.getPreId());
        prebookDTO.setJjjc(preBookListBean.getFundName());
        prebookDTO.setJjdm(preBookListBean.getFundCode());
        prebookDTO.setProductType(preBookListBean.getFundType());
        prebookDTO.setPrebookDt(preBookListBean.getCreDt());
        prebookDTO.setBookAmount(MoneyUtil.formatMoneyUp(preBookListBean.getAckAmt(), 2));
        prebookDTO.setFee(preBookListBean.getFee());
        prebookDTO.setFeeRate(preBookListBean.getFeeRate());
        prebookDTO.setDiscountfee(preBookListBean.getDisCountFee());
        prebookDTO.setPayEndDate(preBookListBean.getPayEndDate());
        prebookDTO.setRedeemDate(preBookListBean.getRedeemDate());
        prebookDTO.setActivityDiscountEndDate(preBookListBean.getActivityDiscountEndDate());
        prebookDTO.setFeeRateMethod(preBookListBean.getFeeRateMethod());
        prebookDTO.setPreType(preBookListBean.getPreType());
        prebookDTO.setSellVol(preBookListBean.getSellVol());
        prebookDTO.setOpenStartDt(preBookListBean.getOpenStartDt());
        prebookDTO.setOpenEndDt(preBookListBean.getOpenEndDt());
        prebookDTO.setSupportAdvanceFlag(preBookListBean.getSupportAdvanceFlag());
        prebookDTO.setPeDivideCallFlag(preBookListBean.getPeDivideCallFlag());
        prebookDTO.setAppointPaidAmt(MoneyUtil.formatMoneyUp(preBookListBean.getAckAmt(), 2));
        prebookDTO.setAppointSubscribeAmt(MoneyUtil.formatMoneyUp(preBookListBean.getAppointSubsAmt(), 2));
        prebookDTO.setSubscribeAmt(MoneyUtil.formatMoneyUp(preBookListBean.getSubsAmt(), 2));
        prebookDTO.setIsFirstPay(preBookListBean.getFirstPreId());
        prebookDTO.setDisCode(preBookListBean.getDisCode());
        prebookDTO.setProductSource(preBookListBean.getProductSource());
        return prebookDTO;
    }

}
