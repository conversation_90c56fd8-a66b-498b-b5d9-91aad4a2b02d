<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>2.3.12.RELEASE</version>
		<relativePath /> 
	</parent>

	<groupId>com.howbuy.tms</groupId>
	<artifactId>elasticsearch-center</artifactId>
	<version>4.8.72-RELEASE</version>
	<packaging>pom</packaging>
	<name>elasticsearch-center</name>

	<properties>

		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>

		<java.version>1.8</java.version>
		<dubbo.version>2.7.15</dubbo.version>
		<spring-cloud.version>Hoxton.SR12</spring-cloud.version>
		<mybatis-spring-boot-starter.version>2.2.0</mybatis-spring-boot-starter.version>
		<zookeeper.version>3.4.13</zookeeper.version>
		<druid.version>1.2.8</druid.version>
		<pagehelper.version>4.1.4</pagehelper.version>
		<fastjson.version>1.2.17</fastjson.version>
		<zkclient.version>0.4</zkclient.version>
		<log4j.version>2.15.0</log4j.version>
		<rocketmq.client.version>4.9.0</rocketmq.client.version>

		<javassist.version>3.20.0-GA</javassist.version>
		<jackson.version>2.9.5</jackson.version>
		<bouncycastle.version>1.55</bouncycastle.version>
		<mail.version>1.5.3</mail.version>
		<unboundid.version>3.2.0</unboundid.version>
		<hessian.version>4.0.7</hessian.version>
		<commons.pool.version>1.6</commons.pool.version>
		<commons.beanutils.version>1.9.2</commons.beanutils.version>
		<commons-fileupload.version>1.2</commons-fileupload.version>
		<org.codehaus.jackson.version>1.9.13</org.codehaus.jackson.version>
		<lmax.version>3.4.2</lmax.version>
		<ojdbc14.version>10.2.0.4.0</ojdbc14.version>
		<data.elasticsearch.version>3.0.8.RELEASE</data.elasticsearch.version>
		<elasticsearch.version>5.6.8</elasticsearch.version>
		<xpack.version>5.6.1</xpack.version>
		<kafka.version>2.1.7.RELEASE</kafka.version>
		<rocketmq.client.version>4.9.0</rocketmq.client.version>
		<com.howbuy.howbuy-cachemanagement.version>3.5.1-RELEASE</com.howbuy.howbuy-cachemanagement.version>
		<com.howbuy.howbuy-boot-actuator.version>1.1.2-RELEASE</com.howbuy.howbuy-boot-actuator.version>

		<com.howbuy.howbuy-message-service.version>2.3.0-RELEASE</com.howbuy.howbuy-message-service.version>
		<com.howbuy.howbuy-message-amq.version>2.3.0-RELEASE</com.howbuy.howbuy-message-amq.version>
		<com.howbuy.howbuy-message-rocket.version>2.3.0-RELEASE</com.howbuy.howbuy-message-rocket.version>
		<com.howbuy.ccms-watcher-plugin-nacosImpl.version>1.0.0-RELEASE</com.howbuy.ccms-watcher-plugin-nacosImpl.version>
		<com.howbuy.howbuy-ccms-watcher.version>6.0.1-RELEASE</com.howbuy.howbuy-ccms-watcher.version>

		<com.howbuy.elasticsearch-center.version>4.8.72-RELEASE</com.howbuy.elasticsearch-center.version>
        <com.howbuy.ftx-order-facade.version>2.1.1-RELEASE</com.howbuy.ftx-order-facade.version>

        <com.howbuy.tms-common-client.version>4.8.72-RELEASE</com.howbuy.tms-common-client.version>
		<com.howbuy.tms-common-message-service.version>4.8.72-RELEASE</com.howbuy.tms-common-message-service.version>
		<com.howbuy.tms-common-lang.version>4.8.72-RELEASE</com.howbuy.tms-common-lang.version>
		<com.howbuy.tms-common-enums.version>4.8.72-RELEASE</com.howbuy.tms-common-enums.version>
		<com.howbuy.tms-common-service.version>4.8.72-RELEASE</com.howbuy.tms-common-service.version>

        <com.howbuy.order-center-client.version>4.8.59-RELEASE</com.howbuy.order-center-client.version>
		<com.howbuy.high-order-center-client.version>4.8.88-RELEASE</com.howbuy.high-order-center-client.version>
		<com.howbuy.product-center-client.version>4.8.59-RELEASE</com.howbuy.product-center-client.version>
		<com.howbuy.product-center-model.version>4.8.59-RELEASE</com.howbuy.product-center-model.version>
		<com.howbuy.howbuy-simu-client.version>release-20250721-4019-RELEASE</com.howbuy.howbuy-simu-client.version>

        <com.howbuy.dtms-order-client.version>bugfix-20250724-RELEASE</com.howbuy.dtms-order-client.version>
    </properties>

	<dependencyManagement>
		<dependencies>
			
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-dependencies</artifactId>
				<version>${spring-cloud.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
			<dependency>
				<groupId>org.apache.dubbo</groupId>
				<artifactId>dubbo</artifactId>
				<version>${dubbo.version}</version>
				<exclusions>
					<exclusion>
						<groupId>org.springframework</groupId>
						<artifactId>spring</artifactId>
					</exclusion>
					<exclusion>
						<groupId>javax.servlet</groupId>
						<artifactId>servlet-api</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.slf4j</groupId>
						<artifactId>slf4j-log4j12</artifactId>
					</exclusion>
					<exclusion>
						<groupId>log4j</groupId>
						<artifactId>log4j</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>org.apache.dubbo</groupId>
				<artifactId>dubbo-spring-boot-starter</artifactId>
				<version>${dubbo.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.zookeeper</groupId>
				<artifactId>zookeeper</artifactId>
				<version>${zookeeper.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.dubbo</groupId>
				<artifactId>dubbo-dependencies-zookeeper</artifactId>
				<version>${dubbo.version}</version>
				<type>pom</type>
				<exclusions>
					<exclusion>
						<groupId>org.slf4j</groupId>
						<artifactId>slf4j-log4j12</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>org.mybatis.spring.boot</groupId>
				<artifactId>mybatis-spring-boot-starter</artifactId>
				<version>${mybatis-spring-boot-starter.version}</version>
			</dependency>
			<dependency>
				<groupId>com.alibaba.cloud</groupId>
				<artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
				<version>2.2.0.RELEASE</version>
			</dependency>
			<dependency>
				<groupId>com.alibaba.cloud</groupId>
				<artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
				<version>2.2.0.RELEASE</version>
			</dependency>
			<dependency>
				<groupId>com.alibaba</groupId>
				<artifactId>druid</artifactId>
				<version>${druid.version}</version>
			</dependency>
			<dependency>
				<groupId>com.github.pagehelper</groupId>
				<artifactId>pagehelper</artifactId>
				<version>${pagehelper.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy</groupId>
				<artifactId>howbuy-message-service</artifactId>
				<version>${com.howbuy.howbuy-message-service.version}</version>
				<exclusions>
					<exclusion>
						<artifactId>slf4j-log4j12</artifactId>
						<groupId>org.slf4j</groupId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>com.howbuy</groupId>
				<artifactId>howbuy-message-amq</artifactId>
				<version>${com.howbuy.howbuy-message-amq.version}</version>
				<exclusions>
					<exclusion>
						<artifactId>activemq-all</artifactId>
						<groupId>org.apache.activemq</groupId>
					</exclusion>
					<exclusion>
						<artifactId>slf4j-log4j12</artifactId>
						<groupId>org.slf4j</groupId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>com.howbuy</groupId>
				<artifactId>howbuy-message-rocket</artifactId>
				<version>${com.howbuy.howbuy-message-rocket.version}</version>
				<exclusions>
					<exclusion>
						<artifactId>slf4j-log4j12</artifactId>
						<groupId>org.slf4j</groupId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>com.howbuy.pa</groupId>
				<artifactId>ccms-watcher-plugin-nacosImpl</artifactId>
				<version>${com.howbuy.ccms-watcher-plugin-nacosImpl.version}</version>
				<exclusions>
					<exclusion>
						<artifactId>fastjson</artifactId>
						<groupId>com.alibaba</groupId>
					</exclusion>
					<exclusion>
						<artifactId>nacos-client</artifactId>
						<groupId>com.alibaba.nacos</groupId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>com.howbuy</groupId>
				<artifactId>howbuy-ccms-watcher</artifactId>
				<version>${com.howbuy.howbuy-ccms-watcher.version}</version>
				<exclusions>
					<exclusion>
						<artifactId>fastjson</artifactId>
						<groupId>com.alibaba</groupId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>com.howbuy</groupId>
				<artifactId>utils</artifactId>
				<version>1.0.0-SNAPSHOT</version>
				<exclusions>
					<exclusion>
						<artifactId>javassist</artifactId>
						<groupId>org.javassist</groupId>
					</exclusion>
				</exclusions>
			</dependency>
			
			<dependency>
				<artifactId>howbuy-cachemanagement</artifactId>
				<groupId>com.howbuy</groupId>
				<version>${com.howbuy.howbuy-cachemanagement.version}</version>
				<exclusions>
					<exclusion>
						<artifactId>*</artifactId>
						<groupId>org.slf4j</groupId>
					</exclusion>
					<exclusion>
						<artifactId>log4j</artifactId>
						<groupId>log4j</groupId>
					</exclusion>
					<exclusion>
						<artifactId>javassist</artifactId>
						<groupId>org.javassist</groupId>
					</exclusion>
					<exclusion>
						<artifactId>zkutils</artifactId>
						<groupId>com.howbuy</groupId>
					</exclusion>
					<exclusion>
						<artifactId>howbuy-ccms-independent</artifactId>
						<groupId>com.howbuy</groupId>
					</exclusion>
					<exclusion>
						<artifactId>fastjson</artifactId>
						<groupId>com.alibaba</groupId>
					</exclusion>
					<exclusion>
						<artifactId>activemq-all</artifactId>
						<groupId>org.apache.activemq</groupId>
					</exclusion>
				</exclusions>
			</dependency>
			
			<dependency>
				<groupId>redis.clients</groupId>
				<artifactId>jedis</artifactId>
				<version>2.9.3</version>
			</dependency>

			<dependency>
				<groupId>org.apache.rocketmq</groupId>
				<artifactId>rocketmq-client</artifactId>
				<version>${rocketmq.client.version}</version>
			</dependency>
			
			<dependency>
				<groupId>com.howbuy</groupId>
				<artifactId>zkutils</artifactId>
				<version>1.1</version>
			</dependency>
			<dependency>
				<groupId>joda-time</groupId>
				<artifactId>joda-time</artifactId>
				<version>2.10.5</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy.tms</groupId>
				<artifactId>tms-common-client</artifactId>
				<version>${com.howbuy.tms-common-client.version}</version>
			</dependency>
			<dependency>
				<groupId>org.javassist</groupId>
				<artifactId>javassist</artifactId>
				<version>${javassist.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy.tms</groupId>
				<artifactId>elasticsearch-center-client</artifactId>
				<version>${com.howbuy.elasticsearch-center.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy.tms</groupId>
				<artifactId>elasticsearch-center-dao</artifactId>
				<version>${com.howbuy.elasticsearch-center.version}</version>
			</dependency>

			<dependency>
				<groupId>com.howbuy.tms</groupId>
				<artifactId>tms-common-message-service</artifactId>
				<version>${com.howbuy.tms-common-message-service.version}</version>
				<exclusions>
					<exclusion>
						<artifactId>kafka-clients</artifactId>
						<groupId>org.apache.kafka</groupId>
					</exclusion>
				</exclusions>
			</dependency>

			<dependency>
				<groupId>com.howbuy.tms</groupId>
				<artifactId>order-center-client</artifactId>
				<version>${com.howbuy.order-center-client.version}</version>
			</dependency>
			
			<dependency>
				<groupId>com.howbuy.tms</groupId>
				<artifactId>high-order-center-client</artifactId>
				<version>${com.howbuy.high-order-center-client.version}</version>
			</dependency>

			<dependency>
				<groupId>com.howbuy.interlayer</groupId>
				<artifactId>product-center-client</artifactId>
				<version>${com.howbuy.product-center-client.version}</version>
			</dependency>
			
			<dependency>
				<groupId>com.howbuy.interlayer</groupId>
				<artifactId>product-center-model</artifactId>
				<version>${com.howbuy.product-center-model.version}</version>
			</dependency>

			<dependency>
				<groupId>com.howbuy.tms</groupId>
				<artifactId>tms-common-lang</artifactId>
				<version>${com.howbuy.tms-common-lang.version}</version>
			</dependency>
			
			<dependency>
				<groupId>com.howbuy.tms</groupId>
				<artifactId>tms-common-enums</artifactId>
				<version>${com.howbuy.tms-common-enums.version}</version>
			</dependency>
			
			<dependency>
				<groupId>com.howbuy.tms</groupId>
				<artifactId>tms-common-service</artifactId>
				<version>${com.howbuy.tms-common-service.version}</version>
				<exclusions>
					<exclusion>
						<artifactId>slf4j-log4j12</artifactId>
						<groupId>org.slf4j</groupId>
					</exclusion>
					<exclusion>
						<groupId>org.apache.logging.log4j</groupId>
						<artifactId>log4j-slf4j-impl</artifactId>
					</exclusion>
					<exclusion>
						<artifactId>dubbo</artifactId>
						<groupId>com.alibaba</groupId>
					</exclusion>
					<exclusion>
						<artifactId>servlet-api</artifactId>
						<groupId>javax.servlet</groupId>
					</exclusion>
				</exclusions>
			</dependency>
			
			<dependency>
				<groupId>com.howbuy</groupId>
				<artifactId>ftx-order-facade</artifactId>
				<version>${com.howbuy.ftx-order-facade.version}</version>
			</dependency>
			<dependency>
				<groupId>com.atomikos</groupId>
				<artifactId>atomikos-util</artifactId>
				<version>${atomikos.version}</version>
			</dependency>
			<dependency>
				<groupId>com.atomikos</groupId>
				<artifactId>transactions</artifactId>
				<version>${atomikos.version}</version>
			</dependency>
			<dependency>
				<groupId>com.atomikos</groupId>
				<artifactId>transactions-jdbc</artifactId>
				<version>${atomikos.version}</version>
			</dependency>
			
			<dependency>
				<groupId>org.springframework.kafka</groupId>
				<artifactId>spring-kafka</artifactId>
				<version>${kafka.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.kafka</groupId>
				<artifactId>kafka-clients</artifactId>
				<version>1.0.1</version>
			</dependency>
			<dependency>
				<groupId>org.elasticsearch</groupId>
				<artifactId>elasticsearch</artifactId>
				<version>${elasticsearch.version}</version>
			</dependency>

			<dependency>
				<groupId>org.elasticsearch.client</groupId>
				<artifactId>elasticsearch-rest-high-level-client</artifactId>
				<version>${elasticsearch.version}</version>
			</dependency>

			<dependency>
				<groupId>org.springframework.data</groupId>
				<artifactId>spring-data-elasticsearch</artifactId>
				<version>${data.elasticsearch.version}</version>
			</dependency>
			
			<dependency>
				<groupId>org.elasticsearch.client</groupId>
				<artifactId>transport</artifactId>
				<version>${elasticsearch.version}</version>
			</dependency>

			<dependency>
				<groupId>org.elasticsearch.plugin</groupId>
				<artifactId>transport-netty4-client</artifactId>
				<version>${elasticsearch.version}</version>
			</dependency>

			<dependency>
				<groupId>org.elasticsearch.client</groupId>
				<artifactId>x-pack-transport</artifactId>
				<version>${xpack.version}</version>
			</dependency>
			
			<dependency>
				<groupId>org.elasticsearch.plugin</groupId>
				<artifactId>x-pack-api</artifactId>
				<version>${xpack.version}</version>
			</dependency>

			<dependency>
				<groupId>org.bouncycastle</groupId>
				<artifactId>bcprov-jdk15on</artifactId>
				<version>${bouncycastle.version}</version>
			</dependency>
			
			<dependency>
				<groupId>com.sun.mail</groupId>
				<artifactId>javax.mail</artifactId>
				<version>${mail.version}</version>
			</dependency>
			
			<dependency>
				<groupId>com.unboundid</groupId>
				<artifactId>unboundid-ldapsdk</artifactId>
				<version>${unboundid.version}</version>
			</dependency>
			<dependency>
				<groupId>com.caucho</groupId>
				<artifactId>hessian</artifactId>
				<version>${hessian.version}</version>
			</dependency>
			<dependency>
				<groupId>com.alibaba</groupId>
				<artifactId>fastjson</artifactId>
				<version>${fastjson.version}</version>
			</dependency>
			<dependency>
				<groupId>commons-pool</groupId>
				<artifactId>commons-pool</artifactId>
				<version>${commons.pool.version}</version>
			</dependency>
			<dependency>
				<groupId>commons-beanutils</groupId>
				<artifactId>commons-beanutils</artifactId>
				<version>${commons.beanutils.version}</version>
			</dependency>
			<dependency>
				<groupId>commons-fileupload</groupId>
				<artifactId>commons-fileupload</artifactId>
				<version>${commons-fileupload.version}</version>
			</dependency>
			<dependency>
				<groupId>com.lmax</groupId>
				<artifactId>disruptor</artifactId>
				<version>${lmax.version}</version>
			</dependency>

			
			<dependency>
				<groupId>com.oracle</groupId>
				<artifactId>ojdbc6</artifactId>
				<version>11.2.0.4</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy.tms</groupId>
				<artifactId>tms-common-log-pattern</artifactId>
				<version>1.0.0-RELEASE</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy</groupId>
				<artifactId>howbuy-simu-client</artifactId>
				<version>${com.howbuy.howbuy-simu-client.version}</version>
				<exclusions>
					<exclusion>
						<artifactId>slf4j-log4j12</artifactId>
						<groupId>org.slf4j</groupId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>com.howbuy.boot</groupId>
				<artifactId>howbuy-boot-actuator</artifactId>
				<version>${com.howbuy.howbuy-boot-actuator.version}</version>
				<exclusions>
					<exclusion>
						<artifactId>*</artifactId>
						<groupId>ch.qos.logback</groupId>
					</exclusion>
				</exclusions>
			</dependency>

			<dependency>
				<groupId>org.apache.rocketmq</groupId>
				<artifactId>rocketmq-client</artifactId>
				<version>${rocketmq.client.version}</version>
			</dependency>

			<dependency>
				<groupId>com.howbuy.dtms</groupId>
				<artifactId>dtms-order-client</artifactId>
				<version>${com.howbuy.dtms-order-client.version}</version>
			</dependency>

		</dependencies>

	</dependencyManagement>

	<repositories>
		<repository>
			<id>maven2-repository.dev.java.net</id>
			<name>Java.net Repository for Maven</name>
			<url>http://download.java.net/maven/2/</url>
			<layout>default</layout>
		</repository>
		<repository>
			<id>ReleaseRepo</id>
			<url>http://mvn.intelnal.howbuy.com/nexus/content/repositories/thirdparty</url>
		</repository>
		<repository>
			<id>nexus</id>
			<name>local private nexus</name>
			<url>http://maven.oschina.net/content/groups/public/</url>
			<releases>
			</releases>
			<snapshots>
			</snapshots>
		</repository>
	</repositories>

	<pluginRepositories>
		<pluginRepository>
			<id>nexus</id>
			<name>local private nexus</name>
			<url>http://maven.oschina.net/content/groups/public/</url>
			<releases>
			</releases>
			<snapshots>
				<enabled>false</enabled>
			</snapshots>
		</pluginRepository>
	</pluginRepositories>

	<distributionManagement>
		<repository>
			<id>howbuy-release</id>
			<name>howbuy-release</name>
			<url>http://nx-java.howbuy.pa/repository/howbuy-release/</url>
		</repository>
		<snapshotRepository>
			<id>howbuy-snapshot</id>
			<name>howbuy-snapshot</name>
			<url>http://nx-java.howbuy.pa/repository/howbuy-snapshot/</url>
		</snapshotRepository>
	</distributionManagement>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>2.3.2</version>
				<configuration>
					<source>${java.version}</source>
					<target>${java.version}</target>
					<encoding>UTF-8</encoding>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<version>2.5</version>
				<configuration>
					
					<includes>
						<include>**/*TestM.java</include>
					</includes>
				</configuration>
			</plugin>
			<plugin>
	            <groupId>org.apache.maven.plugins</groupId>
	            <artifactId>maven-jar-plugin</artifactId>
	            <configuration>
	                <archive>
	                    <manifestEntries>
	                        <Package-Stamp>${parelease}</Package-Stamp>
	                    </manifestEntries>
	                </archive>
	            </configuration>
	        </plugin>
		</plugins>
	</build>

	</project>