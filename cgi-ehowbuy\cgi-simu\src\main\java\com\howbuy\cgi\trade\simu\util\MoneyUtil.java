package com.howbuy.cgi.trade.simu.util;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.HashMap;
import java.util.Map;

/**
 * @description:货币工具类
 * @reason:
 * <AUTHOR>
 * @date 2017年9月26日 上午9:40:52
 * @since JDK 1.6
 */
public final class MoneyUtil {

	public static String digitUppercase(String num) {
		String[] digit = { "零", "壹", "贰", "叁", "肆", "伍", "陆", "柒", "捌", "玖" };
		// 把钱数分成段,每四个一段,实际上得到的是一个二维数组
		String[] unit1 = { "", "拾", "佰", "仟" };
		// 把钱数分成段,每四个一段,实际上得到的是一个二维数组
		String[] unit2 = { "元", "万", "亿", "万亿" };
		BigDecimal bigDecimal = new BigDecimal(num);
		bigDecimal = bigDecimal.multiply(new BigDecimal(100));
		String strVal = String.valueOf(bigDecimal.toBigInteger());
		// 整数部分
		String head = strVal.substring(0, strVal.length() - 2);
		// 小数部分
		String end = strVal.substring(strVal.length() - 2);
		String endMoney = "";
		StringBuilder headMoney = new StringBuilder();
		if ("00".equals(end)) {
			endMoney = "整";
		} else {
			if (!"0".equals(end.substring(0, 1))) {
				endMoney += digit[Integer.parseInt(end.substring(0, 1))] + "角";
			} else if ("0".equals(end.substring(0, 1)) && !"0".equals(end.substring(1, 2))) {
				endMoney += "零";
			}
			if (!"0".equals(end.substring(1, 2))) {
				endMoney += digit[Integer.parseInt(end.substring(1, 2))] + "分";
			}
		}
		char[] chars = head.toCharArray();
		// 段位置是否已出现zero
		Map<String, Boolean> map = new HashMap<String, Boolean>();
		// 0连续出现标志
		boolean zeroKeepFlag = false;
		int vidxtemp = 0;
		for (int i = 0; i < chars.length; i++) {
			// 段内位置 unit1
			int idx = (chars.length - 1 - i) % 4;
			// 段位置 unit2
			int vidx = (chars.length - 1 - i) / 4;
			String s = digit[Integer.parseInt(String.valueOf(chars[i]))];
			if (!"零".equals(s)) {
				headMoney.append(s).append(unit1[idx]).append(unit2[vidx]);
				zeroKeepFlag = false;
			} else if (i == chars.length - 1 || map.get("zero" + vidx) != null) {
				headMoney.append("");
			} else {
				headMoney.append(s);
				zeroKeepFlag = true;
				// 该段位已经出现0；
				map.put("zero" + vidx, true);
			}
			if (vidxtemp != vidx || i == chars.length - 1) {
				headMoney = new StringBuilder(headMoney.toString().replaceAll(unit2[vidx], ""));
				headMoney.append(unit2[vidx]);
			}
			if (zeroKeepFlag && (chars.length - 1 - i) % 4 == 0) {
				headMoney = new StringBuilder(headMoney.toString().replaceAll("零", ""));
			}
		}
		return headMoney + endMoney;
	}

	/**
	 * 将一个数值转为千分位字符串
	 * @param amount 金额
	 * @param decimalPlaces 小数位
	 * @param roundingMode 舍入模式
	 * @return 字符串
	 */
	public static String format(BigDecimal amount, int decimalPlaces, RoundingMode roundingMode) {
		if (amount == null) {
			return "0";
		}

		StringBuilder pattern = new StringBuilder("#,##0");
		if (decimalPlaces > 0) {
			pattern.append(".");
			for (int i = 0; i < decimalPlaces; i++) {
				pattern.append("0");
			}
		}

		DecimalFormat df = new DecimalFormat(pattern.toString());
		df.setRoundingMode(roundingMode);
		return df.format(amount);
	}

}
