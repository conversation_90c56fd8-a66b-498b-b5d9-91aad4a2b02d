### 持仓详情页接口实现文档

- 请求地址

| 类名                                                         | 请求方式 | 方法名                         | 描述       |
| :----------------------------------------------------------- | -------- | :----------------------------- | :--------- |
| com.howbuy.cgi.trade.simu.controller.index.IndexSimuController | post     | `/simu/user/balancedetail.htm` | 持仓详情页 |

- 入参 

| 字段     | 字段注释 | 类型   | 是否必填 |
| :------- | :------- | :----- | :------- |
| fundCode | 基金代码 | String | 是       |
| hboneNo  | 一账通   | String | 是       |
| custNo   | 客户号   | String | 是       |

- 出参

SimuBalanceIndexVo 

| 字段                | 字段注释                                                     | 类型          | 备注 |
| :------------------ | :----------------------------------------------------------- | :------------ | :--- |
| abnormalState       | 是否存在异常,持仓中只要有一笔存在异常，总资产就需显示计算中标识;1:是,0:否 | String        |      |
| isNeedAuth          | 是否需要授权:1- 是; 0- 否                                    | String        |      |
| serverDate          | 服务器日期                                                   | String        |      |
| isDataAuth          | 是否签署数据授权,1:已授权,0:没有授权                         | String        |      |
| isHkDataQuarantine  | 是否香港数据隔离,1:是,0:否                                   | String        |      |
| isnologinBind       | 是否不绑定定登录，用于特定用户不支持微信绑定自登陆,1:不绑定自登陆,0:绑定自登陆 | String        |      |
| highFundInvPlanFlag | 是否含有私募定投 1:是,0:否                                   | String        |      |
| showShouYi          | 是否展示收益分析入口                                         | String        |      |
| showLiCai           | 是否展示理财分析入口                                         | String        |      |
| balanceFund         | 持仓基金信息                                                 | BalanceFundVo |      |
| noTxAcctNo          | 公募未开户 1 是 0 否                                         | String        |      |

BalanceFundVo

| 字段                       | 字段注释                                                     | 类型                       | 备注 |
| -------------------------- | ------------------------------------------------------------ | -------------------------- | ---- |
| fundCode                   | 基金代码                                                     | String                     |      |
| strategy                   | 产品策略,需新增:  股票型 , 股权型 ,CTA, 另类, 固收与中性,多策略 | String                     |      |
| fundNameAbbr               | 基金简称                                                     | String                     |      |
| currentAsset               | 当前收益（人民币）如果是分期成立的,这里是子基金中当前收益之和 | BigDecimal                 |      |
| currentAssetStr            | 当前收益（展示字段）如果是分期成立的,这里是子基金中当前收益之和 | String                     |      |
| totalAsset                 | 总资产                                                       | BigDecimal                 |      |
| totalAssetStr              | 总资产（展示字段）                                           | String                     |      |
| yieldRate                  | 持仓收益率,如果是分期成立的,母基金维度是子基金中的最大的持仓收益率 | BigDecimal                 |      |
| yieldRateStr               | 持仓收益率（展示字段）,如果是分期成立的,母基金维度这个字段是子基金中的最大的持仓收益率 | String                     |      |
| cashCollection             | 回款金额回款金额,分期成立的母基金维度是子基金的回款金额之和  | BigDecimal                 |      |
| cashCollectionStr          | 回款金额展示字段                                             | String                     |      |
| cashCollectionRatio        | 回款比例,分期成立的,母基金没有值                             | String                     |      |
| currencyStr                | 币种展示字段                                                 | String                     |      |
| currency                   | 币种                                                         | String                     |      |
| navDivFlag                 | 净值分红标识,0-否，1-是                                      | String                     |      |
| valueDate                  | 起息日                                                       | String                     |      |
| dueDate                    | 到期日                                                       | String                     |      |
| productType                | 产品类型,7-一对多专户,11-私募                                | String                     |      |
| productSubType             | 产品二级类型:0-现金管理,1-债券,2-类固定收益,3-对冲策略,4-股票策略,5-股权,6-房地产基金,7-其他 | String                     |      |
| abnormalState              | 异常标识 0-否; 1-是                                          | String                     |      |
| crisisFlag                 | 清算标识 0-否; 1-是                                          | String                     |      |
| qianXiFlag                 | 千禧年待投产品标识 0-否; 1-是                                | String                     |      |
| marketValue                | 总市值                                                       | BigDecimal                 |      |
| marketValueStr             | 总市值(展示字段)                                             | String                     |      |
| custRepurchaseProtocol     | 客户复购协议                                                 | CustRepurchaseProtocolDto  |      |
| nav                        | 最新净值,分期成立的没有值,在子基金中展示                     | BigDecimal                 |      |
| navStr                     | 最新净值,展示字段                                            | String                     |      |
| navDt                      | 最新净值日期yyyyMMdd,分期成立的没有值,在子基金中展示,本年度只有MMdd | String                     |      |
| incomeCalStat              | 收益计算状态 0-计算中;1-计算成功                             | String                     |      |
| productDuration            | 产品存续期限(类似于5+3+2这种说明)                            | String                     |      |
| cpqxsm                     | 产品期限说明                                                 | String                     |      |
| stageEstablishFlag         | 是否分期成立,1-是;0-否                                       | String                     |      |
| balanceFundItemDtlList     | 子产品信息                                                   | List<BalanceFundItemDtlVo> |      |
| paidTotalAmt               | 实缴金额,分期成立的母基金维度是子基金的实缴金额之和          | BigDecimal                 |      |
| paidTotalAmtStr            | 实缴金额展示字段                                             | String                     |      |
| paidSubTotalRatio          | 实缴百分比,分期成立的,母基金维度没有值,在子基金维度展示      | String                     |      |
| dayIncome                  | 日收益,分期成立的母基金维度是子基金之和                      | BigDecimal                 |      |
| dayIncomeStr               | 日收益展示字段,分期成立的母基金维度是子基金之和              | String                     |      |
| accumIncome                | 累计收益,分期成立的母基金维度是子基金之和                    | BigDecimal                 |      |
| accumIncomeStr             | 累计收益,展示字段,分期成立的母基金维度是子基金之和           | String                     |      |
| incomeLatestDay            | 最新收益日期,yyyyMMdd,分期成立的母基金维度没值,本年度是MMdd  | String                     |      |
| unConfirmedAmt             | 待确认买入金额                                               | BigDecimal                 |      |
| balanceFundUnConfirmInfo   | 在途订单信息信息                                             | BalanceFundUnConfirmVo     |      |
| balanceFundUnRefundInfo    | 待资金到账订单信息                                           | BalanceFundUnRefundInfoVo  |      |
| balanceVol                 | 持仓份额,分期成立的,是子基金份额之和                         | BigDecimal                 |      |
| balanceVolStr              | 持仓份额,展示字段                                            | String                     |      |
| unitBalanceCostExFee       | 单位持仓成本(当前成本)                                       | BigDecimal                 |      |
| unitBalanceCostExFeeRmb    | 单位持仓成本(人民币)                                         | BigDecimal                 |      |
| unitBalanceCostExFeeRmbStr | 单位持仓成本(人民币),展示字段                                | String                     |      |

BalanceFundItemDtlVo

| 字段                       | 字段注释                                                     | 类型       | 备注 |
| -------------------------- | ------------------------------------------------------------ | ---------- | ---- |
| fundCode                   | 基金代码                                                     | String     |      |
| fundSubCode                | 子基金代码                                                   | String     |      |
| currentAsset               | 当前收益（人民币）如果是分期成立的,这里是子基金中当前收益之和 | BigDecimal |      |
| currentAssetStr            | 当前收益（展示字段）如果是分期成立的,这里是子基金中当前收益之和 | String     |      |
| yieldRate                  | 持仓收益率                                                   | BigDecimal |      |
| yieldRateStr               | 持仓收益率（展示字段）                                       | String     |      |
| cashCollection             | 私募股权回款金额                                             | BigDecimal |      |
| cashCollectionStr          | 私募股权回款金额（展示字段）                                 | String     |      |
| cashCollectionRatio        | 回款比例                                                     | String     |      |
| navDivFlag                 | 净值分红标识,0-否，1-是                                      | String     |      |
| valueDate                  | 起息日                                                       | String     |      |
| dueDate                    | 到期日                                                       | String     |      |
| abnormalState              | 异常标识 0-否; 1-是                                          | String     |      |
| marketValue                | 市值                                                         | BigDecimal |      |
| marketValueStr             | 总市值(展示字段)                                             | String     |      |
| nav                        | 最新净值,分期成立的没有值,在子基金中展示                     | String     |      |
| navDt                      | 最新净值日期yyyyMMdd,本年度,只有MMdd                         | String     |      |
| navStr                     | 最新净值,展示字段                                            | String     |      |
| incomeCalStat              | 收益计算状态 0-计算中;1-计算成功                             | String     |      |
| productDuration            | 产品存续期限(类似于5+3+2这种说明)                            | String     |      |
| cpqxsm                     | 产品期限说明                                                 | String     |      |
| stageEstablishFlag         | 是否分期成立,1-是;0-否                                       | String     |      |
| paidTotalAmt               | 实缴金额                                                     | BigDecimal |      |
| paidTotalAmtStr            | 实缴金额展示字段                                             | String     |      |
| paidSubTotalRatio          | 实缴百分比                                                   | String     |      |
| dayIncome                  | 日收益                                                       | BigDecimal |      |
| dayIncomeStr               | 日收益,展示字段                                              | String     |      |
| dayIncomeRate              | 日收益率                                                     | BigDecimal |      |
| dayIncomeRateStr           | 日收益率,展示字段                                            | String     |      |
| accumIncome                | 累计收益,分期成立的母基金维度是子基金之和                    | BigDecimal |      |
| accumIncomeStr             | 累计收益,展示字段,分期成立的母基金维度是子基金之和           | String     |      |
| incomeLatestDay            | 最新收益日期,yyyyMMdd,本年度是MMdd                           | String     |      |
| balanceVol                 | 持仓份额                                                     | BigDecimal |      |
| balanceVolStr              | 持仓份额,展示字段                                            | String     |      |
| unitBalanceCostExFee       | 单位持仓成本(当前成本)                                       | BigDecimal |      |
| unitBalanceCostExFeeRmb    | 单位持仓成本(人民币)                                         | BigDecimal |      |
| unitBalanceCostExFeeRmbStr | 单位持仓成本(人民币),展示字段                                | String     |      |

BalanceFundUnConfirmVo

| 字段                | 字段注释                                           | 类型                 | 备注                           |
| ------------------- | -------------------------------------------------- | -------------------- | ------------------------------ |
| allIsRedeem         | 是否全赎,1:是,0:不是                               | String               |                                |
| allSubmitRedeem     | 是否全部上报,1:是,0:不是                           | String               |                                |
| maxRedeemSubmitTaDt | 最大赎回上报日                                     | String               |                                |
| totalUnConfirmAmt   | 总待确认金额:申请净金额-储蓄罐预约冻结金额(人民币) | BigDecimal           |                                |
| totalSellVol        | 总赎回份额                                         | BigDecimal           |                                |
| buyUnConfirmList    | 买入到确认交易                                     | List<BuyUnConfirmOrderVo>  | 需要新增BuyUnConfirmOrderVo类  |
| sellUnConfirmList   | 卖出待确认交易                                     | List<SellUnConfirmOrderVo> | 需要新增SellUnConfirmOrderVo类 |
| buyUnConfirmNum     | 买入待确认数量                                     | int                  |                                |
| sellUnConfirmNum    | 卖出待确认数量                                     | int                  |                                |

BuyUnConfirmOrderVo

| 字段         | 字段注释     | 类型       | 备注                                                         |
| ------------ | ------------ | ---------- | ------------------------------------------------------------ |
| dealNo       | 订单号       | String     |                                                              |
| unConfirmAmt | 未确认金额   | BigDecimal |                                                              |
| mBusiCode    | 业务类型     | String     |                                                              |
| mBusiCodeStr | 业务类型中文 | String     | com.howbuy.tms.common.enums.busi.BusinessCodeEnum.getName(mBusiCode) |

SellUnConfirmOrderVo

| 字段             | 字段注释                                               | 类型       | 备注                                                         |
| ---------------- | ------------------------------------------------------ | ---------- | ------------------------------------------------------------ |
| dealNo           | 订单号                                                 | String     |                                                              |
| mBusiCode        | 业务类型                                               | String     |                                                              |
| mBusiCodeStr     | 业务类型中文                                           | String     | com.howbuy.tms.common.enums.busi.BusinessCodeEnum.getName(mBusiCode) |
| appVol           | 申请份额                                               | BigDecimal |                                                              |
| submitTaDt       | 上报日,yyyyMMdd                                        | String     |                                                              |
| notifySubmitFlag | 通知上报标记:0-无需通知,1-未通知,2-已通知,3-需重新通知 | String     |                                                              |

BalanceFundUnRefundInfoVo

| 字段                     | 字段注释           | 类型                       | 备注                           |
| ------------------------ | ------------------ | -------------------------- | ------------------------------ |
| totalWaitRefundAmt       | 总资金到账金额     | BigDecimal                 |                                |
| fundArrivalMsg           | 资金到账提醒文案   | String                     |                                |
| waitRefundOrderDtlVoList | 待资金到账订单明细 | List<WaitRefundOrderDtlVo> | 需要新增WaitRefundOrderDtlVo类 |
| waitRefundNum            | 待资金到账笔数     | int                        |                                |

WaitRefundOrderDtlVo

| 字段            | 字段注释                   | 类型       | 备注                                                         |
| --------------- | -------------------------- | ---------- | ------------------------------------------------------------ |
| dealNo          | 订单号                     | String     |                                                              |
| mBusiCode       | 业务类型                   | String     |                                                              |
| mBusiCodeStr    | 业务类型中文               | String     | com.howbuy.tms.common.enums.busi.BusinessCodeEnum.getName(mBusiCode) |
| refundAmt       | 资金到账金额               | BigDecimal |                                                              |
| redeemDirection | 赎回去向,0-银行卡,1-储蓄罐 | String     |                                                              |

CustRepurchaseProtocolDto

| 字段                  | 字段注释                                  | 类型       | 备注 |
| --------------------- | ----------------------------------------- | ---------- | ---- |
| txAcctNo              | 交易账号                                  | String     |      |
| repurchaseType        | 复购类型 0-全部赎回 1-部分复购 2-全部复购 | String     |      |
| fundCode              | 产品代码                                  | String     |      |
| repurchaseProtocolNo  | 复购协议号                                | String     |      |
| repurchaseVol         | 复购份额                                  | BigDecimal |      |
| endModifyDt           | 截止前端日期                              | String     |      |
| canModify             | 是否可以修改 0-否 1-是                    | String     |      |
| productRepurchaseFlag | 产品复购配置标识 0-否; 1-是               | String     |      |

- 代码逻辑

  **1. 初始化响应对象**
  ```java
  SimuBalanceIndexVo balanceVo = new SimuBalanceIndexVo();
  ```

  **2. 查询数据授权状态**
  ```java
  AcctDataAuthDto acctDataAuthInfo = accCenterService.getAcctDataAuthInfo(paramCmd.getHboneNo());
  balanceVo.setIsDataAuth(acctDataAuthInfo.getIsDataAuth());
  balanceVo.setIsHkDataQuarantine(acctDataAuthInfo.getIsHkDataQuarantine());
  ```

  **3. 构建持仓查询参数**
  ```java
  QueryBalanceParamCmd queryBalanceParamCmd = new QueryBalanceParamCmd();
  queryBalanceParamCmd.setTxAcctNo(paramCmd.getTxAcctNo());
  queryBalanceParamCmd.setHboneNo(paramCmd.getHboneNo());
  queryBalanceParamCmd.setProductCode(paramCmd.getFundCode());
  queryBalanceParamCmd.setIp(paramCmd.getIp());
  queryBalanceParamCmd.setAcctDataAuthInfo(acctDataAuthInfo);
  // 根据数据授权设置过滤标识
  if (YesOrNoEnum.YES.getCode().equals(acctDataAuthInfo.getIsDataAuth())) {
      queryBalanceParamCmd.setNotFilterHkFund(YesOrNoEnum.YES.getCode());
      queryBalanceParamCmd.setNotFilterHzFund(YesOrNoEnum.YES.getCode());
      // 如果香港数据隔离,就过滤香港的产品
      if (YesOrNoEnum.YES.getCode().equals(acctDataAuthInfo.getIsHkDataQuarantine())) {
          queryBalanceParamCmd.setNotFilterHkFund(YesOrNoEnum.NO.getCode());
      }
  } else {
      queryBalanceParamCmd.setNotFilterHkFund(YesOrNoEnum.NO.getCode());
      queryBalanceParamCmd.setNotFilterHzFund(YesOrNoEnum.NO.getCode());
  }
  ```

  **4. 查询持仓数据**
  ```java
  QueryAcctBalanceResponse balanceResponse = queryBalanceVolService.queryNewAcctBalance(queryBalanceParamCmd);
  if (CollectionUtils.isEmpty(balanceResponse.getBalanceList())) {
      return balanceVo; // 无持仓数据直接返回
  }
  ```

  **5. 查询私募定投标识**
  ```java
  String highFundInvPlanFlag = queryHighFundInvPlanFlag(paramCmd);
  balanceVo.setHighFundInvPlanFlag(highFundInvPlanFlag);
  ```

  **6. 并行查询产品上下文信息**
  ```java
  QueryBalanceDetailContext context = new QueryBalanceDetailContext();
  List<String> productCodeList = Arrays.asList(paramCmd.getFundCode());
  List<HowbuyBaseTask> taskList = new ArrayList<>();

  // 查询购买状态
  taskList.add(new QueryBuyStatusTask(context, paramCmd.getIp(), paramCmd.getTxAcctNo(),
                                     productCodeList, queryBuyFundStatusFacade));
  // 查询赎回状态
  taskList.add(new RdmFundStatusTask(productCodeList, queryBalanceVolService,
                                    queryRedeemFundStatusFacade, context, paramCmd.getIp(), paramCmd.getTxAcctNo()));
  // 查询客户复购协议
  taskList.add(new QueryCustRepurchaseProtocolTask(paramCmd.getTxAcctNo(), productCodeList,
                                                  queryCustRepurchaseProtocolFacade, context));
  // 查询产品基础信息
  taskList.add(new QueryProductBaseInfoTask(highProductService, productCodeList, context));
  // 查询产品打标信息
  taskList.add(new QueryProductTagInfoTask(highProductService, productCodeList, context));
  // 查询最近预约日历
  taskList.add(new QueryLatestByAppintDtTask(productCodeList, context, highProductService));
  // 查询产品净值状态
  taskList.add(new QueryProductStatusTask(context, highProductService, productCodeList));
  // 查询产品策略
  taskList.add(new QueryStrategyTask(productCodeList, smZcpzService, context));
  // 查询产品分类
  taskList.add(new QueryCpflTask(productCodeList, context, jjxxService));

  howBuyRunTaskUil.runTask(taskList); // 并行执行所有任务
  ```

  **7. 处理持仓数据**
  ```java
  List<BalanceBean> balanceList = balanceResponse.getBalanceList();
  BalanceFundVo balanceFundVo = new BalanceFundVo();

  // 判断是否分期成立产品（同一个fundCode有多条记录）
  if (balanceList.size() > 1) {
      // 分期成立产品处理
      processStageEstablishFund(balanceList, balanceFundVo, context);
  } else {
      // 非分期成立产品处理
      processNormalFund(balanceList.get(0), balanceFundVo, context);
  }

  balanceVo.setBalanceFund(balanceFundVo);
  ```

  **8. 分期成立产品处理逻辑**
  ```java
  private void processStageEstablishFund(List<BalanceBean> balanceList, BalanceFundVo balanceFundVo, QueryBalanceDetailContext context) {
      BalanceBean firstBean = balanceList.get(0);

      // 设置基础信息（从第一个子基金获取）
      buildBalanceFundBaseInfo(context, balanceFundVo, firstBean, firstBean.getProductCode());

      // 处理子产品信息列表
      List<BalanceFundItemDtlVo> itemDtlList = new ArrayList<>();
      for (BalanceBean bean : balanceList) {
          BalanceFundItemDtlVo itemDtl = new BalanceFundItemDtlVo();
          // 复制子基金详细信息
          copyBeanToItemDtl(bean, itemDtl, context);
          itemDtlList.add(itemDtl);
      }
      balanceFundVo.setBalanceFundItemDtlList(itemDtlList);

      // 汇总计算母基金数据（求和或取最大值）
      aggregateStageEstablishData(balanceList, balanceFundVo);

      // 处理在途订单和待资金到账信息
      setRefundAndUnConfirm(balanceList, balanceFundVo);
  }
  ```

  **9. 非分期成立产品处理逻辑**
  ```java
  private void processNormalFund(BalanceBean balanceBean, BalanceFundVo balanceFundVo, QueryBalanceDetailContext context) {
      // 设置基础信息
      buildBalanceFundBaseInfo(context, balanceFundVo, balanceBean, balanceBean.getProductCode());

      // 直接复制单一产品数据
      copyBeanToBalanceFund(balanceBean, balanceFundVo, context);

      // 处理在途订单和待资金到账信息
      List<BalanceBean> singleList = Arrays.asList(balanceBean);
      setRefundAndUnConfirm(singleList, balanceFundVo);
  }
  ```

  **10. 数据汇总逻辑（分期成立产品）**
  ```java
  private void aggregateStageEstablishData(List<BalanceBean> balanceList, BalanceFundVo balanceFundVo) {
      // 需要求和的字段
      BigDecimal totalCurrentAsset = BigDecimal.ZERO;      // 当前收益之和
      BigDecimal totalAsset = BigDecimal.ZERO;             // 总资产之和
      BigDecimal totalCashCollection = BigDecimal.ZERO;    // 回款金额之和
      BigDecimal totalPaidTotalAmt = BigDecimal.ZERO;      // 实缴金额之和
      BigDecimal totalDayIncome = BigDecimal.ZERO;         // 日收益之和
      BigDecimal totalAccumIncome = BigDecimal.ZERO;       // 累计收益之和
      BigDecimal totalBalanceVol = BigDecimal.ZERO;        // 持仓份额之和

      // 需要取最大值的字段
      BigDecimal maxYieldRate = null;                      // 持仓收益率最大值

      for (BalanceBean bean : balanceList) {
          // 求和计算
          if (bean.getCurrentAsset() != null) {
              totalCurrentAsset = totalCurrentAsset.add(bean.getCurrentAsset());
          }
          if (bean.getMarketValue() != null) {
              totalAsset = totalAsset.add(bean.getMarketValue());
          }
          if (bean.getCashCollection() != null) {
              totalCashCollection = totalCashCollection.add(bean.getCashCollection());
          }
          if (bean.getPaidTotalAmt() != null) {
              totalPaidTotalAmt = totalPaidTotalAmt.add(bean.getPaidTotalAmt());
          }
          if (bean.getDayIncome() != null) {
              totalDayIncome = totalDayIncome.add(bean.getDayIncome());
          }
          if (bean.getAccumIncome() != null) {
              totalAccumIncome = totalAccumIncome.add(bean.getAccumIncome());
          }
          if (bean.getBalanceVol() != null) {
              totalBalanceVol = totalBalanceVol.add(bean.getBalanceVol());
          }

          // 取最大值计算
          if (bean.getYieldRate() != null) {
              if (maxYieldRate == null || bean.getYieldRate().compareTo(maxYieldRate) > 0) {
                  maxYieldRate = bean.getYieldRate();
              }
          }
      }

      // 设置汇总结果
      balanceFundVo.setCurrentAsset(totalCurrentAsset);
      balanceFundVo.setTotalAsset(totalAsset);
      balanceFundVo.setCashCollection(totalCashCollection);
      balanceFundVo.setPaidTotalAmt(totalPaidTotalAmt);
      balanceFundVo.setDayIncome(totalDayIncome);
      balanceFundVo.setAccumIncome(totalAccumIncome);
      balanceFundVo.setBalanceVol(totalBalanceVol);
      balanceFundVo.setYieldRate(maxYieldRate);

      // 格式化展示字段
      balanceFundVo.setCurrentAssetStr(formatAmount(totalCurrentAsset));
      balanceFundVo.setTotalAssetStr(formatAmount(totalAsset));
      balanceFundVo.setCashCollectionStr(formatAmount(totalCashCollection));
      balanceFundVo.setPaidTotalAmtStr(formatAmount(totalPaidTotalAmt));
      balanceFundVo.setDayIncomeStr(formatAmount(totalDayIncome));
      balanceFundVo.setAccumIncomeStr(formatAmount(totalAccumIncome));
      balanceFundVo.setBalanceVolStr(formatAmount(totalBalanceVol));
      balanceFundVo.setYieldRateStr(formatPercent(maxYieldRate));
  }
  ```

  **11. 在途订单和待资金到账处理逻辑**
  ```java
  private void setRefundAndUnConfirm(List<BalanceBean> balanceList, BalanceFundVo balanceFundVo) {
      // 初始化在途订单信息
      BalanceFundUnConfirmVo balanceFundUnConfirmInfo = new BalanceFundUnConfirmVo();
      balanceFundVo.setBalanceFundUnConfirmInfo(balanceFundUnConfirmInfo);

      // 初始化待资金到账信息
      BalanceFundUnRefundInfoVo balanceFundUnRefundInfo = new BalanceFundUnRefundInfoVo();
      balanceFundVo.setBalanceFundUnRefundInfo(balanceFundUnRefundInfo);

      // 汇总所有子基金的在途和待资金到账信息
      for (BalanceBean bean : balanceList) {
          // 处理在途订单信息
          BalanceFundUnConfirmInfoDTO subUnConfirmInfo = bean.getBalanceFundUnConfirmInfo();
          if (subUnConfirmInfo != null) {
              addUnConfirmInfo(balanceFundUnConfirmInfo, subUnConfirmInfo);
          }

          // 处理待资金到账信息
          BalanceFundUnRefundInfoDTO subUnRefundInfo = bean.getBalanceFundUnRefundInfo();
          if (subUnRefundInfo != null) {
              addUnRefundInfo(balanceFundUnRefundInfo, subUnRefundInfo);
          }
      }
  }
  ```

  **12. 在途订单信息汇总**
  ```java
  private void addUnConfirmInfo(BalanceFundUnConfirmVo parentInfo, BalanceFundUnConfirmInfoDTO subInfo) {
      if (subInfo == null) return;

      // 1. 买入待确认订单处理
      List<BuyUnConfirmOrderDTO> subBuyList = subInfo.getBuyUnConfirmList();
      if (CollectionUtils.isNotEmpty(subBuyList)) {
          // 汇总总待确认金额
          BigDecimal totalUnConfirmAmt = parentInfo.getTotalUnConfirmAmt();
          if (totalUnConfirmAmt == null) totalUnConfirmAmt = BigDecimal.ZERO;
          totalUnConfirmAmt = totalUnConfirmAmt.add(subInfo.getTotalUnConfirmAmt());
          parentInfo.setTotalUnConfirmAmt(totalUnConfirmAmt);

          // 汇总买入待确认数量
          parentInfo.setBuyUnConfirmNum(parentInfo.getBuyUnConfirmNum() + subInfo.getBuyUnConfirmNum());

          // 合并买入订单列表
          List<BuyUnConfirmOrderVo> parentBuyList = parentInfo.getBuyUnConfirmList();
          if (parentBuyList == null) {
              parentBuyList = new ArrayList<>();
              parentInfo.setBuyUnConfirmList(parentBuyList);
          }

          for (BuyUnConfirmOrderDTO subOrder : subBuyList) {
              BuyUnConfirmOrderVo orderVo = new BuyUnConfirmOrderVo();
              orderVo.setDealNo(subOrder.getDealNo());
              orderVo.setUnConfirmAmt(subOrder.getUnConfirmAmt());
              orderVo.setMBusiCode(subOrder.getMBusiCode());
              orderVo.setMBusiCodeStr(BusinessCodeEnum.getName(subOrder.getMBusiCode()));
              parentBuyList.add(orderVo);
          }
      }

      // 2. 卖出待确认订单处理
      List<SellUnConfirmOrderDTO> subSellList = subInfo.getSellUnConfirmList();
      if (CollectionUtils.isNotEmpty(subSellList)) {
          // 设置赎回相关标识（取子基金的值，因为只有一个子基金会有赎回订单）
          parentInfo.setAllIsRedeem(subInfo.getAllIsRedeem());
          parentInfo.setAllSubmitRedeem(subInfo.getAllSubmitRedeem());
          parentInfo.setMaxRedeemSubmitTaDt(subInfo.getMaxRedeemSubmitTaDt());
          parentInfo.setTotalSellVol(subInfo.getTotalSellVol());
          parentInfo.setSellUnConfirmNum(subInfo.getSellUnConfirmNum());

          // 合并卖出订单列表
          List<SellUnConfirmOrderVo> parentSellList = parentInfo.getSellUnConfirmList();
          if (parentSellList == null) {
              parentSellList = new ArrayList<>();
              parentInfo.setSellUnConfirmList(parentSellList);
          }

          for (SellUnConfirmOrderDTO subOrder : subSellList) {
              SellUnConfirmOrderVo orderVo = new SellUnConfirmOrderVo();
              orderVo.setDealNo(subOrder.getDealNo());
              orderVo.setMBusiCode(subOrder.getMBusiCode());
              orderVo.setMBusiCodeStr(BusinessCodeEnum.getName(subOrder.getMBusiCode()));
              orderVo.setAppVol(subOrder.getAppVol());
              orderVo.setSubmitTaDt(subOrder.getSubmitTaDt());
              orderVo.setNotifySubmitFlag(subOrder.getNotifySubmitFlag());
              parentSellList.add(orderVo);
          }
      }
  }
  ```

  **13. 待资金到账信息汇总**
  ```java
  private void addUnRefundInfo(BalanceFundUnRefundInfoVo parentInfo, BalanceFundUnRefundInfoDTO subInfo) {
      if (subInfo == null) return;

      List<RefundDealOrderDTO> subRefundList = subInfo.getRefundDealOrderList();
      if (CollectionUtils.isNotEmpty(subRefundList)) {
          // 汇总总资金到账金额
          BigDecimal totalWaitRefundAmt = parentInfo.getTotalWaitRefundAmt();
          if (totalWaitRefundAmt == null) totalWaitRefundAmt = BigDecimal.ZERO;
          totalWaitRefundAmt = totalWaitRefundAmt.add(subInfo.getTotalRefundAmt());
          parentInfo.setTotalWaitRefundAmt(totalWaitRefundAmt);

          // 汇总待资金到账笔数
          parentInfo.setWaitRefundNum(parentInfo.getWaitRefundNum() + subRefundList.size());

          // 设置资金到账提醒文案（取子基金的值）
          if (StringUtils.isNotBlank(subInfo.getFundArrivalMsg())) {
              parentInfo.setFundArrivalMsg(subInfo.getFundArrivalMsg());
          }

          // 合并待资金到账订单列表
          List<WaitRefundOrderDtlVo> parentRefundList = parentInfo.getWaitRefundOrderDtlVoList();
          if (parentRefundList == null) {
              parentRefundList = new ArrayList<>();
              parentInfo.setWaitRefundOrderDtlVoList(parentRefundList);
          }

          for (RefundDealOrderDTO subOrder : subRefundList) {
              WaitRefundOrderDtlVo orderVo = new WaitRefundOrderDtlVo();
              orderVo.setDealNo(subOrder.getDealNo());
              orderVo.setMBusiCode(subOrder.getMBusiCode());
              orderVo.setMBusiCodeStr(BusinessCodeEnum.getName(subOrder.getMBusiCode()));
              orderVo.setRefundAmt(subOrder.getRefundAmt());
              orderVo.setRedeemDirection(subOrder.getRedeemDirection());
              parentRefundList.add(orderVo);
          }
      }
  }
  ```

  **14. 设置SimuBalanceIndexVo其他字段**
  ```java
  // 设置服务器日期
  balanceVo.setServerDate(DateUtils.formatDate(new Date(), "yyyyMMdd"));

  // 设置异常状态（持仓中只要有一笔存在异常，就显示异常）
  boolean hasAbnormal = false;
  if (balanceFundVo.getStageEstablishFlag().equals(YesOrNoEnum.YES.getCode())) {
      // 分期成立产品：检查子产品是否有异常
      List<BalanceFundItemDtlVo> itemList = balanceFundVo.getBalanceFundItemDtlList();
      if (CollectionUtils.isNotEmpty(itemList)) {
          hasAbnormal = itemList.stream().anyMatch(item ->
              YesOrNoEnum.YES.getCode().equals(item.getAbnormalState()));
      }
  } else {
      // 非分期成立产品：直接检查异常状态
      hasAbnormal = YesOrNoEnum.YES.getCode().equals(balanceFundVo.getAbnormalState());
  }
  balanceVo.setAbnormalState(hasAbnormal ? YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode());

  // 设置是否需要授权（根据业务规则判断）
  balanceVo.setIsNeedAuth(determineNeedAuth(balanceFundVo, acctDataAuthInfo));

  // 设置是否不绑定自登录（根据用户配置）
  balanceVo.setIsnologinBind(getUserNoLoginBindFlag(paramCmd.getHboneNo()));

  // 设置收益分析和理财分析入口显示标识
  balanceVo.setShowShouYi(getShowShouYiFlag(balanceFundVo));
  balanceVo.setShowLiCai(getShowLiCaiFlag(balanceFundVo));

  // 设置公募未开户标识
  balanceVo.setNoTxAcctNo(getNoTxAcctFlag(paramCmd.getTxAcctNo()));
  ```

  **15. 关键业务逻辑说明**

  **分期成立产品判断逻辑：**
  - 根据balanceResponse.getBalanceList()的size判断
  - 如果size > 1，说明同一个fundCode有多个子基金，即分期成立产品
  - 如果size = 1，说明是普通的非分期成立产品

  **数据汇总规则：**
  - **求和字段**：当前收益、总资产、回款金额、实缴金额、日收益、累计收益、持仓份额
  - **取最大值字段**：持仓收益率（母基金维度取子基金中的最大值）
  - **特殊字段**：回款比例、实缴百分比、最新收益日期等在母基金维度不显示值

  **在途订单处理：**
  - 买入待确认：汇总所有子基金的买入订单，金额求和，订单列表合并
  - 卖出待确认：只有一个子基金会有赎回订单，直接取该子基金的值
  - 业务类型中文：通过BusinessCodeEnum.getName()获取

  **待资金到账处理：**
  - 总金额：所有子基金的待资金到账金额求和
  - 订单列表：合并所有子基金的待资金到账订单
  - 赎回方向：0-银行卡，1-储蓄罐

  **异常处理：**
  - 所有数据操作都需要进行空值判断
  - BigDecimal计算使用安全的add方法，初始值为ZERO
  - 集合操作前先判断isEmpty
  - 字符串操作使用StringUtils.isNotBlank判断

  **性能优化：**
  - 使用并行任务查询产品上下文信息，减少总体响应时间
  - 一次性查询所有需要的产品信息，避免循环查询
  - 合理使用缓存机制，减少重复查询
