### 持仓详情页接口实现文档

- 请求地址

| 类名                                                         | 请求方式 | 方法名                         | 描述       |
| :----------------------------------------------------------- | -------- | :----------------------------- | :--------- |
| com.howbuy.cgi.trade.simu.controller.index.IndexSimuController | post     | `/simu/user/balancedetail.htm` | 持仓详情页 |

- 入参 

| 字段     | 字段注释 | 类型   | 是否必填 |
| :------- | :------- | :----- | :------- |
| fundCode | 基金代码 | String | 是       |
| hboneNo  | 一账通   | String | 是       |
| custNo   | 客户号   | String | 是       |

- 出参

SimuBalanceIndexVo 

| 字段                | 字段注释                                                     | 类型          | 备注 |
| :------------------ | :----------------------------------------------------------- | :------------ | :--- |
| abnormalState       | 是否存在异常,持仓中只要有一笔存在异常，总资产就需显示计算中标识;1:是,0:否 | String        |      |
| isNeedAuth          | 是否需要授权:1- 是; 0- 否                                    | String        |      |
| serverDate          | 服务器日期                                                   | String        |      |
| isDataAuth          | 是否签署数据授权,1:已授权,0:没有授权                         | String        |      |
| isHkDataQuarantine  | 是否香港数据隔离,1:是,0:否                                   | String        |      |
| isnologinBind       | 是否不绑定定登录，用于特定用户不支持微信绑定自登陆,1:不绑定自登陆,0:绑定自登陆 | String        |      |
| highFundInvPlanFlag | 是否含有私募定投 1:是,0:否                                   | String        |      |
| showShouYi          | 是否展示收益分析入口                                         | String        |      |
| showLiCai           | 是否展示理财分析入口                                         | String        |      |
| balanceFund         | 持仓基金信息                                                 | BalanceFundVo |      |
| noTxAcctNo          | 公募未开户 1 是 0 否                                         | String        |      |

BalanceFundVo

| 字段                       | 字段注释                                                     | 类型                       | 备注 |
| -------------------------- | ------------------------------------------------------------ | -------------------------- | ---- |
| fundCode                   | 基金代码                                                     | String                     |      |
| strategy                   | 产品策略,需新增:  股票型 , 股权型 ,CTA, 另类, 固收与中性,多策略 | String                     |      |
| fundNameAbbr               | 基金简称                                                     | String                     |      |
| currentAsset               | 当前收益（人民币）如果是分期成立的,这里是子基金中当前收益之和 | BigDecimal                 |      |
| currentAssetStr            | 当前收益（展示字段）如果是分期成立的,这里是子基金中当前收益之和 | String                     |      |
| totalAsset                 | 总资产                                                       | BigDecimal                 |      |
| totalAssetStr              | 总资产（展示字段）                                           | String                     |      |
| yieldRate                  | 持仓收益率,如果是分期成立的,母基金维度是子基金中的最大的持仓收益率 | BigDecimal                 |      |
| yieldRateStr               | 持仓收益率（展示字段）,如果是分期成立的,母基金维度这个字段是子基金中的最大的持仓收益率 | String                     |      |
| cashCollection             | 回款金额回款金额,分期成立的母基金维度是子基金的回款金额之和  | BigDecimal                 |      |
| cashCollectionStr          | 回款金额展示字段                                             | String                     |      |
| cashCollectionRatio        | 回款比例,分期成立的,母基金没有值                             | String                     |      |
| currencyStr                | 币种展示字段                                                 | String                     |      |
| currency                   | 币种                                                         | String                     |      |
| navDivFlag                 | 净值分红标识,0-否，1-是                                      | String                     |      |
| valueDate                  | 起息日                                                       | String                     |      |
| dueDate                    | 到期日                                                       | String                     |      |
| productType                | 产品类型,7-一对多专户,11-私募                                | String                     |      |
| productSubType             | 产品二级类型:0-现金管理,1-债券,2-类固定收益,3-对冲策略,4-股票策略,5-股权,6-房地产基金,7-其他 | String                     |      |
| abnormalState              | 异常标识 0-否; 1-是                                          | String                     |      |
| crisisFlag                 | 清算标识 0-否; 1-是                                          | String                     |      |
| qianXiFlag                 | 千禧年待投产品标识 0-否; 1-是                                | String                     |      |
| marketValue                | 总市值                                                       | BigDecimal                 |      |
| marketValueStr             | 总市值(展示字段)                                             | String                     |      |
| custRepurchaseProtocol     | 客户复购协议                                                 | CustRepurchaseProtocolDto  |      |
| nav                        | 最新净值,分期成立的没有值,在子基金中展示                     | BigDecimal                 |      |
| navStr                     | 最新净值,展示字段                                            | String                     |      |
| navDt                      | 最新净值日期yyyyMMdd,分期成立的没有值,在子基金中展示,本年度只有MMdd | String                     |      |
| incomeCalStat              | 收益计算状态 0-计算中;1-计算成功                             | String                     |      |
| productDuration            | 产品存续期限(类似于5+3+2这种说明)                            | String                     |      |
| cpqxsm                     | 产品期限说明                                                 | String                     |      |
| stageEstablishFlag         | 是否分期成立,1-是;0-否                                       | String                     |      |
| balanceFundItemDtlList     | 子产品信息                                                   | List<BalanceFundItemDtlVo> |      |
| paidTotalAmt               | 实缴金额,分期成立的母基金维度是子基金的实缴金额之和          | BigDecimal                 |      |
| paidTotalAmtStr            | 实缴金额展示字段                                             | String                     |      |
| paidSubTotalRatio          | 实缴百分比,分期成立的,母基金维度没有值,在子基金维度展示      | String                     |      |
| dayIncome                  | 日收益,分期成立的母基金维度是子基金之和                      | BigDecimal                 |      |
| dayIncomeStr               | 日收益展示字段,分期成立的母基金维度是子基金之和              | String                     |      |
| accumIncome                | 累计收益,分期成立的母基金维度是子基金之和                    | BigDecimal                 |      |
| accumIncomeStr             | 累计收益,展示字段,分期成立的母基金维度是子基金之和           | String                     |      |
| incomeLatestDay            | 最新收益日期,yyyyMMdd,分期成立的母基金维度没值,本年度是MMdd  | String                     |      |
| unConfirmedAmt             | 待确认买入金额                                               | BigDecimal                 |      |
| balanceFundUnConfirmInfo   | 在途订单信息信息                                             | BalanceFundUnConfirmVo     |      |
| balanceFundUnRefundInfo    | 待资金到账订单信息                                           | BalanceFundUnRefundInfoVo  |      |
| balanceVol                 | 持仓份额,分期成立的,是子基金份额之和                         | BigDecimal                 |      |
| balanceVolStr              | 持仓份额,展示字段                                            | String                     |      |
| unitBalanceCostExFee       | 单位持仓成本(当前成本)                                       | BigDecimal                 |      |
| unitBalanceCostExFeeRmb    | 单位持仓成本(人民币)                                         | BigDecimal                 |      |
| unitBalanceCostExFeeRmbStr | 单位持仓成本(人民币),展示字段                                | String                     |      |

BalanceFundItemDtlVo

| 字段                       | 字段注释                                                     | 类型       | 备注 |
| -------------------------- | ------------------------------------------------------------ | ---------- | ---- |
| fundCode                   | 基金代码                                                     | String     |      |
| fundSubCode                | 子基金代码                                                   | String     |      |
| currentAsset               | 当前收益（人民币）如果是分期成立的,这里是子基金中当前收益之和 | BigDecimal |      |
| currentAssetStr            | 当前收益（展示字段）如果是分期成立的,这里是子基金中当前收益之和 | String     |      |
| yieldRate                  | 持仓收益率                                                   | BigDecimal |      |
| yieldRateStr               | 持仓收益率（展示字段）                                       | String     |      |
| cashCollection             | 私募股权回款金额                                             | BigDecimal |      |
| cashCollectionStr          | 私募股权回款金额（展示字段）                                 | String     |      |
| cashCollectionRatio        | 回款比例                                                     | String     |      |
| navDivFlag                 | 净值分红标识,0-否，1-是                                      | String     |      |
| valueDate                  | 起息日                                                       | String     |      |
| dueDate                    | 到期日                                                       | String     |      |
| abnormalState              | 异常标识 0-否; 1-是                                          | String     |      |
| marketValue                | 市值                                                         | BigDecimal |      |
| marketValueStr             | 总市值(展示字段)                                             | String     |      |
| nav                        | 最新净值,分期成立的没有值,在子基金中展示                     | String     |      |
| navDt                      | 最新净值日期yyyyMMdd,本年度,只有MMdd                         | String     |      |
| navStr                     | 最新净值,展示字段                                            | String     |      |
| incomeCalStat              | 收益计算状态 0-计算中;1-计算成功                             | String     |      |
| productDuration            | 产品存续期限(类似于5+3+2这种说明)                            | String     |      |
| cpqxsm                     | 产品期限说明                                                 | String     |      |
| stageEstablishFlag         | 是否分期成立,1-是;0-否                                       | String     |      |
| paidTotalAmt               | 实缴金额                                                     | BigDecimal |      |
| paidTotalAmtStr            | 实缴金额展示字段                                             | String     |      |
| paidSubTotalRatio          | 实缴百分比                                                   | String     |      |
| dayIncome                  | 日收益                                                       | BigDecimal |      |
| dayIncomeStr               | 日收益,展示字段                                              | String     |      |
| dayIncomeRate              | 日收益率                                                     | BigDecimal |      |
| dayIncomeRateStr           | 日收益率,展示字段                                            | String     |      |
| accumIncome                | 累计收益,分期成立的母基金维度是子基金之和                    | BigDecimal |      |
| accumIncomeStr             | 累计收益,展示字段,分期成立的母基金维度是子基金之和           | String     |      |
| incomeLatestDay            | 最新收益日期,yyyyMMdd,本年度是MMdd                           | String     |      |
| balanceVol                 | 持仓份额                                                     | BigDecimal |      |
| balanceVolStr              | 持仓份额,展示字段                                            | String     |      |
| unitBalanceCostExFee       | 单位持仓成本(当前成本)                                       | BigDecimal |      |
| unitBalanceCostExFeeRmb    | 单位持仓成本(人民币)                                         | BigDecimal |      |
| unitBalanceCostExFeeRmbStr | 单位持仓成本(人民币),展示字段                                | String     |      |

BalanceFundUnConfirmVo

| 字段                | 字段注释                                           | 类型                 | 备注                           |
| ------------------- | -------------------------------------------------- | -------------------- | ------------------------------ |
| allIsRedeem         | 是否全赎,1:是,0:不是                               | String               |                                |
| allSubmitRedeem     | 是否全部上报,1:是,0:不是                           | String               |                                |
| maxRedeemSubmitTaDt | 最大赎回上报日                                     | String               |                                |
| totalUnConfirmAmt   | 总待确认金额:申请净金额-储蓄罐预约冻结金额(人民币) | BigDecimal           |                                |
| totalSellVol        | 总赎回份额                                         | BigDecimal           |                                |
| buyUnConfirmList    | 买入到确认交易                                     | List<BuyUnConfirmOrderVo>  | 需要新增BuyUnConfirmOrderVo类  |
| sellUnConfirmList   | 卖出待确认交易                                     | List<SellUnConfirmOrderVo> | 需要新增SellUnConfirmOrderVo类 |
| buyUnConfirmNum     | 买入待确认数量                                     | int                  |                                |
| sellUnConfirmNum    | 卖出待确认数量                                     | int                  |                                |

BuyUnConfirmOrderVo

| 字段         | 字段注释     | 类型       | 备注                                                         |
| ------------ | ------------ | ---------- | ------------------------------------------------------------ |
| dealNo       | 订单号       | String     |                                                              |
| unConfirmAmt | 未确认金额   | BigDecimal |                                                              |
| mBusiCode    | 业务类型     | String     |                                                              |
| mBusiCodeStr | 业务类型中文 | String     | com.howbuy.tms.common.enums.busi.BusinessCodeEnum.getName(mBusiCode) |

SellUnConfirmOrderVo

| 字段             | 字段注释                                               | 类型       | 备注                                                         |
| ---------------- | ------------------------------------------------------ | ---------- | ------------------------------------------------------------ |
| dealNo           | 订单号                                                 | String     |                                                              |
| mBusiCode        | 业务类型                                               | String     |                                                              |
| mBusiCodeStr     | 业务类型中文                                           | String     | com.howbuy.tms.common.enums.busi.BusinessCodeEnum.getName(mBusiCode) |
| appVol           | 申请份额                                               | BigDecimal |                                                              |
| submitTaDt       | 上报日,yyyyMMdd                                        | String     |                                                              |
| notifySubmitFlag | 通知上报标记:0-无需通知,1-未通知,2-已通知,3-需重新通知 | String     |                                                              |

BalanceFundUnRefundInfoVo

| 字段                     | 字段注释           | 类型                       | 备注                           |
| ------------------------ | ------------------ | -------------------------- | ------------------------------ |
| totalWaitRefundAmt       | 总资金到账金额     | BigDecimal                 |                                |
| fundArrivalMsg           | 资金到账提醒文案   | String                     |                                |
| waitRefundOrderDtlVoList | 待资金到账订单明细 | List<WaitRefundOrderDtlVo> | 需要新增WaitRefundOrderDtlVo类 |
| waitRefundNum            | 待资金到账笔数     | int                        |                                |

WaitRefundOrderDtlVo

| 字段            | 字段注释                   | 类型       | 备注                                                         |
| --------------- | -------------------------- | ---------- | ------------------------------------------------------------ |
| dealNo          | 订单号                     | String     |                                                              |
| mBusiCode       | 业务类型                   | String     |                                                              |
| mBusiCodeStr    | 业务类型中文               | String     | com.howbuy.tms.common.enums.busi.BusinessCodeEnum.getName(mBusiCode) |
| refundAmt       | 资金到账金额               | BigDecimal |                                                              |
| redeemDirection | 赎回去向,0-银行卡,1-储蓄罐 | String     |                                                              |

CustRepurchaseProtocolDto

| 字段                  | 字段注释                                  | 类型       | 备注 |
| --------------------- | ----------------------------------------- | ---------- | ---- |
| txAcctNo              | 交易账号                                  | String     |      |
| repurchaseType        | 复购类型 0-全部赎回 1-部分复购 2-全部复购 | String     |      |
| fundCode              | 产品代码                                  | String     |      |
| repurchaseProtocolNo  | 复购协议号                                | String     |      |
| repurchaseVol         | 复购份额                                  | BigDecimal |      |
| endModifyDt           | 截止前端日期                              | String     |      |
| canModify             | 是否可以修改 0-否 1-是                    | String     |      |
| productRepurchaseFlag | 产品复购配置标识 0-否; 1-是               | String     |      |

- 代码逻辑

  - SimuBalanceIndexVo  balanceVo=new SimuBalanceIndexVo ();

  - 使用com.howbuy.cgi.trade.simu.service.relatedaccount.AccCenterService#getAcctDataAuthInfo查询授权状态,acctDataAuthInfo

  - 查询持仓接口

    - 构建持仓查询入参,参考com.howbuy.cgi.trade.simu.controller.index.IndexSimuController#buildQueryBalanceParam(com.howbuy.cgi.trade.simu.model.dto.IndexDTO, java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.lang.String),注意,本接口,只传参,String txAcctNo, String hboneNo,acctDataAuthInfo,fundCode
    - 查询产品的持仓,使用接口com.howbuy.cgi.trade.simu.service.QueryBalanceVolService#queryNewAcctBalance,获取返回持仓数据balanceResponse
    - 如果balanceResponse.balanceList为空,直接返回balanceVo

  - 查询是否私募定投,参考com.howbuy.cgi.trade.simu.controller.index.IndexSimuController#setHighFundInvPlanFlag,返回highFundInvPlanFlag,是否含有私募定投 0-不含 1-含

  - 查询产品,购买状态,赎回状态等信息上下文,参考这个

    ```java
    QueryBalanceDetailContext queryBalanceDetailContext = new QueryBalanceDetailContext();
            List<HowbuyBaseTask> taskList = new ArrayList<>();
            // 查询购买状态
            taskList.add(new QueryBuyStatusTask(queryBalanceDetailContext, ip, txAcctNo, productCodeList, queryBuyFundStatusFacade));
            // 查询赎回状态
            taskList.add(new RdmFundStatusTask(productCodeList, queryBalanceVolService, queryRedeemFundStatusFacade, queryBalanceDetailContext, ip, txAcctNo));
            taskList.add(new QueryCustRepurchaseProtocolTask(txAcctNo, productCodeList, queryCustRepurchaseProtocolFacade, queryBalanceDetailContext));
            // 查询产品信息
            taskList.add(new QueryProductBaseInfoTask(highProductService, productCodeList, queryBalanceDetailContext));
            // 查询产品打标信息
            taskList.add(new QueryProductTagInfoTask(highProductService, productCodeList, queryBalanceDetailContext));
            // 查询最近预约日历
            taskList.add(new QueryLatestByAppintDtTask(productCodeList, queryBalanceDetailContext, highProductService));
            // 查询产品净值状态
            taskList.add(new QueryProductStatusTask(queryBalanceDetailContext, highProductService, productCodeList));
            // 产品策略
            taskList.add(new QueryStrategyTask(getAllProductCode(balanceResponse), smZcpzService, queryBalanceDetailContext));
            // 产品分类
            taskList.add(new QueryCpflTask(productCodeList, queryBalanceDetailContext, jjxxService));
            howBuyRunTaskUil.runTask(taskList);
    ```

  - 遍历balanceResponse中balanceList,先按照productCode分组,如果出现一个productCode,分组出多条记录的,就是分期成立的,否则就是非分期成立的,这个分组会获得List<String,List<BalanceBean>> balanceMap

  - 遍历balanceMap, BalanceFundVo balanceFundVo= new BalanceFundVo(),

    - 如果是非分期成立的,直接用map的value,根据字段名,字段中文注释,com.howbuy.cgi.trade.simu.controller.index.IndexSimuController#queryBalance rtnDTO的赋值,在途,待资金到账的,同字段名赋值
    - 如果是分期成立的,那么需要先给balanceFundVo.balanceFundItemDtlList,赋值,使用map的value,value是一个list,遍历list赋值给balanceFundItemDtlList,因为部分balanceFundVo的字段是需要根据balanceFundItemDtlList赋值求和或者取最大值,具体的逻辑在字段注释上有,请仔细学习这个字段注释上的解释;
      - 在途订单balanceFundVo.balanceFundUnRefundInfo,取值list元素中balanceFundUnConfirmInfo的内容,如果是数值类型需要求和赋值给balanceFundUnRefundInfo同名字段,buyUnConfirmList,sellUnConfirmList,都需要加入balanceFundVo.balanceFundItemDtlList对应字段,注意所有字段都需要判空
      - 待资金到账信息balanceFundUnRefundInfo跟在途一样,同字段求和,或者加入List

  - SimuBalanceIndexVo除了balanceFund,其他字段依赖balanceFund的值,参考com.howbuy.cgi.trade.simu.controller.index.IndexSimuController#queryBalance rtnDTO的赋值
