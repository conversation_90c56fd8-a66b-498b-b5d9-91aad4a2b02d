# 持仓详情页接口实现文档

## 1. 概述

本文档描述了持仓详情页HTTP接口的完整实现，包括接口设计、数据结构、业务逻辑和技术实现细节。

## 2. 接口规范

### 2.1 基本信息
- **接口地址**: `/simu/user/balancedetail.htm`
- **请求方式**: POST
- **响应格式**: JSON（加密）
- **登录要求**: 是

### 2.2 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| fundCode | String | 是 | 基金代码 |
| hboneNo | String | 否 | 一账通号，默认使用登录用户 |
| custNo | String | 否 | 客户号，默认使用登录用户 |

### 2.3 响应参数
| 参数名 | 类型 | 说明 |
|--------|------|------|
| abnormalState | String | 是否存在异常，1:是,0:否 |
| isNeedAuth | String | 是否需要授权，1:是,0:否 |
| serverDate | String | 服务器日期(yyyyMMdd) |
| isDataAuth | String | 是否签署数据授权，1:已授权,0:没有授权 |
| isHkDataQuarantine | String | 是否香港数据隔离，1:是,0:否 |
| isnologinBind | String | 是否不绑定自登录，1:不绑定,0:绑定 |
| highFundInvPlanFlag | String | 是否含有私募定投，1:是,0:否 |
| showShouYi | String | 是否展示收益分析入口 |
| showLiCai | String | 是否展示理财分析入口 |
| balanceFund | Object | 持仓基金信息 |
| noTxAcctNo | String | 公募未开户，1:是,0:否 |

## 3. 数据结构设计

### 3.1 核心VO类

#### SimuBalanceIndexVo（主响应对象）
```java
public class SimuBalanceIndexVo {
    private String abnormalState;           // 异常状态
    private String isNeedAuth;              // 是否需要授权
    private String serverDate;              // 服务器日期
    private String isDataAuth;              // 数据授权状态
    private String isHkDataQuarantine;      // 香港数据隔离
    private String isnologinBind;           // 自登录绑定
    private String highFundInvPlanFlag;     // 私募定投标识
    private String showShouYi;              // 收益分析入口
    private String showLiCai;               // 理财分析入口
    private BalanceFundVo balanceFund;      // 持仓基金信息
    private String noTxAcctNo;              // 公募开户状态
}
```

#### BalanceFundVo（持仓基金信息）
```java
public class BalanceFundVo {
    private String fundCode;                    // 基金代码
    private String strategy;                    // 产品策略
    private String fundNameAbbr;                // 基金简称
    private BigDecimal currentAsset;            // 当前收益
    private String currentAssetStr;             // 当前收益展示
    private BigDecimal totalAsset;              // 总资产
    private String totalAssetStr;               // 总资产展示
    private BigDecimal yieldRate;               // 持仓收益率
    private String yieldRateStr;                // 持仓收益率展示
    private BigDecimal cashCollection;          // 回款金额
    private String cashCollectionStr;           // 回款金额展示
    private String cashCollectionRatio;         // 回款比例
    private String stageEstablishFlag;          // 是否分期成立
    private List<BalanceFundItemDtlVo> balanceFundItemDtlList; // 子产品列表
    // ... 其他字段
}
```

### 3.2 支持类

#### BalanceFundItemDtlVo（子产品详情）
用于分期成立产品的子基金信息展示。

#### BalanceFundUnConfirmVo（在途订单信息）
包含买入和卖出的在途订单详情。

#### BalanceFundUnRefundInfoVo（待资金到账信息）
包含待退款订单的详细信息。

## 4. 业务逻辑实现

### 4.1 核心流程
1. **登录态检查** - 验证用户登录状态
2. **参数构建** - 构建查询参数，补充默认值
3. **数据授权查询** - 获取用户数据授权信息
4. **持仓数据查询** - 查询用户持仓信息
5. **产品信息查询** - 并行查询产品相关信息
6. **私募定投查询** - 检查私募定投计划
7. **数据转换** - 将原始数据转换为响应格式
8. **响应输出** - 输出加密的JSON响应

### 4.2 分期成立产品处理
对于分期成立的产品（多个子基金），需要特殊处理：
- **汇总字段**：当前收益、总资产、回款金额等需要求和
- **最大值字段**：持仓收益率取最大值
- **状态字段**：异常状态只要有一个异常就是异常
- **子产品列表**：保留所有子产品的详细信息

### 4.3 并行任务处理
使用HowBuyRunTaskUil实现以下任务的并行执行：
- 产品基本信息查询
- 产品标签信息查询
- 最近预约日历查询
- 产品状态查询
- 产品策略查询
- 产品分类查询
- 客户复购协议查询

## 5. 技术实现

### 5.1 关键类说明

#### BalanceDetailService
核心业务逻辑服务，负责：
- 数据授权检查
- 持仓数据查询和处理
- 产品信息整合
- 私募定投状态查询

#### IndexSimuController
HTTP接口控制器，负责：
- 请求参数解析
- 登录态验证
- 服务调用
- 响应输出

### 5.2 依赖服务
- **AccCenterService**: 账户中心服务，获取数据授权信息
- **QueryBalanceVolService**: 持仓查询服务
- **HighProductService**: 产品信息服务
- **SmZcpzService**: 私募资产配置服务
- **JjxxService**: 基金信息服务

### 5.3 异常处理
- 登录态异常：抛出需要重新登录异常
- 服务调用异常：记录日志并返回默认值
- 数据转换异常：记录日志并继续处理

## 6. 测试覆盖

### 6.1 单元测试
- **BalanceDetailServiceTest**: 服务层测试
  - 正常流程测试
  - 空持仓测试
  - 分期成立产品测试
  - 异常情况测试

### 6.2 集成测试
- **IndexSimuControllerBalanceDetailTest**: 控制器测试
  - 成功响应测试
  - 未登录测试
  - 参数验证测试
  - 异常处理测试

## 7. 部署说明

### 7.1 文件清单
**新增文件**：
- QueryBalanceDetailParamCmd.java
- SimuBalanceIndexVo.java
- BalanceFundVo.java
- BalanceFundItemDtlVo.java
- BalanceFundUnConfirmVo.java
- BalanceFundUnRefundInfoVo.java
- BuyUnConfirmOrderVo.java
- SellUnConfirmOrderVo.java
- WaitRefundOrderDtlVo.java
- BalanceDetailService.java

**修改文件**：
- IndexSimuController.java

### 7.2 配置要求
无特殊配置要求，使用现有的Spring配置和数据源配置。

### 7.3 兼容性
- 向后兼容，不影响现有接口
- 使用现有的架构模式和工具类
- 遵循现有的编码规范

## 8. 注意事项

1. **数据授权**：接口会根据用户的数据授权状态过滤数据
2. **香港数据隔离**：支持香港数据的隔离处理
3. **分期成立产品**：正确处理分期成立产品的数据汇总
4. **异常状态**：准确计算和展示异常状态
5. **性能优化**：使用并行任务提高查询效率

## 9. 后续优化建议

1. **缓存机制**：对产品信息等相对稳定的数据增加缓存
2. **监控告警**：增加接口调用监控和异常告警
3. **性能优化**：根据实际使用情况优化查询逻辑
4. **功能扩展**：根据业务需求扩展更多字段和功能
