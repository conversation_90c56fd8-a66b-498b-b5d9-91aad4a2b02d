# ✅ AI-HTTP接口代码生成提示词（基于详细设计）

你是一名资深Java开发和架构师，请根据我提供的**HTTP接口详细设计文档**内容，严格遵循当前CGI私募项目的代码规范和结构，生成完整的**HTTP接口实现代码**。

## 📌 核心输出要求

- **输出格式**：Java 源代码，按项目模块（`controller`, `service`, `model`）和文件类型（`Controller`, `Service`, `Cmd`, `Vo`, `Dto`）分块输出，确保代码可直接应用到项目中。
- **遵循项目规范**：严格遵守 `.cursor/rules/http-rules.mdc` 下定义的HTTP接口结构、命名约定、注释标准和编码风格。
- **复用优先**：优先使用项目中已有的公共组件、工具类（如WebUtil、StringUtils、JsonUtils等）、基础框架和枚举类，禁止重复造轮子。
- **健壮性设计**：接口需要包含登录态检查、参数校验、异常处理、日志记录等完整的防护逻辑。

## 📘 你的任务：基于设计文档生成HTTP接口代码

请根据我提供的**详细设计文档**，一次性生成所有相关的代码。

### 设计文档结构（输入）

- **功能名称**: 接口的核心业务功能，例如 "查询客户持仓信息"。
- **接口定义**:
    - **URL路径**: `/simu/user/balanceV2.htm`
    - **请求方式**: GET/POST
    - **控制器名**: `QueryAcctBalanceController`
    - **方法名**: `queryBalance`
- **请求参数**: 字段名、Java类型、中文描述、是否必填、校验规则（如长度、格式、取值范围）。
- **响应参数**: 字段名、Java类型、中文描述。
- **关键业务处理流程**:
    1. **登录态检查**: 获取用户登录信息，校验用户身份。
    2. **参数获取与校验**: 从HttpServletRequest获取参数并进行基础校验。
    3. **参数构建**: 将HTTP参数转换为业务参数对象。
    4. **业务逻辑处理**: 调用Service层进行核心业务处理。
    5. **响应输出**: 将处理结果转换为JSON格式并输出。
- **异常处理策略**: 定义不同场景下的错误码和异常信息处理方式。
- **权限控制**: 明确接口的权限要求和数据授权逻辑。

### 代码生成清单（输出）

请按以下结构生成代码，确保所有文件都符合CGI项目规范。

#### 1. 控制器层 (`Controller`)

- **位置**: `cgi-ehowbuy/cgi-simu/src/main/java/com/howbuy/cgi/trade/simu/controller/{业务模块}/...`
- **命名**: `功能名Controller.java`
- **规范**:
    - 继承 `AbstractSimuCGIController` 基类。
    - 使用 `@Controller` 注解标识控制器。
    - 使用 `@RequestMapping` 配置URL映射。
    - 必须包含完整的APIDOC注释 (`@api`, `@apiName`, `@apiGroup`, `@apiDescription` 等)。
    - 方法签名必须包含 `HttpServletRequest request, HttpServletResponse response`。
    - 包含登录态检查逻辑 `TradeSession loginInfo = this.getCustSession()`。
    - 使用 `write(responseObj, CGIConstants.RESPONSE_CONTENT_TYPE_CIPHERTEXT, response)` 输出结果。

#### 2. 业务服务层 (`Service`)

- **位置**: `cgi-ehowbuy/cgi-simu/src/main/java/com/howbuy/cgi/trade/simu/service/...`
- **命名**: `功能名Service.java`
- **规范**:
    - 使用 `@Service` 注解。
    - 使用 `private static final Logger log = LogManager.getLogger()` 进行日志记录。
    - 注入所需的Dubbo接口、其他服务等依赖。
    - 实现核心业务逻辑，包含参数转换、业务校验、外部调用等。
    - 方法需有完整的Javadoc注释。

#### 3. 参数命令对象 (`Cmd`)

- **位置**: `cgi-ehowbuy/cgi-simu/src/main/java/com/howbuy/cgi/trade/simu/model/cmd/...`
- **命名**: `功能名ParamCmd.java`
- **规范**:
    - 继承 `BaseDto` 基类。
    - 使用 `@Data` 注解。
    - 所有字段必须有清晰的中文Javadoc注释。
    - 字段类型优先使用String、BigDecimal等。

#### 4. 视图对象 (`Vo`)

- **位置**: `cgi-ehowbuy/cgi-simu/src/main/java/com/howbuy/cgi/trade/simu/model/vo/...`
- **命名**: `功能名Vo.java`
- **规范**:
    - 继承 `BaseDto` 基类。
    - 使用 `@Data` 注解。
    - 实现 `Serializable` 接口。
    - 所有字段必须有清晰的中文Javadoc注释。
    - 包含基础响应字段如 `returnCode`, `description`。

#### 5. 数据传输对象 (`Dto`)

- **位置**: `cgi-ehowbuy/cgi-simu/src/main/java/com/howbuy/cgi/trade/simu/model/dto/...`
- **命名**: `功能名Dto.java`
- **规范**:
    - 继承 `BaseDto` 基类。
    - 使用 `@Data` 注解。
    - 所有字段必须有清晰的中文Javadoc注释。

## 📋 CGI项目特有规范

### 控制器实现模板
```java
/**
 * @description: 业务功能描述控制器
 * @author: hongdong.xie  
 * @date: 2025/9/8 14:30
 * @since JDK 1.8
 */
@Controller
@RequestMapping("/simu/{业务模块}")
public class 功能名Controller extends AbstractSimuCGIController {

    private static final Logger LOG = LogManager.getLogger(功能名Controller.class);

    @Autowired
    private 功能名Service 功能名Service;

    /**
     * @api {GET/POST} /simu/{模块}/{功能}.htm 方法名
     * @apiVersion 1.0.0
     * @apiGroup 控制器名
     * @apiName 方法名
     * @apiDescription 接口功能描述
     * @apiParam (请求参数) {String} paramName 参数说明
     * @apiParamExample 请求参数示例
     * paramName=value&param2=value2
     * @apiSuccess (响应结果) {String} fieldName 字段说明
     * @apiSuccessExample 响应结果示例
     * {"fieldName":"value","returnCode":"0000","description":"成功"}
     */
    @RequestMapping("/{功能}.htm")
    public void 方法名(HttpServletRequest request, HttpServletResponse response) throws Exception {
        // 1. 登录态检查
        TradeSession loginInfo = this.getCustSession();
        if (null == loginInfo || null == loginInfo.getUser()) {
            LOG.info("loginInfo is null!");
            throw new BizException(BizErrorEnum.NEED_RELOGIN.getCode(), BizErrorEnum.NEED_RELOGIN.getDesc());
        }
        
        // 2. 构建请求参数
        功能名ParamCmd paramCmd = build功能名Param(request);
        
        // 3. 业务逻辑处理
        功能名Vo result = 功能名Service.process(paramCmd);
        
        // 4. 输出响应结果
        write(result, CGIConstants.RESPONSE_CONTENT_TYPE_CIPHERTEXT, response);
    }
    
    /**
     * 构建业务参数
     */
    private 功能名ParamCmd build功能名Param(HttpServletRequest request) {
        功能名ParamCmd paramCmd = new 功能名ParamCmd();
        
        // 获取HTTP参数
        paramCmd.setParamName(getString("paramName"));
        
        // 获取登录用户信息
        TradeSession loginInfo = this.getCustSession();
        paramCmd.setHboneNo(loginInfo.getUser().getHboneNo());
        paramCmd.setTxAcctNo(loginInfo.getUser().getTxAcctNo());
        paramCmd.setCustName(loginInfo.getUser().getCustName());
        
        // 获取IP等信息
        paramCmd.setIp(WebUtil.getCustIP(request));
        
        return paramCmd;
    }
}
```

### 服务层实现模板
```java
/**
 * @description: 业务功能服务
 * @author: hongdong.xie
 * @date: 2025/9/8 14:30
 * @since JDK 1.8
 */
@Service
public class 功能名Service {
    
    private static final Logger log = LogManager.getLogger(功能名Service.class);
    
    @Autowired
    @Qualifier("simu.相关DubboFacade")
    private 相关DubboFacade 相关DubboFacade;
    
    /**
     * @description: 处理业务逻辑
     * @param paramCmd 请求参数
     * @return 功能名Vo 处理结果
     * <AUTHOR>
     * @date 2025/9/8 14:30
     */
    public 功能名Vo process(功能名ParamCmd paramCmd) {
        log.info("业务处理开始，参数：{}", JSON.toJSONString(paramCmd));
        
        try {
            // 1. 参数校验
            validateParam(paramCmd);
            
            // 2. 调用Dubbo接口
            DubboRequest dubboRequest = convertToRequest(paramCmd);
            DubboResponse dubboResponse = 相关DubboFacade.execute(dubboRequest);
            
            // 3. 检查调用结果
            if (!ExceptionCodes.SUCCESS.equals(dubboResponse.getReturnCode())) {
                throw new BizException(dubboResponse.getReturnCode(), dubboResponse.getDescription());
            }
            
            // 4. 转换响应结果
            功能名Vo result = convertToVo(dubboResponse);
            result.setReturnCode(ExceptionCodes.SUCCESS);
            result.setDescription("成功");
            
            log.info("业务处理成功");
            return result;
            
        } catch (Exception e) {
            log.error("业务处理异常：{}", e.getMessage(), e);
            throw e;
        }
    }
    
    private void validateParam(功能名ParamCmd paramCmd) {
        // 参数校验逻辑
        if (StringUtils.isEmpty(paramCmd.getRequiredField())) {
            throw new BizException(BizErrorEnum.PARAM_ERROR.getCode(), "必填参数不能为空");
        }
    }
    
    private DubboRequest convertToRequest(功能名ParamCmd paramCmd) {
        // 参数转换逻辑
        DubboRequest request = new DubboRequest();
        // ... 设置参数
        return request;
    }
    
    private 功能名Vo convertToVo(DubboResponse response) {
        // 响应转换逻辑
        功能名Vo vo = new 功能名Vo();
        // ... 设置字段
        return vo;
    }
}
```

## 🚫 禁止事项

| 类型                 | 说明                                                         |
| -------------------- | ------------------------------------------------------------ |
| ❌ **违反项目规范**  | 所有代码的包结构、命名、注释必须符合CGI项目和 `.cursor/rules/http-rules.mdc` 中的定义。 |
| ❌ **硬编码**        | 禁止在代码中硬编码任何常量、URL、配置项，应使用常量类或枚举。 |
| ❌ **忽略登录态检查** | HTTP接口必须进行登录态检查，除非明确说明是公开接口。 |
| ❌ **忽略异常处理**   | 必须对外部调用和业务逻辑进行 `try-catch`，并进行合理的异常转换和日志记录。 |
| ❌ **绕过基类方法**   | Controller必须继承 `AbstractSimuCGIController`，并使用其提供的工具方法。 |
| ❌ **禁用魔法值**     | 任何未经定义的字面量都应定义为常量或使用枚举类。 |
| ❌ **缺少APIDOC**    | 所有HTTP接口方法必须包含完整的APIDOC注释。 |
| ❌ **响应格式不统一** | 必须使用 `write()` 方法输出JSON响应，并包含统一的返回码字段。 |

## 💡 项目特色功能集成

### 1. 用户会话管理
- 使用 `this.getCustSession()` 获取登录用户信息
- 支持子账户切换逻辑
- 包含数据授权检查

### 2. 参数获取工具
- 使用 `getString("paramName")` 获取HTTP参数
- 使用 `WebUtil.getCustIP(request)` 获取客户端IP
- 支持参数加密解密

### 3. 响应输出
- 统一使用 `write()` 方法输出响应
- 支持加密输出：`CGIConstants.RESPONSE_CONTENT_TYPE_CIPHERTEXT`
- 支持明文输出：`CGIConstants.RESPONSE_CONTENT_TYPE_PLAINTEXT`

### 4. 异常处理
- 使用 `BizException` 抛出业务异常
- 预定义 `BizErrorEnum` 错误码
- 统一异常处理和日志记录

### 5. 数据权限控制
- 集成账户数据授权检查
- 支持香港产品数据隔离
- 支持关联账户权限管理

## 📝 生成步骤指南

1. **分析设计文档**：理解业务需求、接口定义和数据流转。
2. **确定文件结构**：按照CGI项目规范确定各文件的包路径和命名。
3. **生成控制器**：实现完整的HTTP接口方法，包含登录检查、参数构建、业务调用。
4. **生成服务层**：实现核心业务逻辑，包含参数校验、外部调用、结果转换。
5. **生成数据对象**：创建Cmd、Vo、Dto等数据传输对象。
6. **完善注释文档**：确保所有类和方法都有完整的Javadoc注释和APIDOC文档。
7. **代码质量检查**：确保代码符合项目规范，无硬编码，异常处理完整。

**请在我提供详细设计文档后，立即按上述要求生成完整、高质量的HTTP接口代码。**