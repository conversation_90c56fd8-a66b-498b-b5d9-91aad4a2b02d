package com.howbuy.cgi.trade.simu.model.dto;

import com.howbuy.interlayer.product.model.HighProductAppointmentInfoModel;
import com.howbuy.interlayer.product.model.HighProductBaseInfoModel;
import com.howbuy.interlayer.product.model.HighProductStatInfoModel;
import com.howbuy.interlayer.product.model.HighProductTagInfoModel;
import com.howbuy.simu.dto.business.product.SimuZcpzJjInfo;
import com.howbuy.tms.high.orders.facade.search.querybuyfundstatus.QueryBuyFundStatusResponse;
import com.howbuy.tms.high.orders.facade.search.queryredeemfundstatus.QueryRedeemFundStatusResponse;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * @Description:查询持仓上下文
 * @Author: yun.lu
 * Date: 2025/8/4 10:37
 */
@Data
public class QueryBalanceContext {
    /**
     * 查询赎回状态
     */
    private Map<String, QueryRedeemFundStatusResponse.RedeemFundStatusBean> redeemFundStatusMap;

    /**
     * 购买协议
     */
    private Map<String, CustRepurchaseProtocolDto> custRepurchaseProtocolMap;

    /**
     * 产品基础信息
     */
    private Map<String, HighProductBaseInfoModel> productInfoMap;

    /**
     * 产品打标信息
     */
    private  Map<String, HighProductTagInfoModel> productTagInfoMap;

    /**
     * 查询最近预约日历
     */
    private Map<String, List<HighProductAppointmentInfoModel>> productAppointMap;

    /**
     * 产品策略
     */
    private  Map<String, SimuZcpzJjInfo> strategyMap;

    /**
     * 产品基金状态
     */
    private  Map<String, HighProductStatInfoModel> productStatMap;

    /**
     * 产品分类
     */
    private Map<String, String> cpflMap;
    /**
     * 产品购买状态
     */
    private  Map<String, QueryBuyFundStatusResponse.BuyFundStatusBean> buyFundStatusMap;





}
