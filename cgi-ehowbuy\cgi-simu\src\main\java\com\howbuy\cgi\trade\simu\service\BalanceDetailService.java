package com.howbuy.cgi.trade.simu.service;

import com.alibaba.fastjson.JSON;
import com.howbuy.cgi.common.enums.BizErrorEnum;
import com.howbuy.cgi.common.exception.BizException;
import com.howbuy.cgi.trade.simu.common.enums.CurrencyEnum;
import com.howbuy.cgi.trade.simu.common.enums.ProductStrategyTypeNewEnum;
import com.howbuy.cgi.trade.simu.model.cmd.QueryBalanceDetailParamCmd;
import com.howbuy.cgi.trade.simu.model.cmd.QueryBalanceParamCmd;
import com.howbuy.cgi.trade.simu.model.dto.AcctDataAuthDto;
import com.howbuy.cgi.trade.simu.model.dto.CustRepurchaseProtocolDto;
import com.howbuy.cgi.trade.simu.model.dto.QueryBalanceDetailContext;
import com.howbuy.cgi.trade.simu.model.dto.QueryBalanceDetailOtherInfoVo;
import com.howbuy.cgi.trade.simu.model.vo.*;
import com.howbuy.cgi.trade.simu.service.relatedaccount.AccCenterService;
import com.howbuy.cgi.trade.simu.service.task.*;
import com.howbuy.cgi.trade.simu.util.BusiUtil;
import com.howbuy.cgi.trade.simu.util.MoneyUtil;
import com.howbuy.fund.service.base.product.JjxxService;
import com.howbuy.interlayer.common.enums.FundCanBuyEnum;
import com.howbuy.interlayer.product.model.*;
import com.howbuy.interlayer.product.service.HighProductService;
import com.howbuy.interlayer.product.service.TradeDayService;
import com.howbuy.simu.dto.business.product.SimuZcpzJjInfo;
import com.howbuy.simu.service.base.product.SmZcpzService;
import com.howbuy.tms.common.enums.busi.*;
import com.howbuy.tms.common.utils.DateUtils;
import com.howbuy.tms.high.orders.facade.search.queryacctbalance.*;
import com.howbuy.tms.high.orders.facade.search.queryacctbalance.QueryAcctBalanceResponse.BalanceBean;
import com.howbuy.tms.high.orders.facade.search.querybuyfundstatus.QueryBuyFundStatusFacade;
import com.howbuy.tms.high.orders.facade.search.querybuyfundstatus.QueryBuyFundStatusResponse;
import com.howbuy.tms.high.orders.facade.search.queryhighfundinvplanlist.QueryHighFundInvPlanListFacade;
import com.howbuy.tms.high.orders.facade.search.queryredeemfundstatus.QueryRedeemFundStatusFacade;
import com.howbuy.tms.high.orders.facade.search.queryredeemfundstatus.QueryRedeemFundStatusResponse;
import com.howbuy.tms.high.orders.facade.search.queryrepurchaseprotocol.QueryCustRepurchaseProtocolFacade;
import com.howbuy.tms.orders.search.facade.enums.RedeemDirectionEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @description: 持仓详情页服务
 * @author: hongdong.xie
 * @date: 2025/9/9 14:30
 * @since JDK 1.8
 */
@Service
@Slf4j
public class BalanceDetailService {

    private static final Logger logger = LogManager.getLogger(BalanceDetailService.class);

    @Autowired
    private AccCenterService accCenterService;

    @Autowired
    private QueryBalanceVolService queryBalanceVolService;

    @Autowired
    @Qualifier("simu.queryBuyFundStatusFacade")
    private QueryBuyFundStatusFacade queryBuyFundStatusFacade;

    @Autowired
    @Qualifier("simu.queryRedeemFundStatusFacade")
    private QueryRedeemFundStatusFacade queryRedeemFundStatusFacade;

    @Autowired
    @Qualifier("simu.queryCustRepurchaseProtocolFacade")
    private QueryCustRepurchaseProtocolFacade queryCustRepurchaseProtocolFacade;

    @Autowired
    @Qualifier("simu.highProductService")
    private HighProductService highProductService;

    @Autowired
    @Qualifier("simu.smZcpzService")
    private SmZcpzService smZcpzService;

    @Autowired
    @Qualifier("simu.jjxxService")
    private JjxxService jjxxService;

    @Autowired
    @Qualifier("simu.queryHighFundInvPlanListFacade")
    private QueryHighFundInvPlanListFacade queryHighFundInvPlanListFacade;

    @Autowired
    private HowBuyRunTaskUil howBuyRunTaskUil;
    @Autowired
    private QueryAcctOtherInfoService queryAcctOtherInfoService;
    @Autowired
    @Qualifier("simu.tradeDayService")
    private TradeDayService tradeDayService;
    // 默认下单截止时间
    private static final String DEFAULT_TIME = "150000";

    /**
     * @param paramCmd 请求参数
     * @return SimuBalanceIndexVo 持仓详情信息
     * @description: 查询持仓详情信息
     * <AUTHOR>
     * @date 2025/9/9 14:30
     */
    public SimuBalanceIndexVo queryBalanceDetail(QueryBalanceDetailParamCmd paramCmd) {
        logger.info("查询持仓详情开始，参数：{}", JSON.toJSONString(paramCmd));
        try {
            // 1. 初始化返回对象
            SimuBalanceIndexVo balanceVo = new SimuBalanceIndexVo();
            // 2. 查询授权状态
            AcctDataAuthDto acctDataAuthInfo = accCenterService.getAcctDataAuthInfo(paramCmd.getHboneNo());
            paramCmd.setAcctDataAuthInfo(acctDataAuthInfo);
            // 3. 构建持仓查询参数
            QueryBalanceParamCmd queryBalanceParamCmd = buildQueryBalanceParam(paramCmd, acctDataAuthInfo);
            // 4. 查询产品的持仓
            QueryAcctBalanceResponse balanceResponse = queryBalanceVolService.queryNewAcctBalance(queryBalanceParamCmd);
            // 5. 如果持仓为空，直接返回
            if (balanceResponse == null || CollectionUtils.isEmpty(balanceResponse.getBalanceList())) {
                logger.info("持仓信息为空，直接返回");
                setDataAuthInfo(balanceVo, acctDataAuthInfo);
                return balanceVo;
            }
            // 6. 查询产品相关信息上下文
            QueryBalanceDetailContext queryBalanceDetailContext = queryProductContext(balanceResponse, paramCmd);
            // 7. 处理持仓数据
            BalanceFundVo balanceFundVo = processBalanceDetailData(balanceResponse, queryBalanceDetailContext);
            // 8. 设置返回信息
            setBalanceVoInfo(balanceVo, balanceFundVo, acctDataAuthInfo, balanceResponse, queryBalanceDetailContext);
            logger.info("查询持仓详情成功");
            return balanceVo;
        } catch (Exception e) {
            logger.error("查询持仓详情异常：{}", e.getMessage(), e);
            throw new BizException(BizErrorEnum.SYSTEM_ERROR.getCode(), "查询持仓详情失败");
        }
    }

    /**
     * 处理产品购买/赎回状态
     */
    private void processBuyAndRdmStatus(BalanceFundVo balanceFundVo, Map<String, QueryBuyFundStatusResponse.BuyFundStatusBean> buyFundStatusMap,
                                        Map<String, QueryRedeemFundStatusResponse.RedeemFundStatusBean> rdmFundStatusMap) {
        // 产品购买状态
        balanceFundVo.setCanBuy(FundCanBuyEnum.CAN_SUB.getCode());
        // 产品赎回状态
        balanceFundVo.setCanRedeem(CanRedeemFlagEnum.YES.getCode());

        QueryBuyFundStatusResponse.BuyFundStatusBean buyFundStatusBean = buyFundStatusMap.get(balanceFundVo.getFundCode());
        if (buyFundStatusBean != null) {
            balanceFundVo.setCanBuy(buyFundStatusBean.getBuyStatus());
            balanceFundVo.setInDirectBlackList(YesOrNoEnum.NO.getCode());
            balanceFundVo.setFundBuyStatus(buyFundStatusBean.getFundBuyStatus());
            if (FundCanBuyEnum.CAN_SUB.getCode().equals(buyFundStatusBean.getBuyStatus()) && BuyStatusTypeEnum.IN_BLANKLIST.getCode().equals(buyFundStatusBean.getBuyStatusType())) {
                balanceFundVo.setInDirectBlackList(YesOrNoEnum.YES.getCode());
            }
        }

        QueryRedeemFundStatusResponse.RedeemFundStatusBean redeemFundStatusBean = rdmFundStatusMap.get(balanceFundVo.getFundCode());
        if (redeemFundStatusBean != null) {
            balanceFundVo.setCanRedeem(redeemFundStatusBean.getRedeemStatus());
            // 可赎回份额
            balanceFundVo.setAvailVol(redeemFundStatusBean.getAvailVol());
            // 锁定份额
            balanceFundVo.setLockVol(redeemFundStatusBean.getLockVol());
        }
    }

    /**
     * 构建持仓查询参数
     */
    private QueryBalanceParamCmd buildQueryBalanceParam(QueryBalanceDetailParamCmd paramCmd, AcctDataAuthDto acctDataAuthInfo) {
        QueryBalanceParamCmd queryBalanceParamCmd = new QueryBalanceParamCmd();
        queryBalanceParamCmd.setTxAcctNo(paramCmd.getTxAcctNo());
        queryBalanceParamCmd.setHboneNo(paramCmd.getHboneNo());
        queryBalanceParamCmd.setProductCode(paramCmd.getFundCode());
        queryBalanceParamCmd.setIp(paramCmd.getIp());
        queryBalanceParamCmd.setAcctDataAuthInfo(acctDataAuthInfo);

        // 设置数据授权相关参数
        if (YesOrNoEnum.YES.getCode().equals(acctDataAuthInfo.getIsDataAuth())) {
            queryBalanceParamCmd.setNotFilterHkFund(YesOrNoEnum.YES.getCode());
            queryBalanceParamCmd.setNotFilterHzFund(YesOrNoEnum.YES.getCode());
            // 如果香港数据隔离,就过滤香港的产品
            if (YesOrNoEnum.YES.getCode().equals(acctDataAuthInfo.getIsHkDataQuarantine())) {
                queryBalanceParamCmd.setNotFilterHkFund(YesOrNoEnum.NO.getCode());
            }
        } else {
            queryBalanceParamCmd.setNotFilterHkFund(YesOrNoEnum.NO.getCode());
            queryBalanceParamCmd.setNotFilterHzFund(YesOrNoEnum.NO.getCode());
        }

        return queryBalanceParamCmd;
    }

    /**
     * 设置权限信息
     */
    private void setDataAuthInfo(SimuBalanceIndexVo balanceVo, AcctDataAuthDto acctDataAuthInfo) {
        balanceVo.setIsDataAuth(acctDataAuthInfo.getIsDataAuth());
        balanceVo.setIsHkDataQuarantine(acctDataAuthInfo.getIsHkDataQuarantine());
        balanceVo.setServerDate(getCurrentDate());
    }

    /**
     * 获取当前日期
     */
    private String getCurrentDate() {
        return java.time.LocalDate.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyyMMdd"));
    }

    /**
     * 查询是否私募定投
     */
    private String queryHighFundInvPlanFlag(QueryBalanceDetailParamCmd paramCmd) {
        try {
            QueryHighFundInvPlanFlagTask task = new QueryHighFundInvPlanFlagTask();
            task.setTxAcctNo(paramCmd.getTxAcctNo());
            task.setProductCode(paramCmd.getFundCode());
            task.setIp(paramCmd.getIp());
            task.setQueryHighFundInvPlanListFacade(queryHighFundInvPlanListFacade);

            SiMuIndexVo siMuIndexVo = new SiMuIndexVo();
            task.setSiMuIndexVo(siMuIndexVo);

            // 使用工具类执行任务
            howBuyRunTaskUil.runTask(task);

            return siMuIndexVo.getHighFundInvPlanFlag() != null ?
                    siMuIndexVo.getHighFundInvPlanFlag() : YesOrNoEnum.NO.getCode();
        } catch (Exception e) {
            logger.error("查询私募定投标识异常：{}", e.getMessage(), e);
            return YesOrNoEnum.NO.getCode();
        }
    }

    /**
     * 查询产品相关信息上下文
     */
    private QueryBalanceDetailContext queryProductContext(QueryAcctBalanceResponse balanceResponse, QueryBalanceDetailParamCmd paramCmd) {
        QueryBalanceDetailContext queryBalanceDetailContext = new QueryBalanceDetailContext();
        BalanceBean balanceBean = balanceResponse.getBalanceList().get(0);
        String productCode = balanceBean.getProductCode();
        List<HowbuyBaseTask> taskList = new ArrayList<>();
        if (!ScaleTypeEnum.DIRECT.getCode().equals(balanceBean.getScaleType())) {
            // 查询购买状态
            taskList.add(new QueryBuyStatusTask(queryBalanceDetailContext, paramCmd.getIp(), paramCmd.getCustNo(), Collections.singletonList(productCode), queryBuyFundStatusFacade));
            // 查询赎回状态
            taskList.add(new RdmFundStatusTask(Collections.singletonList(productCode), queryBalanceVolService, queryRedeemFundStatusFacade, queryBalanceDetailContext, paramCmd.getIp(), paramCmd.getCustNo()));
            taskList.add(new QueryCustRepurchaseProtocolTask(paramCmd.getCustNo(), Collections.singletonList(productCode), queryCustRepurchaseProtocolFacade, queryBalanceDetailContext));
            // 查询最近预约日历
            taskList.add(new QueryLatestByAppintDtTask(Collections.singletonList(productCode), queryBalanceDetailContext, highProductService));
            // 产品分类
            taskList.add(new QueryCpflTask(Collections.singletonList(productCode), queryBalanceDetailContext, jjxxService));
        }
        // 查询产品信息
        taskList.add(new QueryProductBaseInfoTask(highProductService, Collections.singletonList(productCode), queryBalanceDetailContext));
        // 查询产品打标信息
        taskList.add(new QueryProductTagInfoTask(highProductService, Collections.singletonList(productCode), queryBalanceDetailContext));
        // 查询产品净值状态
        taskList.add(new QueryProductStatusTask(queryBalanceDetailContext, highProductService, Collections.singletonList(productCode)));
        // 产品策略
        taskList.add(new QueryStrategyTask(getAllProductCode(Collections.singletonList(productCode)), smZcpzService, queryBalanceDetailContext));
        // 是否私募定投
        taskList.add(new QueryHighFundInvPlanFlagWithContextTask(queryBalanceDetailContext, queryHighFundInvPlanListFacade, paramCmd.getCustNo(), paramCmd.getIp(), productCode));

        // 查询持仓补充信息
        QueryOtherBalanceInfoTask queryOtherBalanceInfoTask = new QueryOtherBalanceInfoTask();
        queryOtherBalanceInfoTask.setQueryBalanceDetailContext(queryBalanceDetailContext);
        queryOtherBalanceInfoTask.setIp(paramCmd.getIp());
        queryOtherBalanceInfoTask.setQueryAcctOtherInfoService(queryAcctOtherInfoService);
        queryOtherBalanceInfoTask.setDisCode(balanceBean.getDisCode());
        queryOtherBalanceInfoTask.setHbOneNo(paramCmd.getHboneNo());
        List<String> fundSubCodeList = balanceResponse.getBalanceList().stream().map(BalanceBean::getSubProductCode).collect(Collectors.toList());
        queryOtherBalanceInfoTask.setSubFundCodeList(fundSubCodeList);
        taskList.add(queryOtherBalanceInfoTask);
        howBuyRunTaskUil.runTask(taskList);
        return queryBalanceDetailContext;
    }

    /**
     * 获取所有产品代码（包括子产品）
     */
    private List<String> getAllProductCode(List<String> productCodeList) {
        // 这里可以根据需要扩展，获取包括子产品在内的所有产品代码
        return productCodeList;
    }

    /**
     * 处理持仓数据
     */
    private BalanceFundVo processBalanceDetailData(QueryAcctBalanceResponse balanceResponse, QueryBalanceDetailContext context) {
        List<BalanceBean> balanceList = balanceResponse.getBalanceList();
        BalanceFundVo balanceFundVo = new BalanceFundVo();
        if (CollectionUtils.isEmpty(balanceList)) {
            return balanceFundVo;
        }
        BalanceBean balanceBean = balanceList.get(0);
        String productCode = balanceBean.getProductCode();
        // 部分信息是否母子结构无关的
        buildBalanceFundBaseInfo(context, balanceFundVo, balanceBean, productCode);
        // 判断是否分期成立
        if (balanceList.size() > 1) {
            // 母子结构
            processStageEstablishFund(balanceList, balanceFundVo);
        } else {
            // 非分期成立的产品
            processNormalFund(balanceBean, balanceFundVo);
        }
        return balanceFundVo;
    }

    /**
     * 持仓产品基础信息
     */
    private void buildBalanceFundBaseInfo(QueryBalanceDetailContext context, BalanceFundVo balanceFundVo, BalanceBean balanceBean, String productCode) {
        // 设置分期成立标识
        balanceFundVo.setStageEstablishFlag(balanceBean.getStageEstablishFlag());
        // 基金代码/母基金代码
        balanceFundVo.setFundCode(productCode);
        // 直销还是代销
        balanceFundVo.setScaleType(balanceBean.getScaleType());
        // 产品存续期限(类似于5+3+2这种说明)
        balanceFundVo.setFundCxqXStr(balanceBean.getFundCXQXStr());
        // 币种
        balanceFundVo.setCurrency(balanceBean.getCurrency());
        // 币种展示字段
        balanceFundVo.setCurrencyStr(CurrencyEnum.getNameByCode(balanceBean.getCurrency()));
        // 是否海外产品,1:是,0:不是
        balanceFundVo.setHkSaleFlag(balanceBean.getHkSaleFlag());
        // NA产品收费类型 10201-好买收费 0-管理人收费
        balanceFundVo.setNaProductFeeType(balanceBean.getNaProductFeeType());
        // 产品类型,7-一对多专户,11-私募
        balanceFundVo.setProductType(balanceBean.getProductType());
        // 产品二级类型:0-现金管理,1-债券,2-类固定收益,3-对冲策略,4-股票策略,5-股权,6-房地产基金,7-其他
        balanceFundVo.setProductSubType(balanceBean.getProductSubType());
        // 是否分次call
        balanceFundVo.setFractionateCallFlag(balanceBean.getFractionateCallFlag());
        // 标准固收标识(固收类有此标识:0-非标准固收，1-标准固收，2-现金管理， 3-券商集合理财 4-纯债产品)
        balanceFundVo.setStandardFixedIncomeFlag(balanceBean.getStandardFixedIncomeFlag());
        // 清算标识 0-否; 1-是
        balanceFundVo.setCrisisFlag(balanceBean.getCrisisFlag());
        // 业绩比较基准类型（固收类产品有此标识）<br> 0-业绩比较基准（年化） 1-业绩报酬计提基准（年化）
        balanceFundVo.setBenchmarkType(balanceBean.getBenchmarkType());
        // 业绩比较基准
        balanceFundVo.setBenchmark(balanceBean.getBenchmark());
        // 千禧年待投产品标识 0-否; 1-是
        balanceFundVo.setQianXiFlag(balanceBean.getQianXiFlag());
        // 产品期限说明
        balanceFundVo.setCpqxsm(balanceBean.getCpqxsm());
        // 净值披露方式(1-净值,2-份额收益)
        balanceFundVo.setNavDisclosureType(balanceBean.getNavDisclosureType());
        // 设置产品基本信息
        if (context.getProductInfoMap() != null && context.getProductInfoMap().containsKey(productCode)) {
            HighProductBaseInfoModel productInfo = context.getProductInfoMap().get(productCode);
            balanceFundVo.setFundNameAbbr(productInfo.getFundName());
        }
        // 设置产品策略
        if (context.getStrategyMap() != null && context.getStrategyMap().containsKey(productCode)) {
            SimuZcpzJjInfo strategyInfo = context.getStrategyMap().get(productCode);
            balanceFundVo.setStrategy(strategyInfo.getYjclfl());
            balanceFundVo.setStrategyStr(ProductStrategyTypeNewEnum.getDescByType(strategyInfo.getYjclfl()));
        }
        // 设置客户复购协议
        setRePurchaseProtocolInfo(context, balanceFundVo, productCode);
        // 设置待资金到账与待确认信息
        setRefundAndUnConfirm(Collections.singletonList(balanceBean), balanceFundVo);
        // 设置持仓补充
        setOtherBalanceInfo(balanceFundVo, context.getQueryBalanceDetailOtherInfoVo());
        // 是否高毅领山产品 0-否 1-是
        processTagInfo(balanceFundVo, context.getProductTagInfoMap());
        // 处理预约交易日历信息
        setAppointInfo(balanceFundVo, context);
        // 设置购买,赎回状态
        processBuyAndRdmStatus(balanceFundVo, context.getBuyFundStatusMap(), context.getRedeemFundStatusMap());
    }

    /**
     * 设置客户复购协议
     */
    private static void setRePurchaseProtocolInfo(QueryBalanceDetailContext context, BalanceFundVo balanceFundVo, String productCode) {
        if (context.getCustRepurchaseProtocolMap() != null && context.getCustRepurchaseProtocolMap().containsKey(productCode)) {
            CustRepurchaseProtocolDto protocolDto = context.getCustRepurchaseProtocolMap().get(productCode);
            balanceFundVo.setCanModifyRepurchaseProtocolFlag(protocolDto.getCanModify());
            balanceFundVo.setCustRepurchaseProtocol(protocolDto);
            balanceFundVo.setProductRepurchaseFlag(protocolDto.getProductRepurchaseFlag());
        }
    }

    private void processTagInfo(BalanceFundVo balanceFundVo, Map<String, HighProductTagInfoModel> tagInfoMap) {
        HighProductTagInfoModel model = tagInfoMap.get(balanceFundVo.getFundCode());
        if (model != null && org.apache.commons.lang3.StringUtils.isNotBlank(model.getGaoYiLingShanFlag())) {
            balanceFundVo.setGaoYiLingShanFlag(model.getGaoYiLingShanFlag());
        } else {
            balanceFundVo.setGaoYiLingShanFlag(YesOrNoEnum.NO.getCode());
        }
    }

    /**
     * 处理预约交易日历信息
     */
    private void setAppointInfo(BalanceFundVo balanceFundVo, QueryBalanceDetailContext context) {
        try {
            WorkDayModel workDayModel = tradeDayService.getWorkDayModel(new Date());
            processBuyAppointInfoDay(balanceFundVo, context.getProductAppointMap(), context.getProductInfoMap(), context.getProductStatMap(), workDayModel);
            processSellAppointInfoDay(balanceFundVo, context.getProductAppointMap(), context.getProductInfoMap(), context.getProductStatMap(), workDayModel);
        } catch (Exception e) {
            log.error("setAppointInfo error:{}", e.getMessage(), e);
        }

    }

    /**
     * 处理买入预约日历信息
     */
    private void processBuyAppointInfoDay(BalanceFundVo balanceFundVo, Map<String, List<HighProductAppointmentInfoModel>> productAppointMap,
                                          Map<String, HighProductBaseInfoModel> productInfoMap, Map<String, HighProductStatInfoModel> productStatMap,
                                          WorkDayModel workDayModel) {
        HighProductBaseInfoModel highProductBaseInfoModel = productInfoMap.get(balanceFundVo.getFundCode());
        if (workDayModel != null && highProductBaseInfoModel != null && highProductBaseInfoModel.getIpoEndDt() != null
                && workDayModel.getWorkday().compareTo(highProductBaseInfoModel.getIpoEndDt()) <= 0) {
            // 认购
            List<HighProductAppointmentInfoModel> appointList = productAppointMap.get(balanceFundVo.getFundCode());
            for (HighProductAppointmentInfoModel highProductAppointmentInfoModel : appointList) {
                if (BusinessCodeEnum.SUBS.getMCode().equals(highProductAppointmentInfoModel.getmBusiCode())) {
                    balanceFundVo.setBuyAppointStartDt(highProductAppointmentInfoModel.getAppointStartDt());
                    balanceFundVo.setBuyAppointEndDt(highProductAppointmentInfoModel.getApponitEndDt());
                    balanceFundVo.setPayEndDate(highProductAppointmentInfoModel.getPayDeadlineDtm());
                    balanceFundVo.setOpenStartDate(highProductAppointmentInfoModel.getOpenStartDt());
                    balanceFundVo.setOpenEndDate(highProductAppointmentInfoModel.getOpenEndDt());
                    break;
                }
            }
        } else if (highProductBaseInfoModel != null && BusiUtil.isSupportAdvanceBuy(highProductBaseInfoModel.getIsScheduledTrade())) {
            List<HighProductAppointmentInfoModel> appointList = productAppointMap.get(balanceFundVo.getFundCode());
            if (appointList != null) {
                for (HighProductAppointmentInfoModel highProductAppointmentInfoModel : appointList) {
                    if (BusinessCodeEnum.PURCHASE.getMCode().equals(highProductAppointmentInfoModel.getmBusiCode())
                            || BusinessCodeEnum.SUBS.getMCode().equals(highProductAppointmentInfoModel.getmBusiCode())) {
                        balanceFundVo.setBuyAppointStartDt(highProductAppointmentInfoModel.getAppointStartDt());
                        balanceFundVo.setBuyAppointEndDt(highProductAppointmentInfoModel.getApponitEndDt());
                        balanceFundVo.setPayEndDate(highProductAppointmentInfoModel.getPayDeadlineDtm());
                        balanceFundVo.setOpenStartDate(highProductAppointmentInfoModel.getOpenStartDt());
                        balanceFundVo.setOpenEndDate(highProductAppointmentInfoModel.getOpenEndDt());
                        break;
                    }
                }
            }
        } else {
            HighProductStatInfoModel highProductStatInfoModel = productStatMap.get(balanceFundVo.getFundCode());
            if (workDayModel != null && highProductStatInfoModel != null && (FundNavStatusType.isPur(highProductStatInfoModel.getFundStat())
                    || FundNavStatusType.isSubs(highProductStatInfoModel.getFundStat()))) {
                balanceFundVo.setBuyAppointStartDt(workDayModel.getWorkday());
                balanceFundVo.setBuyAppointEndDt(workDayModel.getWorkday());
                balanceFundVo.setPayEndDate(workDayModel.getWorkday() + DEFAULT_TIME);
            }
        }
    }


    /**
     * 处理卖出预约日历信息
     */
    private void processSellAppointInfoDay(BalanceFundVo balanceFundVo, Map<String, List<HighProductAppointmentInfoModel>> productAppointMap,
                                           Map<String, HighProductBaseInfoModel> productInfoMap, Map<String, HighProductStatInfoModel> productStatMap,
                                           WorkDayModel workDayModel) {
        HighProductBaseInfoModel highProductBaseInfoModel = productInfoMap.get(balanceFundVo.getFundCode());
        if (highProductBaseInfoModel != null && BusiUtil.isSupportAdvanceRedeem(highProductBaseInfoModel.getIsScheduledTrade())) {
            List<HighProductAppointmentInfoModel> appointList = productAppointMap.get(balanceFundVo.getFundCode());
            if (appointList != null) {
                for (HighProductAppointmentInfoModel highProductAppointmentInfoModel : appointList) {
                    if (BusinessCodeEnum.REDEEM.getMCode().equals(highProductAppointmentInfoModel.getmBusiCode())) {
                        balanceFundVo.setSellAppointStartDt(highProductAppointmentInfoModel.getAppointStartDt());
                        balanceFundVo.setSellAppointEndDt(highProductAppointmentInfoModel.getApponitEndDt());

                        break;
                    }
                }
            }
        } else {
            HighProductStatInfoModel highProductStatInfoModel = productStatMap.get(balanceFundVo.getFundCode());
            if (workDayModel != null && highProductStatInfoModel != null && (FundNavStatusType.isRedeem(highProductStatInfoModel.getFundStat()))) {
                balanceFundVo.setSellAppointStartDt(workDayModel.getWorkday());
                balanceFundVo.setSellAppointEndDt(workDayModel.getWorkday());
            }
        }


    }


    /**
     * 添加额外信息
     *
     * @param balanceFundVo                 产品持仓信息
     * @param queryBalanceDetailOtherInfoVo 额外信息
     */
    private void setOtherBalanceInfo(BalanceFundVo balanceFundVo, QueryBalanceDetailOtherInfoVo queryBalanceDetailOtherInfoVo) {
        // 连续持有天数
        balanceFundVo.setBalanceDayNum(queryBalanceDetailOtherInfoVo.getBalanceDayNum());
        // 开始持有日期
        balanceFundVo.setStartHoldDay(queryBalanceDetailOtherInfoVo.getStartHoldDay());
        // 当前日期
        balanceFundVo.setNowDt(queryBalanceDetailOtherInfoVo.getNowDt());
        // 用户持仓明细信息
        balanceFundVo.setAcctBalanceDetailBaseInfoVo(queryBalanceDetailOtherInfoVo.getAcctBalanceDetailBaseInfoVo());
    }

    /**
     * 处理非分期成立产品
     */
    private void processNormalFund(BalanceBean balanceBean, BalanceFundVo balanceFundVo) {
        // 认缴金额
        balanceFundVo.setPaidInAmt(balanceBean.getPaidInAmt());
        // 认缴金额,展示字段
        if (balanceBean.getPaidInAmt() != null) {
            balanceFundVo.setPaidInAmtStr(balanceBean.getPaidInAmt().toPlainString());
        }
        // 实缴金额
        balanceFundVo.setPaidTotalAmt(balanceBean.getPaidTotalAmt());
        // 实缴金额展示字段
        if (balanceBean.getPaidTotalAmt() != null) {
            balanceFundVo.setPaidTotalAmtStr(balanceBean.getPaidTotalAmt().toPlainString());
        }
        // 千禧年待投金额
        balanceFundVo.setCurrencyUnPaidInAmt(balanceBean.getCurrencyUnPaidInAmt());
        //  千禧年待投金额,展示字段
        if (balanceBean.getCurrencyUnPaidInAmt() != null) {
            balanceFundVo.setCurrencyUnPaidInAmtStr(balanceBean.getCurrencyUnPaidInAmt().toPlainString());
        }
        // 实缴百分比
        balanceFundVo.setPaidSubTotalRatio(balanceBean.getPaidSubTotalRatio());
        // 日收益
        balanceFundVo.setDayIncome(balanceBean.getDailyAsset());
        // 日收益展示字段
        if (balanceBean.getDailyAsset() != null) {
            balanceFundVo.setDayIncomeStr(balanceBean.getDailyAsset().toPlainString());
        }
        // 累计收益
        balanceFundVo.setAccumIncome(balanceBean.getAccumIncome());
        //  累计收益,展示字段
        if (balanceBean.getAccumIncome() != null) {
            balanceFundVo.setAccumIncomeStr(balanceBean.getAccumIncome().toPlainString());
        }
        // 最新收益日期,yyyyMMdd,本年度是MMdd
        balanceFundVo.setIncomeLatestDay(getProcessedDt(balanceBean.getIncomeDt()));
        // 持仓份额
        balanceFundVo.setBalanceVol(balanceBean.getBalanceVol());
        // 持仓份额,展示字段
        if (balanceBean.getBalanceVol() != null) {
            balanceFundVo.setBalanceVolStr(balanceBean.getBalanceVol().toPlainString());
        }
        // 累计应收管理费
        balanceFundVo.setReceiveManageFee(balanceBean.getReceivManageFee());
        // 累计应收管理费,展示字段,千分位,四舍五入
        if (balanceBean.getReceivManageFee() != null) {
            balanceFundVo.setReceiveManageFeeStr(MoneyUtil.format(balanceBean.getReceivManageFee(), 2, RoundingMode.HALF_UP));
        }
        // 万份收益
        balanceFundVo.setCopiesIncome(balanceBean.getCopiesIncome());
        // 万份收益,展示字段,千分位,四舍五入
        if (balanceBean.getCopiesIncome() != null) {
            balanceFundVo.setCopiesIncomeStr(MoneyUtil.format(balanceBean.getCopiesIncome(), 2, RoundingMode.HALF_UP));
        }
        // 投资总成本
        balanceFundVo.setCurrencyNetBuyAmount(balanceBean.getCurrencyNetBuyAmount());
        // 投资总成本,展示字段
        if (balanceBean.getCurrencyNetBuyAmount() != null) {
            balanceFundVo.setCurrencyNetBuyAmountStr(MoneyUtil.format(balanceBean.getCurrencyNetBuyAmount(), 2, RoundingMode.HALF_UP));
        }
        // 累计应收业绩报酬
        balanceFundVo.setReceivePreformFee(balanceBean.getReceivPreformFee());
        // 累计应收业绩报酬,展示字段,千分位,四舍五入
        if (balanceBean.getReceivPreformFee() != null) {
            balanceFundVo.setReceivePreformFeeStr(MoneyUtil.format(balanceBean.getReceivPreformFee(), 2, RoundingMode.HALF_UP));
        }
        // 平衡因子
        balanceFundVo.setBalanceFactor(balanceBean.getBalanceFactor());

        // 平衡因子,展示字段
        if (balanceBean.getBalanceFactor() != null) {
            balanceFundVo.setBalanceFactorStr(MoneyUtil.format(balanceBean.getBalanceFactor(), 2, RoundingMode.HALF_UP));
        }
        // 平衡因子日期
        balanceFundVo.setBalanceFactorDate(balanceBean.getBalanceFactorDate());

        // 产品成立日期
        balanceFundVo.setEstablishDt(balanceBean.getEstablishDt());
        // 固收货币产品七日年化收益,如果分期成立的在子基金中
        balanceFundVo.setYieldIncome(balanceBean.getYieldIncome());
        // 固收货币产品七日年化收益,如果分期成立的在子基金中,展示字段
        if (balanceBean.getYieldIncome() != null) {
            balanceFundVo.setYieldIncomeStr(balanceBean.getYieldIncome() + "");
        }
        // 固收货币产品净值日期
        balanceFundVo.setYieldIncomeDt(balanceBean.getYieldIncomeDt());
        // 单位持仓成本(当前币种)
        balanceFundVo.setUnitBalanceCostExFee(balanceBean.getUnitBalanceCostExFee());
        // 单位持仓成本(当前币种),展示字段
        if (balanceBean.getUnitBalanceCostExFee() != null) {
            balanceFundVo.setUnitBalanceCostExFeeStr(balanceBean.getUnitBalanceCostExFee().toPlainString());
        }
        // 单位持仓成本(人民币)
        balanceFundVo.setUnitBalanceCostExFeeRmb(balanceBean.getUnitBalanceCostExFeeRmb());
        // 单位持仓成本(人民币),展示字段
        if (balanceBean.getUnitBalanceCostExFeeRmb() != null) {
            balanceFundVo.setUnitBalanceCostExFeeRmbStr(balanceBean.getUnitBalanceCostExFeeRmb().toPlainString());
        }
        // 净值分红标识,,0-无需分红提醒;1-提醒收益偏差;2-提醒即将分红
        balanceFundVo.setNavDivFlag(balanceBean.getNavDivFlag());
        // 起息日
        balanceFundVo.setValueDate(balanceBean.getValueDate());
        // 到期日
        balanceFundVo.setDueDate(balanceBean.getDueDate());
        // 异常标识 0-否; 1-是
        balanceFundVo.setAbnormalState(balanceBean.getAbnormalFlag());
        // 总市值
        balanceFundVo.setMarketValue(balanceBean.getMarketValue());
        // 总市值(展示字段)
        if (balanceBean.getMarketValue() != null) {
            balanceFundVo.setMarketValueStr(balanceBean.getMarketValue().toPlainString());
        }
        // 市值(当前币种)
        balanceFundVo.setCurrencyMarketValue(balanceBean.getCurrencyMarketValue());
        // 市值(当前币种)(展示字段)
        if (balanceBean.getCurrencyMarketValue() != null) {
            balanceFundVo.setCurrencyMarketValueStr(balanceBean.getCurrencyMarketValue().toPlainString());
        }
        // 费后参考市值(当前币种)
        balanceFundVo.setCurrencyMarketValueExFee(balanceBean.getCurrencyMarketValueExFee());
        // 费后参考市值(当前币种)(展示字段)
        if (balanceBean.getCurrencyMarketValueExFee() != null) {
            balanceFundVo.setCurrencyMarketValueExFeeStr(balanceBean.getCurrencyMarketValueExFee().toPlainString());
        }
        // 最新净值
        balanceFundVo.setNav(balanceBean.getNav());
        // 最新净值,展示字段
        if (balanceBean.getNav() != null) {
            balanceFundVo.setNavStr(balanceBean.getNav().toPlainString());
        }
        // 最新净值日期yyyyMMdd
        balanceFundVo.setNavDt(getProcessedDt(balanceBean.getNavDt()));
        // 收益计算状态 0-计算中;1-计算成功
        balanceFundVo.setIncomeCalStat(balanceBean.getIncomeCalStat());
        // 当前收益（人民币）
        balanceFundVo.setCurrentAsset(balanceBean.getCurrentAsset());
        // 当前收益（展示字段）（人民币）
        if (balanceBean.getCurrentAsset() != null) {
            balanceFundVo.setCurrentAssetStr(balanceBean.getCurrentAsset() + "");
        }
        // 当前收益（当前币种）
        balanceFundVo.setCurrentAssetCurrency(balanceBean.getCurrentAssetCurrency());
        // 当前收益（当前币种）
        if (balanceBean.getCurrentAssetCurrency() != null) {
            balanceFundVo.setCurrentAssetCurrencyStr(balanceBean.getCurrentAssetCurrency() + "");
        }
        // 总资产
        balanceFundVo.setTotalAsset(balanceBean.getTotalAssert());
        // 总资产（展示字段）
        if (balanceBean.getTotalAssert() != null) {
            balanceFundVo.setTotalAssetStr(balanceBean.getTotalAssert() + "");
        }
        //  持仓收益率
        balanceFundVo.setYieldRate(balanceBean.getYieldRate());
        // 持仓收益率（展示字段）
        if (balanceBean.getYieldRate() != null) {
            balanceFundVo.setYieldRateStr(balanceBean.getYieldRate() + "");
        }
        // 回款金额回款金额
        balanceFundVo.setCashCollection(balanceBean.getCashCollection());
        if (balanceBean.getCashCollection() != null) {
            balanceFundVo.setCashCollectionStr(balanceBean.getCashCollection().toPlainString());
        }
        // 回款比例
        balanceFundVo.setCashCollectionRatio(balanceBean.getAccumBackRatio());
        // 日收益率
        balanceFundVo.setDayIncomeRate(balanceBean.getDayAssetRate());
        // 日收益率,展示字段,乘以100,位数补齐,拼%,千分位处理
        if (balanceBean.getDayAssetRate() != null) {
            balanceFundVo.setDayIncomeRateStr(balanceBean.getDayAssetRate() + "");
        }
    }

    /**
     * 处理分期成立产品
     */
    private void processStageEstablishFund(List<BalanceBean> balanceList, BalanceFundVo balanceFundVo) {
        // 处理子产品信息
        List<BalanceFundItemDtlVo> itemDtlList = new ArrayList<>();
        for (BalanceBean subBalanceBean : balanceList) {
            BalanceFundItemDtlVo itemDtl = getBalanceFundItemDtlVo(subBalanceBean);
            itemDtlList.add(itemDtl);
        }
        balanceFundVo.setBalanceFundItemDtlList(itemDtlList);
        // 部分字段需要遍历汇总子基金信息
        sumSubItemInfo(itemDtlList, balanceFundVo);
        // 设置待资金到账与待确认信息
        setRefundAndUnConfirm(balanceList, balanceFundVo);
    }

    /**
     * item维度汇总
     *
     * @param itemDtlList   item维度信息
     * @param balanceFundVo 基金信息
     */
    private void sumSubItemInfo(List<BalanceFundItemDtlVo> itemDtlList, BalanceFundVo balanceFundVo) {
        BigDecimal currentAsset = BigDecimal.ZERO;
        BigDecimal currentAssetCurrency = BigDecimal.ZERO;
        BigDecimal totalAsset = BigDecimal.ZERO;
        BigDecimal cashCollection = BigDecimal.ZERO;
        // 异常标识 0-否; 1-是
        String abnormalState = YesOrNoEnum.NO.getCode();
        // 总市值
        BigDecimal marketValue = BigDecimal.ZERO;
        // 市值(当前币种)
        BigDecimal currencyMarketValue = BigDecimal.ZERO;
        // 费后参考市值(当前币种)
        BigDecimal currencyMarketValueExFee = BigDecimal.ZERO;
        // 收益计算状态 0-计算中;1-计算成功
        String incomeCalStat = YesOrNoEnum.YES.getCode();
        // 实缴金额,分期成立的母基金维度是子基金的实缴金额之和
        BigDecimal paidTotalAmt = BigDecimal.ZERO;
        // 千禧年待投金额
        BigDecimal currencyUnPaidInAmt = BigDecimal.ZERO;
        // 认缴金额
        BigDecimal paidInAmt = BigDecimal.ZERO;
        // 日收益,分期成立的母基金维度是子基金之和
        BigDecimal dayIncome = BigDecimal.ZERO;
        //累计收益,分期成立的母基金维度是子基金之和
        BigDecimal accumIncome = BigDecimal.ZERO;
        // 持仓收益率,如果是分期成立的,母基金维度是子基金中的最大的持仓收益率
        BigDecimal yieldRate = BigDecimal.ZERO;
        // 持仓份额,分期成立的,是子基金份额之和
        BigDecimal balanceVol = BigDecimal.ZERO;
        //  累计应收管理费
        BigDecimal receiveManageFee = BigDecimal.ZERO;
        // 累计应收业绩报酬
        BigDecimal receivePreformFee = BigDecimal.ZERO;
        for (BalanceFundItemDtlVo balanceFundItemDtlVo : itemDtlList) {
            // 当前收益（人民币）如果是分期成立的,这里是子基金中当前收益之和
            BigDecimal subCurrentAsset = balanceFundItemDtlVo.getCurrentAsset() == null ? BigDecimal.ZERO : balanceFundItemDtlVo.getCurrentAsset();
            currentAsset = currentAsset.add(subCurrentAsset);
            // 当前收益（人民币）如果是分期成立的,这里是子基金中当前收益之和
            BigDecimal subCurrentAssetCurrency = balanceFundItemDtlVo.getCurrentAssetCurrency() == null ? BigDecimal.ZERO : balanceFundItemDtlVo.getCurrentAssetCurrency();
            currentAssetCurrency = currentAssetCurrency.add(subCurrentAssetCurrency);
            // 总资产
            BigDecimal subTotalAsset = balanceFundItemDtlVo.getTotalAsset() == null ? BigDecimal.ZERO : balanceFundItemDtlVo.getTotalAsset();
            totalAsset = totalAsset.add(subTotalAsset);
            // 回款金额回款金额
            BigDecimal subCashCollection = balanceFundItemDtlVo.getCashCollection() == null ? BigDecimal.ZERO : balanceFundItemDtlVo.getCashCollection();
            balanceFundVo.setCashCollection(cashCollection.add(subCashCollection));
            // 异常标识 0-否; 1-是
            if (YesOrNoEnum.YES.getCode().equals(balanceFundItemDtlVo.getAbnormalState())) {
                abnormalState = YesOrNoEnum.YES.getCode();
            }
            // 总市值
            BigDecimal subMarketValue = balanceFundItemDtlVo.getMarketValue() == null ? BigDecimal.ZERO : balanceFundItemDtlVo.getMarketValue();
            marketValue = marketValue.add(subMarketValue);
            // 市值(当前币种)
            BigDecimal subCurrencyMarketValue = balanceFundItemDtlVo.getCurrencyMarketValue() == null ? BigDecimal.ZERO : balanceFundItemDtlVo.getCurrencyMarketValue();
            currencyMarketValue = currencyMarketValue.add(subCurrencyMarketValue);
            // 费后参考市值(当前币种)
            BigDecimal subCurrencyMarketValueExFee = balanceFundItemDtlVo.getCurrencyMarketValueExFee() == null ? BigDecimal.ZERO : balanceFundItemDtlVo.getCurrencyMarketValueExFee();
            currencyMarketValueExFee = currencyMarketValueExFee.add(subCurrencyMarketValueExFee);
            // 收益计算状态 0-计算中;1-计算成功
            if (YesOrNoEnum.YES.getCode().equals(balanceFundItemDtlVo.getIncomeCalStat())) {
                incomeCalStat = YesOrNoEnum.NO.getCode();
            }
            // 实缴金额,分期成立的母基金维度是子基金的实缴金额之和
            paidTotalAmt = paidTotalAmt.add(balanceFundItemDtlVo.getPaidTotalAmt() == null ? BigDecimal.ZERO : balanceFundItemDtlVo.getPaidTotalAmt());
            // 千禧年待投金额
            currencyUnPaidInAmt = currencyUnPaidInAmt.add(balanceFundItemDtlVo.getCurrencyUnPaidInAmt() == null ? BigDecimal.ZERO : balanceFundItemDtlVo.getCurrencyUnPaidInAmt());
            // 认缴金额
            paidInAmt = paidInAmt.add(balanceFundItemDtlVo.getPaidInAmt() == null ? BigDecimal.ZERO : balanceFundItemDtlVo.getPaidInAmt());
            // 日收益,分期成立的母基金维度是子基金之和
            dayIncome = dayIncome.add(balanceFundItemDtlVo.getDayIncome() == null ? BigDecimal.ZERO : balanceFundItemDtlVo.getDayIncome());
            // 累计收益,分期成立的母基金维度是子基金之和
            accumIncome = accumIncome.add(balanceFundItemDtlVo.getAccumIncome() == null ? BigDecimal.ZERO : balanceFundItemDtlVo.getAccumIncome());
            // 持仓收益率,如果是分期成立的,母基金维度是子基金中的最大的持仓收益率
            if (balanceFundItemDtlVo.getYieldRate() != null && balanceFundItemDtlVo.getYieldRate().compareTo(yieldRate) > 0) {
                yieldRate = balanceFundItemDtlVo.getYieldRate();
            }
            // 持仓份额,分期成立的,是子基金份额之和
            balanceVol = balanceVol.add(balanceFundItemDtlVo.getBalanceVol() == null ? BigDecimal.ZERO : balanceFundItemDtlVo.getBalanceVol());
            // 累计应收管理费
            receiveManageFee = receiveManageFee.add(balanceFundItemDtlVo.getReceiveManageFee() == null ? BigDecimal.ZERO : balanceFundItemDtlVo.getReceiveManageFee());
            // 累计应收业绩报酬
            receivePreformFee = receiveManageFee.add(balanceFundItemDtlVo.getReceivePreformFee() == null ? BigDecimal.ZERO : balanceFundItemDtlVo.getReceivePreformFee());

        }
        // 设置汇总结果
        balanceFundVo.setCurrentAsset(currentAsset);
        balanceFundVo.setCurrentAssetCurrency(currentAssetCurrency);
        balanceFundVo.setTotalAsset(totalAsset);
        balanceFundVo.setCashCollection(cashCollection);
        // 总市值
        balanceFundVo.setMarketValue(marketValue);
        // 市值(当前币种)
        balanceFundVo.setCurrencyMarketValue(currencyMarketValue);
        // 费后参考市值(当前币种)
        balanceFundVo.setCurrencyMarketValueExFee(currencyMarketValueExFee);
        // 实缴金额,分期成立的母基金维度是子基金的实缴金额之和
        balanceFundVo.setPaidTotalAmt(paidTotalAmt);
        // 千禧年待投金额
        balanceFundVo.setCurrencyUnPaidInAmt(currencyUnPaidInAmt);
        // 实缴金额
        balanceFundVo.setPaidInAmt(paidInAmt);
        // 日收益,分期成立的母基金维度是子基金之和
        balanceFundVo.setDayIncome(dayIncome);
        //累计收益,分期成立的母基金维度是子基金之和
        balanceFundVo.setAccumIncome(accumIncome);
        // 持仓收益率,如果是分期成立的,母基金维度是子基金中的最大的持仓收益率
        balanceFundVo.setYieldRate(yieldRate);
        // 持仓份额,分期成立的,是子基金份额之和
        balanceFundVo.setBalanceVol(balanceVol);
        // 是否存在异常,持仓中只要有一笔存在异常，总资产就需显示计算中标识;1:是,0:否
        balanceFundVo.setAbnormalState(abnormalState);
        // 收益计算状态 0-计算中;1-计算成功
        balanceFundVo.setIncomeCalStat(incomeCalStat);
        // 累计应收管理费
        balanceFundVo.setReceiveManageFee(receiveManageFee);
        // 累计应收管理费,展示字段,千分位,四舍五入
        if (receiveManageFee != null) {
            balanceFundVo.setReceiveManageFeeStr(MoneyUtil.format(receiveManageFee, 2, RoundingMode.HALF_UP));
        }
        // 累计应收业绩报酬
        balanceFundVo.setReceivePreformFee(receivePreformFee);
        // 累计应收业绩报酬,展示字段,千分位,四舍五入
        if (receivePreformFee != null) {
            balanceFundVo.setReceivePreformFeeStr(MoneyUtil.format(receivePreformFee, 2, RoundingMode.HALF_UP));
        }

    }

    /**
     * 待确认信息跟待资金到账的
     */
    private void setRefundAndUnConfirm(List<BalanceBean> balanceList, BalanceFundVo balanceFundVo) {
        // 在途订单信息信息
        BalanceFundUnConfirmVo balanceFundUnConfirmInfo = new BalanceFundUnConfirmVo();
        balanceFundVo.setBalanceFundUnConfirmInfo(balanceFundUnConfirmInfo);
        // 待资金到账订单信息
        BalanceFundUnRefundInfoVo balanceFundUnRefundInfo = new BalanceFundUnRefundInfoVo();
        balanceFundVo.setBalanceFundUnRefundInfo(balanceFundUnRefundInfo);
        for (BalanceBean bean : balanceList) {
            //在途订单信息信息
            BalanceFundUnConfirmInfoDTO subBalanceFundUnConfirmInfo = bean.getBalanceFundUnConfirmInfo();
            if (subBalanceFundUnConfirmInfo != null) {
                addUnConfirmInfo(balanceFundUnConfirmInfo, subBalanceFundUnConfirmInfo);
            }
            // 待资金到账
            BalanceFundUnRefundInfoDTO subBalanceFundUnRefundInfo = bean.getBalanceFundUnRefundInfo();
            if (subBalanceFundUnRefundInfo != null) {
                addUnRefundInfo(balanceFundUnRefundInfo, subBalanceFundUnRefundInfo);
            }

        }

    }

    /**
     * 待资金到账数据新增
     *
     * @param balanceFundUnRefundInfo    待资金到账信息
     * @param subBalanceFundUnRefundInfo 待资金到账信息
     */
    private void addUnRefundInfo(BalanceFundUnRefundInfoVo balanceFundUnRefundInfo, BalanceFundUnRefundInfoDTO subBalanceFundUnRefundInfo) {
        if (subBalanceFundUnRefundInfo == null) {
            return;
        }
        List<RefundDealOrderDTO> subRefundDealOrderList = subBalanceFundUnRefundInfo.getRefundDealOrderList();
        if (CollectionUtils.isNotEmpty(subRefundDealOrderList)) {
            balanceFundUnRefundInfo.setWaitRefundNum(subBalanceFundUnRefundInfo.getRefundDealOrderList().size());
            balanceFundUnRefundInfo.setTotalWaitRefundAmt(subBalanceFundUnRefundInfo.getTotalRefundAmt());
            List<WaitRefundOrderDtlVo> waitRefundOrderDtlVoList = balanceFundUnRefundInfo.getWaitRefundOrderDtlVoList() == null ? new ArrayList<>() : balanceFundUnRefundInfo.getWaitRefundOrderDtlVoList();
            for (RefundDealOrderDTO refundDealOrderDTO : subRefundDealOrderList) {
                WaitRefundOrderDtlVo waitRefundOrderDtlVo = new WaitRefundOrderDtlVo();
                waitRefundOrderDtlVo.setDealNo(refundDealOrderDTO.getDealNo());
                waitRefundOrderDtlVo.setRefundAmt(refundDealOrderDTO.getRefundAmt());
                waitRefundOrderDtlVo.setMBusiCode(refundDealOrderDTO.getMBusiCode());
                waitRefundOrderDtlVo.setMBusiCodeStr(BusinessCodeEnum.getName(refundDealOrderDTO.getMBusiCode()));
                waitRefundOrderDtlVo.setRedeemDirection(refundDealOrderDTO.getRedeemDirection());
                waitRefundOrderDtlVo.setRedeemDirectionStr(getRedeemDirectionStr(refundDealOrderDTO.getRedeemDirection()));
                waitRefundOrderDtlVoList.add(waitRefundOrderDtlVo);
            }
            balanceFundUnRefundInfo.setWaitRefundOrderDtlVoList(waitRefundOrderDtlVoList);
        }
    }

    /**
     * 获取赎回方向
     *
     * @param redeemDirection 赎回方向
     * @return 赎回方向中文
     */
    private String getRedeemDirectionStr(String redeemDirection) {
        if (StringUtils.isBlank(redeemDirection)) {
            return null;
        }
        return RedeemDirectionEnum.CARD.getCode().equals(redeemDirection) ? RedeemDirectionEnum.CARD.getName() : RedeemDirectionEnum.CXG.getName();
    }


    /**
     * 添加在途订单信息信息
     *
     * @param balanceFundUnConfirmInfo    母基金汇总信息
     * @param subBalanceFundUnConfirmInfo 子基金在途信息
     */
    private void addUnConfirmInfo(BalanceFundUnConfirmVo balanceFundUnConfirmInfo, BalanceFundUnConfirmInfoDTO subBalanceFundUnConfirmInfo) {
        // 子基信息判空
        if (subBalanceFundUnConfirmInfo == null) {
            return;
        }
        // 1.如果买入待确认数据有值,母基金维度需要添加
        List<BuyUnConfirmOrderDTO> subBuyUnConfirmList = subBalanceFundUnConfirmInfo.getBuyUnConfirmList();
        if (CollectionUtils.isNotEmpty(subBuyUnConfirmList)) {
            // 总待确认金额当前币种
            BigDecimal totalUnConfirmAmt = balanceFundUnConfirmInfo.getTotalUnConfirmAmt() == null ? BigDecimal.ZERO : balanceFundUnConfirmInfo.getTotalUnConfirmAmt();
            // 买入待确认交易
            List<BuyUnConfirmOrderVo> buyUnConfirmList = balanceFundUnConfirmInfo.getBuyUnConfirmList() == null ? new ArrayList<BuyUnConfirmOrderVo>() : balanceFundUnConfirmInfo.getBuyUnConfirmList();
            // 总待确认金额:申请净金额-储蓄罐预约冻结金额(人民币),注意判断空,母基金的直接取子基金的汇总,因为子基金只有一个有值
            balanceFundUnConfirmInfo.setTotalUnConfirmAmt(subBalanceFundUnConfirmInfo.getTotalUnConfirmAmt());
            // 买入待确认数量,,母基金的直接取子基金的汇总,因为子基金只有一个有值
            balanceFundUnConfirmInfo.setBuyUnConfirmNum(subBalanceFundUnConfirmInfo.getBuyUnConfirmNum());
            // 买入到确认交易
            for (BuyUnConfirmOrderDTO buyUnConfirmOrderDTO : subBuyUnConfirmList) {
                BuyUnConfirmOrderVo buyUnConfirmOrderVo = new BuyUnConfirmOrderVo();
                // 订单号
                buyUnConfirmOrderVo.setDealNo(buyUnConfirmOrderDTO.getDealNo());
                //产品编码
                buyUnConfirmOrderVo.setFundCode(buyUnConfirmOrderDTO.getFundCode());
                // 待确认金额:申请净金额-储蓄罐预约冻结金额(人民币)
                buyUnConfirmOrderVo.setUnConfirmAmt(buyUnConfirmOrderDTO.getUnConfirmAmt());
                //中台业务类型
                buyUnConfirmOrderVo.setMBusiCode(buyUnConfirmOrderDTO.getMBusiCode());
                //中台业务类型
                buyUnConfirmOrderVo.setMBusiCodeStr(BusinessCodeEnum.getName(buyUnConfirmOrderDTO.getMBusiCode()));
                // 分销渠道
                buyUnConfirmOrderVo.setDisCode(buyUnConfirmOrderDTO.getDisCode());
                buyUnConfirmList.add(buyUnConfirmOrderVo);
            }
            balanceFundUnConfirmInfo.setBuyUnConfirmList(buyUnConfirmList);
        }
        // 2.卖出待确认
        List<SellUnConfirmOrderDTO> subSellUnConfirmList = subBalanceFundUnConfirmInfo.getSellUnConfirmList();
        if (CollectionUtils.isNotEmpty(subSellUnConfirmList)) {
            // 是否全赎,1:是,0:不是
            balanceFundUnConfirmInfo.setAllIsRedeem(subBalanceFundUnConfirmInfo.getAllIsRedeem());
            // 是否全部上报,1:是,0:不是
            balanceFundUnConfirmInfo.setAllSubmitRedeem(subBalanceFundUnConfirmInfo.getAllSubmitRedeem());
            // 最大赎回上报日
            balanceFundUnConfirmInfo.setMaxRedeemSubmitTaDt(subBalanceFundUnConfirmInfo.getMaxRedeemSubmitTaDt());
            //总赎回份额,母基金的直接取子基金的汇总,因为子基金只有一个有值
            balanceFundUnConfirmInfo.setTotalSellVol(subBalanceFundUnConfirmInfo.getTotalSellVol());
            // 卖出待确认数量,母基金的直接取子基金的汇总,因为子基金只有一个有值
            balanceFundUnConfirmInfo.setSellUnConfirmNum(subBalanceFundUnConfirmInfo.getSellUnConfirmNum());
            // 卖出待确认交易
            List<SellUnConfirmOrderVo> sellUnConfirmList = balanceFundUnConfirmInfo.getSellUnConfirmList() == null ? new ArrayList<>() : balanceFundUnConfirmInfo.getSellUnConfirmList();
            for (SellUnConfirmOrderDTO sellUnConfirmOrderDTO : subSellUnConfirmList) {
                SellUnConfirmOrderVo sellUnConfirmOrderVo = new SellUnConfirmOrderVo();
                sellUnConfirmOrderVo.setDealNo(sellUnConfirmOrderDTO.getDealNo());
                sellUnConfirmOrderVo.setFundCode(sellUnConfirmOrderDTO.getFundCode());
                sellUnConfirmOrderVo.setAppVol(sellUnConfirmOrderDTO.getAppVol());
                sellUnConfirmOrderVo.setMBusiCode(sellUnConfirmOrderDTO.getMBusiCode());
                sellUnConfirmOrderVo.setMBusiCodeStr(BusinessCodeEnum.getName(sellUnConfirmOrderDTO.getMBusiCode()));
                sellUnConfirmOrderVo.setNotifySubmitFlag(sellUnConfirmOrderDTO.getNotifySubmitFlag());
                sellUnConfirmOrderVo.setSubmitTaDt(sellUnConfirmOrderDTO.getSubmitTaDt());
                sellUnConfirmList.add(sellUnConfirmOrderVo);
            }
            balanceFundUnConfirmInfo.setSellUnConfirmList(sellUnConfirmList);
        }
    }


    /**
     * 构建子产品信息
     */
    private BalanceFundItemDtlVo getBalanceFundItemDtlVo(BalanceBean subBalanceBean) {
        BalanceFundItemDtlVo itemDtl = new BalanceFundItemDtlVo();
        // 子产品信息赋值
        itemDtl.setFundCode(subBalanceBean.getProductCode());
        itemDtl.setFundSubCode(subBalanceBean.getSubProductCode());
        // 当前收益（人民币）如果是分期成立的,这里是子基金中当前收益之和
        itemDtl.setCurrentAsset(subBalanceBean.getCurrentAsset());
        // 当前收益（展示字段）
        if (subBalanceBean.getCurrentAsset() != null) {
            itemDtl.setCurrentAssetStr(subBalanceBean.getCurrentAsset() + "");
        }
        // 清算标识 0-否; 1-是
        itemDtl.setCrisisFlag(subBalanceBean.getCrisisFlag());
        //持仓收益率
        itemDtl.setYieldRate(subBalanceBean.getYieldRate());
        // 持仓收益率（展示字段）
        if (subBalanceBean.getYieldRate() != null) {
            itemDtl.setYieldRateStr(subBalanceBean.getYieldRate() + "");
        }
        // 私募股权回款金额
        itemDtl.setCashCollection(subBalanceBean.getCashCollection());
        // 私募股权回款金额（展示字段）
        if (subBalanceBean.getCashCollection() != null) {
            itemDtl.setCashCollectionStr(subBalanceBean.getCashCollection() + "");
        }
        // 回款比例
        itemDtl.setCashCollectionRatio(subBalanceBean.getAccumBackRatio());
        // 净值分红标识,,0-无需分红提醒;1-提醒收益偏差;2-提醒即将分红
        itemDtl.setNavDivFlag(subBalanceBean.getNavDivFlag());
        // 起息日
        itemDtl.setValueDate(subBalanceBean.getValueDate());
        // 到期日
        itemDtl.setDueDate(subBalanceBean.getDueDate());
        // 异常标识 0-否; 1-是
        itemDtl.setAbnormalState(subBalanceBean.getAbnormalFlag());
        // 市值
        itemDtl.setMarketValue(subBalanceBean.getMarketValue());
        // 固收货币产品七日年化收益,如果分期成立的在子基金中
        itemDtl.setYieldIncome(subBalanceBean.getYieldIncome());
        // 固收货币产品七日年化收益,如果分期成立的在子基金中,展示字段
        if (subBalanceBean.getYieldIncome() != null) {
            itemDtl.setYieldIncomeStr(subBalanceBean.getYieldIncome() + "");
        }
        // 万份收益
        itemDtl.setCopiesIncome(subBalanceBean.getCopiesIncome());
        // 万份收益,展示字段,千分位,四舍五入
        if (subBalanceBean.getCopiesIncome() != null) {
            itemDtl.setCopiesIncomeStr(MoneyUtil.format(subBalanceBean.getCopiesIncome(), 4, RoundingMode.HALF_UP));
        }
        // 产品成立日期
        itemDtl.setEstablishDt(subBalanceBean.getEstablishDt());
        // 固收货币产品净值日期
        itemDtl.setYieldIncomeDt(subBalanceBean.getYieldIncomeDt());
        //市值(展示字段)
        if (subBalanceBean.getMarketValue() != null) {
            itemDtl.setMarketValueStr(subBalanceBean.getMarketValue() + "");
        }
        // 市值(当前币种)
        itemDtl.setCurrencyMarketValue(subBalanceBean.getCurrencyMarketValue());
        //市值(展示字段)
        if (subBalanceBean.getCurrencyMarketValue() != null) {
            itemDtl.setCurrencyMarketValueStr(subBalanceBean.getCurrencyMarketValue() + "");
        }
        // 最新净值,分期成立的没有值,在子基金中展示
        itemDtl.setNav(subBalanceBean.getNav());
        // 最新净值,展示字段
        if (subBalanceBean.getNav() != null) {
            itemDtl.setNavStr(subBalanceBean.getNav() + "");
        }
        // 最新净值日期yyyy-MM-dd,本年度,只有MM-dd
        String navDt = subBalanceBean.getNavDt();
        if (StringUtils.isNotBlank(navDt) && navDt.length() == 8) {
            navDt = getProcessedDt(navDt);
        }
        itemDtl.setNavDt(navDt);
        // 累计应收管理费
        itemDtl.setReceiveManageFee(subBalanceBean.getReceivManageFee());
        // 累计应收管理费,展示字段,千分位,四舍五入
        if (subBalanceBean.getReceivManageFee() != null) {
            itemDtl.setReceiveManageFeeStr(MoneyUtil.format(subBalanceBean.getReceivManageFee(), 2, RoundingMode.HALF_UP));
        }
        // 累计应收业绩报酬
        itemDtl.setReceivePreformFee(subBalanceBean.getReceivPreformFee());
        // 累计应收业绩报酬,展示字段,千分位,四舍五入
        if (subBalanceBean.getReceivPreformFee() != null) {
            itemDtl.setReceivePreformFeeStr(MoneyUtil.format(subBalanceBean.getReceivPreformFee(), 2, RoundingMode.HALF_UP));
        }
        // 平衡因子
        itemDtl.setBalanceFactor(subBalanceBean.getBalanceFactor());

        // 平衡因子,展示字段
        if (subBalanceBean.getBalanceFactor() != null) {
            itemDtl.setBalanceFactorStr(MoneyUtil.format(subBalanceBean.getBalanceFactor(), 2, RoundingMode.HALF_UP));
        }
        // 平衡因子日期
        itemDtl.setBalanceFactorDate(subBalanceBean.getBalanceFactorDate());
        // 收益计算状态 0-计算中;1-计算成功
        itemDtl.setIncomeCalStat(subBalanceBean.getIncomeCalStat());
        // 产品存续期限(类似于5+3+2这种说明)
        itemDtl.setProductDuration(subBalanceBean.getFundCXQXStr());
        // 产品期限说明
        itemDtl.setCpqxsm(subBalanceBean.getCpqxsm());
        // 是否分期成立,1-是;0-否
        itemDtl.setStageEstablishFlag(subBalanceBean.getStageEstablishFlag());
        // 实缴金额
        itemDtl.setPaidTotalAmt(subBalanceBean.getPaidTotalAmt());
        // 实缴金额展示字段
        if (subBalanceBean.getPaidTotalAmt() != null) {
            itemDtl.setPaidTotalAmtStr(subBalanceBean.getPaidTotalAmt() + "");
        }
        // 实缴百分比
        itemDtl.setPaidSubTotalRatio(subBalanceBean.getPaidSubTotalRatio());
        // 日收益
        itemDtl.setDayIncome(subBalanceBean.getDailyAsset());
        // 日收益,展示字段
        if (subBalanceBean.getDailyAsset() != null) {
            itemDtl.setDayIncomeStr(subBalanceBean.getDailyAsset() + "");
        }
        // 日收益率
        itemDtl.setDayIncomeRate(subBalanceBean.getDayAssetRate());
        // 日收益率,展示字段
        if (subBalanceBean.getDayAssetRate() != null) {
            itemDtl.setDayIncomeRateStr(subBalanceBean.getDayAssetRate() + "");
        }
        // 累计收益
        itemDtl.setAccumIncome(subBalanceBean.getAccumIncome());
        //累计收益,展示字段
        if (subBalanceBean.getAccumIncome() != null) {
            itemDtl.setAccumIncomeStr(subBalanceBean.getAccumIncome() + "");
        }
        // 最新收益日期,yyyyMMdd,本年度是MMdd
        itemDtl.setIncomeLatestDay(getProcessedDt(subBalanceBean.getIncomeDt()));
        // 持仓份额
        itemDtl.setBalanceVol(subBalanceBean.getBalanceVol());
        // 持仓份额,展示字段
        if (subBalanceBean.getBalanceVol() != null) {
            itemDtl.setBalanceVolStr(subBalanceBean.getBalanceVol() + "");
        }
        // 单位持仓成本(当前成本)
        itemDtl.setUnitBalanceCostExFee(subBalanceBean.getUnitBalanceCostExFee());
        // 单位持仓成本(当前币种),展示字段
        if (subBalanceBean.getUnitBalanceCostExFee() != null) {
            itemDtl.setUnitBalanceCostExFeeStr(subBalanceBean.getUnitBalanceCostExFee() + "");
        }
        // 单位持仓成本(人民币)
        itemDtl.setUnitBalanceCostExFeeRmb(subBalanceBean.getUnitBalanceCostExFeeRmb());
        // 单位持仓成本(人民币),展示字段
        if (subBalanceBean.getUnitBalanceCostExFeeRmb() != null) {
            itemDtl.setUnitBalanceCostExFeeRmbStr(subBalanceBean.getUnitBalanceCostExFeeRmb() + "");
        }
        // 市值(当前币种)
        itemDtl.setCurrencyMarketValue(subBalanceBean.getCurrencyMarketValue());
        //市值(展示字段)
        if (subBalanceBean.getCurrencyMarketValue() != null) {
            itemDtl.setCurrencyMarketValueStr(subBalanceBean.getCurrencyMarketValue() + "");
        }
        // 费后参考市值(当前币种)
        itemDtl.setCurrencyMarketValueExFee(subBalanceBean.getCurrencyMarketValueExFee());
        //费后参考市值(当前币种)(展示字段)
        if (subBalanceBean.getCurrencyMarketValueExFee() != null) {
            itemDtl.setCurrencyMarketValueExFeeStr(subBalanceBean.getCurrencyMarketValueExFee() + "");
        }
        // 认缴金额
        itemDtl.setPaidInAmt(subBalanceBean.getPaidInAmt());
        // 认缴金额,展示字段
        if (subBalanceBean.getPaidInAmt() != null) {
            itemDtl.setPaidInAmtStr(subBalanceBean.getPaidInAmt() + "");
        }
        // 千禧年待投金额
        itemDtl.setCurrencyUnPaidInAmt(subBalanceBean.getCurrencyUnPaidInAmt());
        // 千禧年待投金额,展示字段
        if (subBalanceBean.getCurrencyUnPaidInAmt() != null) {
            itemDtl.setCurrencyUnPaidInAmtStr(subBalanceBean.getCurrencyUnPaidInAmt() + "");
        }
        // 净值披露方式(1-净值,2-份额收益)
        itemDtl.setNavDisclosureType(subBalanceBean.getNavDisclosureType());
        // 当前收益（当前币种）
        itemDtl.setCurrentAssetCurrency(subBalanceBean.getCurrentAssetCurrency());
        // 当前收益（当前币种）（展示字段）
        if (subBalanceBean.getCurrentAssetCurrency() != null) {
            itemDtl.setCurrentAssetCurrencyStr(subBalanceBean.getCurrentAssetCurrency() + "");
        }
        // 总资产
        itemDtl.setTotalAsset(subBalanceBean.getTotalAssert());
        // 总资产,展示字段
        if (subBalanceBean.getTotalAssert() != null) {
            itemDtl.setTotalAssetStr(subBalanceBean.getTotalAssert() + "");
        }


        return itemDtl;
    }

    /**
     * 处理过的日期,默认,yyyy-MM-dd,本年度:MM-dd
     *
     * @param processDt 待处理日期,yyyyMMdd
     * @return 处理过的日期
     */
    private String getProcessedDt(String processDt) {
        if (StringUtils.isBlank(processDt)) {
            return null;
        }
        Date navDate = DateUtils.formatToDate(processDt, DateUtils.YYYYMMDD);
        String currentYear = DateUtils.formatToString(new Date(), DateUtils.YYYY);
        String navYear = processDt.substring(0, 4);
        String resultDt;
        if (currentYear.equals(navYear)) {
            resultDt = DateUtils.formatToString(navDate, DateUtils.MM__DD);
        } else {
            resultDt = DateUtils.formatToString(navDate, DateUtils.YYYY_MM_DD);
        }
        return resultDt;
    }


    /**
     * 设置返回对象信息
     */
    private void setBalanceVoInfo(SimuBalanceIndexVo balanceVo, BalanceFundVo balanceFundVo, AcctDataAuthDto acctDataAuthInfo, QueryAcctBalanceResponse balanceResponse, QueryBalanceDetailContext queryBalanceDetailContext) {
        // 持仓基金
        balanceVo.setBalanceFund(balanceFundVo);
        // 是否数据授权
        balanceVo.setIsDataAuth(acctDataAuthInfo.getIsDataAuth());
        // 是否香港隔离
        balanceVo.setIsHkDataQuarantine(acctDataAuthInfo.getIsHkDataQuarantine());
        // 服务器时间
        balanceVo.setServerDate(getCurrentDate());
        // 是否需要授权
        balanceVo.setIsNeedAuth(getIsNeedAuth(acctDataAuthInfo, balanceResponse));
        // 是否含有私募定投 1:是,0:否
        balanceVo.setHighFundInvPlanFlag(queryBalanceDetailContext.getHighFundInvPlanFlag());
    }

    /**
     * 是否需要授权
     *
     * @param acctDataAuthInfo 权限信息
     * @param balanceResponse  持仓接口返回
     * @return 是否需要授权, 1:需要授权,0:不需要授权
     */
    private String getIsNeedAuth(AcctDataAuthDto acctDataAuthInfo, QueryAcctBalanceResponse balanceResponse) {
        if (YesOrNoEnum.YES.getCode().equals(acctDataAuthInfo.getIsDataAuth()) && YesOrNoEnum.NO.getCode().equals(acctDataAuthInfo.getIsHkDataQuarantine())) {
            log.info("已经数据授权,香港数据不隔离,不需要再授权");
            return YesOrNoEnum.NO.getCode();
        }
        if (YesOrNoEnum.NO.getCode().equals(acctDataAuthInfo.getIsDataAuth()) && YesOrNoEnum.YES.getCode().equals(balanceResponse.getHasHZProduct())) {
            log.info("没有数据授权,有好臻,需要授权");
            return YesOrNoEnum.YES.getCode();
        }
        if (YesOrNoEnum.NO.getCode().equals(acctDataAuthInfo.getIsDataAuth()) && YesOrNoEnum.YES.getCode().equals(balanceResponse.getHasHKProduct())) {
            log.info("没有数据授权,有香港,需要授权");
            return YesOrNoEnum.YES.getCode();
        }
        return YesOrNoEnum.NO.getCode();
    }

}
