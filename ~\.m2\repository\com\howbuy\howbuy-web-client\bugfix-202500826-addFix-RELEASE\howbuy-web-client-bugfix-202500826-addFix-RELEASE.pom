<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.howbuy</groupId>
    <artifactId>howbuy-web-client</artifactId>
  <version>bugfix-202500826-addFix-RELEASE</version>
    <name>howbuy-web-client</name>

    <properties>
        <howbuy.version>1.0.0-SNAPSHOT</howbuy.version>
        <com.howbuy.howbuy-persistence.version>release-20250425-hw2.9-RELEASE</com.howbuy.howbuy-persistence.version>
        <com.howbuy.howbuy-cms-client.version>release-20240830-gdlwjs-RELEASE</com.howbuy.howbuy-cms-client.version>
        <com.howbuy.howbuy-content-client.version>release-20250807-zdd043-RELEASE</com.howbuy.howbuy-content-client.version>
        <com.howbuy.howbuy-content-dto.version>release-20240705-ccpzjy-RELEASE</com.howbuy.howbuy-content-dto.version>
        <com.howbuy.webUtil.version>week-20231007-tgzhqzqh-RELEASE</com.howbuy.webUtil.version>
    </properties>

    <profiles>
        <profile>
            <id>dev</id>
            <!--测试环境 e.g: mvn clean install -P dev -->
            <activation>
                <!-- 设置默认激活 -->
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <howbuy.version>1.0.0-SNAPSHOT</howbuy.version>
                <com.howbuy.howbuy-content-client.version>content-server-0.0.1-RELEASE
                </com.howbuy.howbuy-content-client.version>
            </properties>
        </profile>
        <profile>
            <!-- 生产环境 linux 激活 当前 profile e.g: mvn clean install -P prod -->
            <id>prod</id>
            <properties>
                <howbuy.version>1.0.0-release</howbuy.version>
            </properties>
        </profile>
    </profiles>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.howbuy</groupId>
                <artifactId>howbuy-infrastructure</artifactId>
                <version>1.0.0-SNAPSHOT</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <dependencies>
        <dependency>
            <groupId>com.howbuy</groupId>
            <artifactId>howbuy-persistence</artifactId>
            <version>${com.howbuy.howbuy-persistence.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>*</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.howbuy</groupId>
            <artifactId>utils</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- //TODO: dto po 分层后，后续清理依赖 -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-annotation</artifactId>
            <version>3.1.2</version>
            <scope>provided</scope>
            <exclusions>
                <exclusion>
                    <artifactId>mybatis</artifactId>
                    <groupId>org.mybatis</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- //TODO: 待cms 迁移后下线，清理 content-client 依赖 -->
        <dependency>
            <groupId>com.howbuy</groupId>
            <artifactId>howbuy-content-client</artifactId>
            <version>${com.howbuy.howbuy-content-client.version}</version>
        </dependency>
        <dependency>
            <groupId>com.vdurmont</groupId>
            <artifactId>emoji-java</artifactId>
            <version>4.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.83</version>
        </dependency>
        <dependency>
            <groupId>commons-lang</groupId>
            <artifactId>commons-lang</artifactId>
            <version>2.6</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.24</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>3.14.0</version>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <!-- 打包源码 -->
            <plugin>
                <artifactId>maven-source-plugin</artifactId>
                <version>2.1</version>
                <configuration>
                    <attach>true</attach>
                </configuration>
                <executions>
                    <execution>
                        <phase>compile</phase>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>2.3.2</version>
                <configuration>
                    <source>1.6</source>
                    <target>1.6</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
            <plugin>
                <artifactId>maven-dependency-plugin</artifactId>
                <version>3.1.1</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>8</source>
                    <target>8</target>
                </configuration>
            </plugin>
        </plugins>
        <defaultGoal>install</defaultGoal>
    </build>
    <distributionManagement>
        <repository>
            <id>howbuy-release</id>
            <name>howbuy-release</name>
            <url>http://nx-java.howbuy.pa/repository/howbuy-release/</url>
        </repository>
        <snapshotRepository>
            <id>howbuy-snapshot</id>
            <name>howbuy-snapshot</name>
            <url>http://nx-java.howbuy.pa/repository/howbuy-snapshot/</url>
        </snapshotRepository>
    </distributionManagement>
</project>