---
description: 高端订单中心HTTP接口定义和实现规范
globs:
  - "**/*Controller.java"
  - "**/*Request.java"
  - "**/*Response.java"
  - "**/*Vo.java"
alwaysApply: false
---

# 高端订单中心HTTP接口生成规则
# 作者: hongdong.xie
# 日期: 2025-09-08 14:30:00
# 版本: 1.0.0

#==================== HTTP接口生成规范 ====================

# HTTP接口包名与命名规范
http_interface_rules:
  - 查询类控制器主包：com.howbuy.cgi.trade.simu.controller.search
  - 交易类控制器主包：com.howbuy.cgi.trade.simu.controller.trade
  - 通用控制器主包：com.howbuy.cgi.trade.simu.controller.index
  - 子包命名：按具体业务功能命名，如queryacctbalance、queryacctbalancedtl
  - 控制器命名：业务功能+Controller (如QueryAcctBalanceController)
  - 请求类型：业务功能+Request (可选，简单参数可直接用@RequestParam)
  - 响应类型：业务功能+Response 或 业务功能+Vo
  - URL路径规范：/simu/{模块}/{功能}.htm

# 接口修改/新增规则
interface_modify_rules:
  - 判断依据：
    - 设计文档中接口名后标注"——修改"的，表示在原有接口修改
    - 设计文档中接口名后标注"——新增"的，表示新增接口
    - 设计文档中接口名后无标注的，表示新增接口
  - 修改原则：
    - 修改接口时，保持原有的URL路径和方法名不变
    - 修改接口时，需要兼容原有的功能
    - 修改接口时，新增参数采用追加方式，不要删除或修改原有参数
    - 修改接口时，保持原有的注释格式，仅更新内容

#==================== HTTP接口实现规范 ====================

# HTTP控制器实现规范
http_controller_rules:
  - 控制器包名规则：
    - 查询类主包：com.howbuy.cgi.trade.simu.controller.search
    - 交易类主包：com.howbuy.cgi.trade.simu.controller.trade
    - 通用主包：com.howbuy.cgi.trade.simu.controller.index
    - 子包结构：应与业务功能对应，如search.queryacctbalance
  - 控制器命名：业务功能+Controller (如QueryAcctBalanceController)
  - 注解使用：
    - @Controller - 标识为Spring MVC控制器
    - @RequestMapping - 配置请求路径映射
  - 日志记录：使用private static final Logger logger = LoggerFactory.getLogger()
  - Service层交互：通过@Autowired注入所需的Service类

# HTTP接口方法规范
http_method_rules:
  - 方法参数：
    - 必须包含HttpServletRequest request
    - 必须包含HttpServletResponse response
    - 简单参数可使用@RequestParam注解
    - 复杂参数可定义Request对象
  - 返回类型：通常为void，通过response输出JSON
  - 异常处理：使用try-catch处理异常，并输出错误响应
  - 参数验证：在方法开始处进行参数校验
  - 响应输出：使用JsonUtils.toJsonString()输出JSON响应

#==================== APIDOC注释规范 ====================

# HTTP接口APIDOC规范
http_apidoc_rules:
  - 必须包含以下标签：
    - @api {GET/POST} URL路径 方法名
    - @apiVersion 版本号
    - @apiGroup 控制器名
    - @apiName 方法名
    - @apiDescription 接口功能描述
    - @apiParam (请求参数) {类型} 参数名 参数说明
    - @apiParamExample 请求参数示例
    - @apiSuccess (响应结果) {类型} 字段名 字段说明
    - @apiSuccessExample 响应结果示例
  - 特殊要求：
    - 请求和响应示例必须包含真实业务场景数据
    - 字段说明必须包含数据格式要求（如日期格式YYYYMMDD）
    - 枚举值必须说明所有可能的取值（如1-柜台,2-网站,3-电话,4-Wap,5-App,6-机构）
    - 金额字段必须说明精度
    - 生成的apidoc在控制器方法上面

# 控制器注释规范
controller_comment_rules:
  ```
  /**
   * @description: 控制器功能描述
   * @author: hongdong.xie
   * @date: 2025/9/8 14:30
   * @since JDK 1.8
   */
  ```

# 方法注释规范
method_comment_rules:
  ```
  /**
   * @description: 方法功能描述
   * @author: hongdong.xie
   * @date: 2025/9/8 14:30
   * @since JDK 1.8
   */
  ```

# 字段注释规范
field_comment_rules:
  ```
  /**
   * 字段中文名称
   * 可选：附加说明（如：格式要求/取值范围）
   */
  ```

#==================== 参数验证规范 ====================

# 参数验证规则
validation_rules:
  - 必填字符串：判空验证StringUtils.isEmpty()
  - 可选字符串：允许为空或null
  - 必填金额：判空验证且数值格式验证
  - 必填日期：判空验证且日期格式验证
  - 枚举值：验证取值范围是否合法

#==================== 响应对象规范 ====================

# 响应对象规范
response_rules:
  - 响应类包名：com.howbuy.cgi.trade.simu.vo.{业务包}
  - 类命名：业务功能+Response 或 业务功能+Vo
  - 注解使用：@Getter/@Setter
  - 序列化：实现Serializable接口，定义serialVersionUID
  - 字段说明：每个字段必须添加中文注释
  - 基础字段：通常包含returnCode、description等状态字段

#==================== 代码示例 ====================

# HTTP查询接口示例
http_search_controller_example:
  ```java
  /**
   * @description: 查询客户持仓信息控制器
   * @author: hongdong.xie
   * @date: 2025/9/8 14:30
   * @since JDK 1.8
   */
  @Controller
  @RequestMapping("/simu/user")
  public class QueryAcctBalanceController {

      private static final Logger logger = LoggerFactory.getLogger(QueryAcctBalanceController.class);

      @Autowired
      private QueryAcctBalanceService queryAcctBalanceService;

      /**
       * @api {GET} /simu/user/balanceV2.htm indexV2
       * @apiVersion 1.0.0
       * @apiGroup QueryAcctBalanceController
       * @apiName indexV2
       * @apiDescription 私募持仓首页V2
       * @apiParam (请求参数) {String} disCode 分销渠道
       * @apiParam (请求参数) {String} hkSaleFlag 是否只查询香港标识,1:是,0:否
       * @apiParam (请求参数) {String} productCode 产品代码
       * @apiParam (请求参数) {String} productType 产品类型,7-一对多专户,11-私募
       * @apiParam (请求参数) {String} productSubType 产品二级类型:0-现金管理,1-债券,2-类固定收益,3-对冲策略,4-股票策略,5-股权,6-房地产基金,7-其他
       * @apiParam (请求参数) {String} subAccountId 子关联账户id
       * @apiParam (请求参数) {String} hboneNo 一账通号
       * @apiParamExample 请求参数示例
       * productCode=HM001&subAccountId=SA001&disCode=HM&hkSaleFlag=1&productSubType=4&productType=11&hboneNo=HB123456
       * @apiSuccess (响应结果) {Number} totalAsset 总资产
       * @apiSuccess (响应结果) {String} abnormalState 是否存在异常,持仓中只要有一笔存在异常，总资产就需显示计算中标识;1:是,0:否
       * @apiSuccess (响应结果) {String} totalIncomCalStat 总收益计算状态:0-计算中;1-计算成功
       * @apiSuccess (响应结果) {String} showRelatedAccount 展示关联账户入口 1 是 0 否
       * @apiSuccess (响应结果) {String} hasInvite 是否有待处理邀请码,1:有,0:没有
       * @apiSuccess (响应结果) {String} relatedCustName 关联客户姓名
       * @apiSuccess (响应结果) {String} relatedHboneNo 关联客户一账通
       * @apiSuccess (响应结果) {Number} totalUnconfirmedAmt 总待确认金额
       * @apiSuccess (响应结果) {String} isNeedAuth 是否需要授权:1- 是; 0- 否
       * @apiSuccess (响应结果) {Number} totalCurrentAsset 当前总收益
       * @apiSuccess (响应结果) {String} isProfessor 是否专业投资者:1- 是; 0- 否
       * @apiSuccess (响应结果) {String} noTxAcctNo 公募未开户 1 是 0 否
       * @apiSuccess (响应结果) {Number} balanceNum 持仓条数,1个母基金下有2子基金,算2条
       * @apiSuccess (响应结果) {Array} fundSetVoList 产品集合信息
       * @apiSuccess (响应结果) {Number} fundSetVoList.totalAsset 资产合计(元)
       * @apiSuccess (响应结果) {String} fundSetVoList.fundSetType 产品集合类型
       * @apiSuccess (响应结果) {Number} fundSetVoList.balanceNum 持仓笔数
       * @apiSuccess (响应结果) {Array} fundSetVoList.fundBalanceVoList 产品持仓信息
       * @apiSuccess (响应结果) {String} fundSetVoList.fundBalanceVoList.fundCode 产品代码
       * @apiSuccess (响应结果) {String} fundSetVoList.fundBalanceVoList.fundName 产品名称
       * @apiSuccess (响应结果) {Number} fundSetVoList.fundBalanceVoList.totalAsset 总市值(人民币)
       * @apiSuccess (响应结果) {Number} fundSetVoList.fundBalanceVoList.currentAsset 当前收益（人民币）
       * @apiSuccess (响应结果) {String} fundSetVoList.fundBalanceVoList.productType 产品类型,7-一对多专户,11-私募
       * @apiSuccess (响应结果) {String} fundSetVoList.fundBalanceVoList.productSubType 产品二级类型:0-现金管理,1-债券,2-类固定收益,3-对冲策略,4-股票策略,5-股权,6-房地产基金,7-其他
       * @apiSuccess (响应结果) {String} returnCode 返回码
       * @apiSuccess (响应结果) {String} description 描述信息
       * @apiSuccessExample 响应结果示例
       * {"totalAsset":150000.00,"abnormalState":"0","totalIncomCalStat":"1","showRelatedAccount":"1","hasInvite":"0","relatedCustName":"张三","relatedHboneNo":"HB123456","totalUnconfirmedAmt":0.00,"isNeedAuth":"0","totalCurrentAsset":5000.00,"isProfessor":"1","noTxAcctNo":"0","balanceNum":2,"fundSetVoList":[{"totalAsset":150000.00,"fundSetType":"股票策略","balanceNum":2,"fundBalanceVoList":[{"fundCode":"HM001","fundName":"好买基金1号","totalAsset":100000.00,"currentAsset":3000.00,"productType":"11","productSubType":"4"}]}],"returnCode":"0000","description":"成功"}
       */
      @RequestMapping("/balanceV2.htm")
      public void indexV2(HttpServletRequest request, HttpServletResponse response) throws Exception {
          logger.info("私募持仓首页V2查询开始");
          
          try {
              // 1.参数获取
              String disCode = request.getParameter("disCode");
              String hkSaleFlag = request.getParameter("hkSaleFlag");
              String productCode = request.getParameter("productCode");
              String productType = request.getParameter("productType");
              String productSubType = request.getParameter("productSubType");
              String subAccountId = request.getParameter("subAccountId");
              String hboneNo = request.getParameter("hboneNo");
              
              // 2.参数验证
              if (StringUtils.isEmpty(hboneNo)) {
                  throw new BusinessException("一账通号不能为空");
              }
              
              // 3.构建请求对象
              QueryAcctBalanceRequest queryRequest = new QueryAcctBalanceRequest();
              queryRequest.setDisCode(disCode);
              queryRequest.setHkSaleFlag(hkSaleFlag);
              queryRequest.setProductCode(productCode);
              queryRequest.setProductType(productType);
              queryRequest.setProductSubType(productSubType);
              queryRequest.setHboneNo(hboneNo);
              
              // 4.业务逻辑处理
              QueryAcctBalanceResponse queryResponse = queryAcctBalanceService.process(queryRequest);
              
              // 5.返回结果
              response.setContentType("application/json;charset=UTF-8");
              response.getWriter().write(JsonUtils.toJsonString(queryResponse));
              
              logger.info("私募持仓首页V2查询成功");
          } catch (Exception e) {
              logger.error("私募持仓首页V2查询异常：{}", e.getMessage(), e);
              
              // 输出错误响应
              ErrorResponse errorResponse = new ErrorResponse();
              errorResponse.setReturnCode("9999");
              errorResponse.setDescription("系统异常：" + e.getMessage());
              
              response.setContentType("application/json;charset=UTF-8");
              response.getWriter().write(JsonUtils.toJsonString(errorResponse));
          }
      }
  }
  ```

# HTTP请求对象示例
http_request_example:
  ```java
  /**
   * @description: 查询客户持仓请求
   * @author: hongdong.xie
   * @date: 2025/9/8 14:30
   * @since JDK 1.8
   */
  @Getter
  @Setter
  public class QueryAcctBalanceRequest implements Serializable {

      private static final long serialVersionUID = 1L;

      /**
       * 分销渠道
       */
      private String disCode;

      /**
       * 是否只查询香港标识
       * 1-是，0-否
       */
      private String hkSaleFlag;

      /**
       * 产品代码
       */
      private String productCode;

      /**
       * 产品类型
       * 7-一对多专户，11-私募
       */
      private String productType;

      /**
       * 产品二级类型
       * 0-现金管理,1-债券,2-类固定收益,3-对冲策略,4-股票策略,5-股权,6-房地产基金,7-其他
       */
      private String productSubType;

      /**
       * 子关联账户id
       */
      private String subAccountId;

      /**
       * 一账通号
       */
      private String hboneNo;
  }
  ```

# HTTP响应对象示例
http_response_example:
  ```java
  /**
   * @description: 查询客户持仓响应
   * @author: hongdong.xie
   * @date: 2025/9/8 14:30
   * @since JDK 1.8
   */
  @Getter
  @Setter
  public class QueryAcctBalanceResponse implements Serializable {

      private static final long serialVersionUID = 1L;

      /**
       * 总资产
       */
      private BigDecimal totalAsset;

      /**
       * 是否存在异常
       * 1-是，0-否
       */
      private String abnormalState;

      /**
       * 总收益计算状态
       * 0-计算中，1-计算成功
       */
      private String totalIncomCalStat;

      /**
       * 展示关联账户入口
       * 1-是，0-否
       */
      private String showRelatedAccount;

      /**
       * 是否有待处理邀请码
       * 1-有，0-没有
       */
      private String hasInvite;

      /**
       * 关联客户姓名
       */
      private String relatedCustName;

      /**
       * 关联客户一账通
       */
      private String relatedHboneNo;

      /**
       * 总待确认金额
       */
      private BigDecimal totalUnconfirmedAmt;

      /**
       * 是否需要授权
       * 1-是，0-否
       */
      private String isNeedAuth;

      /**
       * 当前总收益
       */
      private BigDecimal totalCurrentAsset;

      /**
       * 是否专业投资者
       * 1-是，0-否
       */
      private String isProfessor;

      /**
       * 公募未开户
       * 1-是，0-否
       */
      private String noTxAcctNo;

      /**
       * 持仓条数
       */
      private Integer balanceNum;

      /**
       * 产品集合信息
       */
      private List<FundSetVo> fundSetVoList;

      /**
       * 返回码
       */
      private String returnCode;

      /**
       * 描述信息
       */
      private String description;
  }
  ```

# 生成指南
generate_guide:
  1. 明确业务需求，确定接口URL和功能
  2. 判断是查询类还是交易类接口
  3. 在正确的包路径下创建控制器类
  4. 按规范编写APIDOC注释，确保包含所有必要标签
  5. 实现控制器方法，包含参数获取、验证、业务处理、响应输出
  6. 创建对应的请求和响应对象（如需要）
  7. 所有字段添加明确的中文注释，说明用途和格式要求
  8. 提供真实的请求和响应示例
  9. 对枚举值和特殊格式字段提供详细说明
  10. 添加完整的日志记录和异常处理

#==================== 查询类与交易类接口的主要区别 ====================

# 查询类与交易类接口差异
search_vs_trade_differences:
  1. 包路径：controller.search.{功能} vs controller.trade.{功能}
  2. 请求方式：通常GET请求 vs 通常POST请求
  3. 幂等性：查询类天然幂等 vs 交易类需要考虑幂等控制
  4. 事务控制：查询类通常无事务 vs 交易类需要事务管理
  5. 权限控制：查询类权限相对宽松 vs 交易类权限控制更严格
  6. 日志记录：查询类日志相对简单 vs 交易类日志记录更详细
  7. 缓存策略：查询类可考虑缓存 vs 交易类通常不缓存
  8. 参数验证：查询类验证相对简单 vs 交易类验证更严格

#==================== 错误处理规范 ====================

# 错误响应规范
error_response_rules:
  - 统一错误响应格式
  - 包含错误码和错误描述
  - 记录详细错误日志
  - 对外屏蔽敏感信息
  - 示例错误响应：
    ```json
    {
      "returnCode": "9999",
      "description": "系统异常"
    }
    ```