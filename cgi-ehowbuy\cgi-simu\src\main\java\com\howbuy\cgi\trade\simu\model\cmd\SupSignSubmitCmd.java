/**
 * Copyright (c) 2020, <PERSON>gH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.cgi.trade.simu.model.cmd;

import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.tms.common.validate.MyValidation;

import java.io.Serializable;
import java.util.List;

/**
 * 签署补签协议
 * <AUTHOR>
 * @date 2020/12/14 11:28
 * @since JDK 1.8
 */
public class SupSignSubmitCmd implements Serializable {

    private static final long serialVersionUID = -2808243466014028719L;

    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "基金代码", isRequired = true, max = 6)
    private String fundCode;
    private List<String> agreementCodeList;
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "密码", isRequired = true)
    private String txPassword;
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "手机号", isRequired = true)
    private String mobile;
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "快捷鉴权验证码", isRequired = true)
    private String authenticateCode;
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "快捷鉴权流水号", isRequired = true)
    private String authenticateSeriousNo;
    /**
     * 分销渠道
     */
    private String disCode;
    /**
     * 好臻产品,手写签名id
     */
    private String sealId;

    public String getDisCode() {
        return disCode;
    }

    public void setDisCode(String disCode) {
        this.disCode = disCode;
    }

    public String getSealId() {
        return sealId;
    }

    public void setSealId(String sealId) {
        this.sealId = sealId;
    }

    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }

    public List<String> getAgreementCodeList() {
        return agreementCodeList;
    }

    public void setAgreementCodeList(List<String> agreementCodeList) {
        this.agreementCodeList = agreementCodeList;
    }

    public String getTxPassword() {
        return txPassword;
    }

    public void setTxPassword(String txPassword) {
        this.txPassword = txPassword;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getAuthenticateCode() {
        return authenticateCode;
    }

    public void setAuthenticateCode(String authenticateCode) {
        this.authenticateCode = authenticateCode;
    }

    public String getAuthenticateSeriousNo() {
        return authenticateSeriousNo;
    }

    public void setAuthenticateSeriousNo(String authenticateSeriousNo) {
        this.authenticateSeriousNo = authenticateSeriousNo;
    }
}