/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.cgi.trade.simu.controller.query.memberinfo;

import com.howbuy.cgi.common.CGIConstants;
import com.howbuy.cgi.common.enums.BizErrorEnum;
import com.howbuy.cgi.common.exception.BizException;
import com.howbuy.cgi.trade.simu.controller.AbstractSimuCGIController;
import com.howbuy.cgi.trade.simu.model.dto.MemberInfoResponse;
import com.howbuy.cgi.trade.simu.service.MemberInfoService;
import com.howbuy.trade.common.session.model.TradeSession;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * @description: 会员信息查询控制器
 * <AUTHOR>
 * @date 2025/9/9 15:15
 * @since JDK 1.8
 */
@Controller
public class MemberInfoController extends AbstractSimuCGIController {

    private static final Logger LOG = LogManager.getLogger(MemberInfoController.class);

    @Autowired
    private MemberInfoService memberInfoService;

    /**
     * @api {POST} /simu/member/info.htm info
     * @apiVersion 1.0.0
     * @apiGroup MemberInfoController
     * @apiName info
     * @apiDescription 会员信息查询接口
     * @apiParam (请求参数) {String} pageSource 页面来源 * 1-我的页面 2-私募持仓页面 3-好臻专区页面  4-好买香港专区页面 5-收益分析页面
     * @apiSuccess (响应结果) {String} memberLevel 用户的会员等级 23301臻享会员 23302私享会员 23303尊享会员
     * @apiSuccess (响应结果) {String} memberBgColor 会员背景色字段 1.红色 2.黑色
     * @apiSuccessExample 响应结果示例
     * {"memberLevel":"23301","memberBgColor":"1"}
     */
    @PostMapping("/simu/member/info.htm")
    public void info(HttpServletRequest request, HttpServletResponse response) throws Exception {
        TradeSession loginInfo = this.getCustSession();
        if (null == loginInfo || null == loginInfo.getUser()) {
            LOG.info("loginInfo is null!");
            throw new BizException(BizErrorEnum.NEED_RELOGIN.getCode(), BizErrorEnum.NEED_RELOGIN.getDesc());
        }
        String pageSource = getString("pageSource");

        // 调用Service层查询会员信息
        MemberInfoResponse result = memberInfoService.queryMemberInfo(
            loginInfo.getUser().getTxAcctNo(),
            loginInfo.getUser().getHboneNo(),
                pageSource
        );

        write(result, CGIConstants.RESPONSE_CONTENT_TYPE_CIPHERTEXT, response);
    }
}