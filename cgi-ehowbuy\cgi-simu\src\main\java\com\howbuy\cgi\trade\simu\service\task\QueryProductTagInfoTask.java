package com.howbuy.cgi.trade.simu.service.task;

import com.howbuy.cgi.trade.simu.model.dto.QueryBalanceContext;
import com.howbuy.interlayer.product.model.HighProductTagInfoModel;
import com.howbuy.interlayer.product.service.HighProductService;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description:查询打标信息
 * @Author: yun.lu
 * Date: 2025/8/4 10:57
 */
public class QueryProductTagInfoTask extends HowbuyBaseTask {
    private HighProductService highProductService;
    private List<String> fundCodes;
    private QueryBalanceContext queryBalanceContext;

    @Override
    protected void callTask() {
        List<HighProductTagInfoModel> productTagInfoList = highProductService.getTagInfoByFundCodeList(fundCodes);
        Map<String, HighProductTagInfoModel> productTagInfoMap = new HashMap<>();
        if (productTagInfoList != null) {
            for (HighProductTagInfoModel model : productTagInfoList) {
                productTagInfoMap.put(model.getFundCode(), model);
            }
        }
        queryBalanceContext.setProductTagInfoMap(productTagInfoMap);
    }

    public HighProductService getHighProductService() {
        return highProductService;
    }

    public void setHighProductService(HighProductService highProductService) {
        this.highProductService = highProductService;
    }

    public List<String> getFundCodes() {
        return fundCodes;
    }

    public void setFundCodes(List<String> fundCodes) {
        this.fundCodes = fundCodes;
    }

    public QueryBalanceContext getQueryBalanceContext() {
        return queryBalanceContext;
    }

    public void setQueryBalanceContext(QueryBalanceContext queryBalanceContext) {
        this.queryBalanceContext = queryBalanceContext;
    }

    public QueryProductTagInfoTask(HighProductService highProductService, List<String> fundCodes, QueryBalanceContext queryBalanceContext) {
        this.highProductService = highProductService;
        this.fundCodes = fundCodes;
        this.queryBalanceContext = queryBalanceContext;
    }
}
