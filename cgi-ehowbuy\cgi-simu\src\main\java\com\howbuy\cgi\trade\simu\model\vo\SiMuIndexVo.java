package com.howbuy.cgi.trade.simu.model.vo;

import com.howbuy.cgi.trade.simu.model.dto.BaseDto;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Description:私募首页接口内容实体
 * @Author: yun.lu
 * Date: 2023/9/27 14:23
 */
public class SiMuIndexVo extends BaseDto {
    /**
     * 总资产
     */
    private BigDecimal totalAsset;

    /**
     * 是否存在异常,持仓中只要有一笔存在异常，总资产就需显示计算中标识;1:是,0:否
     */
    private String abnormalState;

    /**
     * 总收益计算状态:0-计算中;1-计算成功
     */
    private String totalIncomCalStat;
    /**
     * 展示关联账户入口 1 是 0 否
     */
    private String showRelatedAccount;

    /**
     * 是否有待处理邀请码,1:有,0:没有
     */
    private String hasInvite;
    /**
     * 关联客户姓名
     */
    private String relatedCustName;
    /**
     * 关联客户一账通
     */
    private String relatedHboneNo;
    /**
     * 总待确认金额
     */
    private BigDecimal totalUnconfirmedAmt;
    /**
     * 是否需要授权:1- 是; 0- 否
     */
    private String isNeedAuth;
    /**
     * 当前总收益
     */
    private BigDecimal totalCurrentAsset;
    /**
     * 是否专业投资者:1- 是; 0- 否
     */
    private String isProfessor;
    /**
     * 公募未开户 1 是 0 否
     */
    private String noTxAcctNo;

    /**
     * 持仓条数,1个母基金下有2子基金,算2条
     */
    private int balanceNum;

    /**
     * 在途列表文案
     */
    private String onWayMsg;

    /**
     * 在途订单号列表
     */
    private List<OnWayDealVo> onWayDealVoList;


    /**
     * 资金到账提醒文案
     */
    private String fundArrivalMsg;


    /**
     * 是否签署数据授权,1:已授权,0:没有授权
     */
    private String isDataAuth;

    /**
     * 是否香港数据隔离,1:是,0:否
     */
    private String isHkDataQuarantine;

    /**
     * 是否不绑定定登录，用于特定用户不支持微信绑定自登陆,1:不绑定自登陆,0:绑定自登陆
     */
    private String isnologinBind;

    /**
     * 补签协议条数
     */
    private int waitSupSignAgreementNum;

    /**
     * 是否存在密码,1:是,0:否
     */
    private String existPassword;

    /**
     * 是否含有私募定投 1:是,0:否
     */
    private String highFundInvPlanFlag;

    /**
     * 换汇后总现金余额
     */
    private BigDecimal totalExchangeCashBalance;

    /**
     * 换汇后总冻结金额
     */
    private BigDecimal totalExchangeFrozenAmt;

    /**
     * 换汇后总可用余额
     */
    private BigDecimal totalExchangeAvailableBalance;

    /**
     * 是否展示收益分析入口
     */
    private String showShouYi;

    /**
     * 是否展示理财分析入口
     */
    private String showLiCai;

    /**
     * 产品集合信息
     */
    List<FundSetVo> fundSetVoList;


    /**
     * 补签协议产品code
     */
    private List<String> waitSupSignAgreementFundCodeList;

    /**
     * 补签协议产品信息
     */
    private List<WaitSupSignAgreementFundVo> waitSupSignAgreementFundList;

    public BigDecimal getTotalExchangeCashBalance() {
        return totalExchangeCashBalance;
    }

    public void setTotalExchangeCashBalance(BigDecimal totalExchangeCashBalance) {
        this.totalExchangeCashBalance = totalExchangeCashBalance;
    }

    public BigDecimal getTotalExchangeFrozenAmt() {
        return totalExchangeFrozenAmt;
    }

    public void setTotalExchangeFrozenAmt(BigDecimal totalExchangeFrozenAmt) {
        this.totalExchangeFrozenAmt = totalExchangeFrozenAmt;
    }

    public BigDecimal getTotalExchangeAvailableBalance() {
        return totalExchangeAvailableBalance;
    }

    public void setTotalExchangeAvailableBalance(BigDecimal totalExchangeAvailableBalance) {
        this.totalExchangeAvailableBalance = totalExchangeAvailableBalance;
    }

    public BigDecimal getTotalAsset() {
        return totalAsset;
    }

    public void setTotalAsset(BigDecimal totalAsset) {
        this.totalAsset = totalAsset;
    }

    public String getAbnormalState() {
        return abnormalState;
    }

    public String getHasInvite() {
        return hasInvite;
    }

    public void setHasInvite(String hasInvite) {
        this.hasInvite = hasInvite;
    }

    public void setAbnormalState(String abnormalState) {
        this.abnormalState = abnormalState;
    }

    public String getTotalIncomCalStat() {
        return totalIncomCalStat;
    }

    public void setTotalIncomCalStat(String totalIncomCalStat) {
        this.totalIncomCalStat = totalIncomCalStat;
    }

    public String getShowRelatedAccount() {
        return showRelatedAccount;
    }

    public void setShowRelatedAccount(String showRelatedAccount) {
        this.showRelatedAccount = showRelatedAccount;
    }

    public String getRelatedCustName() {
        return relatedCustName;
    }

    public void setRelatedCustName(String relatedCustName) {
        this.relatedCustName = relatedCustName;
    }

    public String getRelatedHboneNo() {
        return relatedHboneNo;
    }

    public void setRelatedHboneNo(String relatedHboneNo) {
        this.relatedHboneNo = relatedHboneNo;
    }

    public BigDecimal getTotalUnconfirmedAmt() {
        return totalUnconfirmedAmt;
    }

    public void setTotalUnconfirmedAmt(BigDecimal totalUnconfirmedAmt) {
        this.totalUnconfirmedAmt = totalUnconfirmedAmt;
    }

    public String getIsHkDataQuarantine() {
        return isHkDataQuarantine;
    }

    public void setIsHkDataQuarantine(String isHkDataQuarantine) {
        this.isHkDataQuarantine = isHkDataQuarantine;
    }

    public String getIsNeedAuth() {
        return isNeedAuth;
    }

    public void setIsNeedAuth(String isNeedAuth) {
        this.isNeedAuth = isNeedAuth;
    }

    public BigDecimal getTotalCurrentAsset() {
        return totalCurrentAsset;
    }

    public void setTotalCurrentAsset(BigDecimal totalCurrentAsset) {
        this.totalCurrentAsset = totalCurrentAsset;
    }

    public String getIsProfessor() {
        return isProfessor;
    }

    public void setIsProfessor(String isProfessor) {
        this.isProfessor = isProfessor;
    }

    public List<String> getWaitSupSignAgreementFundCodeList() {
        return waitSupSignAgreementFundCodeList;
    }

    public void setWaitSupSignAgreementFundCodeList(List<String> waitSupSignAgreementFundCodeList) {
        this.waitSupSignAgreementFundCodeList = waitSupSignAgreementFundCodeList;
    }

    public String getNoTxAcctNo() {
        return noTxAcctNo;
    }

    public void setNoTxAcctNo(String noTxAcctNo) {
        this.noTxAcctNo = noTxAcctNo;
    }

    public int getBalanceNum() {
        return balanceNum;
    }

    public void setBalanceNum(int balanceNum) {
        this.balanceNum = balanceNum;
    }

    public String getOnWayMsg() {
        return onWayMsg;
    }

    public void setOnWayMsg(String onWayMsg) {
        this.onWayMsg = onWayMsg;
    }

    public List<OnWayDealVo> getOnWayDealVoList() {
        return onWayDealVoList;
    }

    public void setOnWayDealVoList(List<OnWayDealVo> onWayDealVoList) {
        this.onWayDealVoList = onWayDealVoList;
    }

    public String getFundArrivalMsg() {
        return fundArrivalMsg;
    }

    public void setFundArrivalMsg(String fundArrivalMsg) {
        this.fundArrivalMsg = fundArrivalMsg;
    }

    public String getIsDataAuth() {
        return isDataAuth;
    }

    public void setIsDataAuth(String isDataAuth) {
        this.isDataAuth = isDataAuth;
    }

    public String getIsnologinBind() {
        return isnologinBind;
    }

    public void setIsnologinBind(String isnologinBind) {
        this.isnologinBind = isnologinBind;
    }

    public int getWaitSupSignAgreementNum() {
        return waitSupSignAgreementNum;
    }

    public void setWaitSupSignAgreementNum(int waitSupSignAgreementNum) {
        this.waitSupSignAgreementNum = waitSupSignAgreementNum;
    }

    public String getExistPassword() {
        return existPassword;
    }

    public void setExistPassword(String existPassword) {
        this.existPassword = existPassword;
    }

    public String getHighFundInvPlanFlag() {
        return highFundInvPlanFlag;
    }

    public void setHighFundInvPlanFlag(String highFundInvPlanFlag) {
        this.highFundInvPlanFlag = highFundInvPlanFlag;
    }

    public List<WaitSupSignAgreementFundVo> getWaitSupSignAgreementFundList() {
        return waitSupSignAgreementFundList;
    }

    public void setWaitSupSignAgreementFundList(List<WaitSupSignAgreementFundVo> waitSupSignAgreementFundList) {
        this.waitSupSignAgreementFundList = waitSupSignAgreementFundList;
    }

    public String getShowShouYi() {
        return showShouYi;
    }

    public void setShowShouYi(String showShouYi) {
        this.showShouYi = showShouYi;
    }

    public String getShowLiCai() {
        return showLiCai;
    }

    public void setShowLiCai(String showLiCai) {
        this.showLiCai = showLiCai;
    }

    public List<FundSetVo> getFundSetVoList() {
        return fundSetVoList;
    }

    public void setFundSetVoList(List<FundSetVo> fundSetVoList) {
        this.fundSetVoList = fundSetVoList;
    }
}
