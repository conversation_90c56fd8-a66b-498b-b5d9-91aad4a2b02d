/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.cgi.trade.simu.model.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @description: 清仓产品详情查询响应DTO
 * <AUTHOR>
 * @date 2025/9/4 14:15
 * @since JDK 1.8
 */
public class FundLiquidationDetailDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 基金名称
     */
    private String fundName;

    /**
     * 基金代码
     */
    private String fundCode;

    /**
     * 单位
     */
    private String unit;

    /**
     * 清仓收益
     */
    private String clearUpIncome;

    /**
     * 清仓后涨跌幅
     */
    private String clearUpRate;

    /**
     * 产品类型
     */
    private String productType;

    /**
     * 产品子类型
     */
    private String productSubType;

    /**
     * 累计持有天数
     */
    private String totalHoldDays;

    /**
     * 累计收益率
     */
    private String accumIncomeRate;

    /**
     * 收益计算中标签
     * 1：有计算中标签 0：无计算中标签
     */
    private String incomeStatus;

    /**
     * 回款金额
     */
    private String cashCollection;

    /**
     * 回款进度
     */
    private String cashCollectionProgress;

    /**
     * 初始投资成本
     */
    private String initInvestCost;

    /**
     * 清仓明细列表
     */
    private List<ClearDetailInfo> clearDetailList;

    // Getter and Setter methods
    public String getFundName() {
        return fundName;
    }

    public void setFundName(String fundName) {
        this.fundName = fundName;
    }

    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getClearUpIncome() {
        return clearUpIncome;
    }

    public void setClearUpIncome(String clearUpIncome) {
        this.clearUpIncome = clearUpIncome;
    }

    public String getClearUpRate() {
        return clearUpRate;
    }

    public void setClearUpRate(String clearUpRate) {
        this.clearUpRate = clearUpRate;
    }

    public String getProductType() {
        return productType;
    }

    public void setProductType(String productType) {
        this.productType = productType;
    }

    public String getProductSubType() {
        return productSubType;
    }

    public void setProductSubType(String productSubType) {
        this.productSubType = productSubType;
    }

    public String getTotalHoldDays() {
        return totalHoldDays;
    }

    public void setTotalHoldDays(String totalHoldDays) {
        this.totalHoldDays = totalHoldDays;
    }

    public String getAccumIncomeRate() {
        return accumIncomeRate;
    }

    public void setAccumIncomeRate(String accumIncomeRate) {
        this.accumIncomeRate = accumIncomeRate;
    }

    public String getIncomeStatus() {
        return incomeStatus;
    }

    public void setIncomeStatus(String incomeStatus) {
        this.incomeStatus = incomeStatus;
    }

    public String getCashCollection() {
        return cashCollection;
    }

    public void setCashCollection(String cashCollection) {
        this.cashCollection = cashCollection;
    }

    public String getCashCollectionProgress() {
        return cashCollectionProgress;
    }

    public void setCashCollectionProgress(String cashCollectionProgress) {
        this.cashCollectionProgress = cashCollectionProgress;
    }

    public String getInitInvestCost() {
        return initInvestCost;
    }

    public void setInitInvestCost(String initInvestCost) {
        this.initInvestCost = initInvestCost;
    }

    public List<ClearDetailInfo> getClearDetailList() {
        return clearDetailList;
    }

    public void setClearDetailList(List<ClearDetailInfo> clearDetailList) {
        this.clearDetailList = clearDetailList;
    }

    /**
     * 清仓明细信息
     */
    public static class ClearDetailInfo implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 基金名称
         */
        private String fundName;

        /**
         * 基金代码
         */
        private String fundCode;

        /**
         * 单位
         */
        private String unit;

        /**
         * 清仓收益
         */
        private String clearUpIncome;

        /**
         * 清仓后涨跌幅
         */
        private String clearUpRate;

        /**
         * 产品类型
         */
        private String productType;

        /**
         * 产品子类型
         */
        private String productSubType;

        /**
         * 累计持有天数
         */
        private Integer totalHoldDays;

        /**
         * 累计收益率
         */
        private String accumIncomeRate;

        /**
         * 收益计算中标签
         * 1：有计算中标签 0：无计算中标签
         */
        private String incomeStatus;

        // Getter and Setter methods
        public String getFundName() {
            return fundName;
        }

        public void setFundName(String fundName) {
            this.fundName = fundName;
        }

        public String getFundCode() {
            return fundCode;
        }

        public void setFundCode(String fundCode) {
            this.fundCode = fundCode;
        }

        public String getUnit() {
            return unit;
        }

        public void setUnit(String unit) {
            this.unit = unit;
        }

        public String getClearUpIncome() {
            return clearUpIncome;
        }

        public void setClearUpIncome(String clearUpIncome) {
            this.clearUpIncome = clearUpIncome;
        }

        public String getClearUpRate() {
            return clearUpRate;
        }

        public void setClearUpRate(String clearUpRate) {
            this.clearUpRate = clearUpRate;
        }

        public String getProductType() {
            return productType;
        }

        public void setProductType(String productType) {
            this.productType = productType;
        }

        public String getProductSubType() {
            return productSubType;
        }

        public void setProductSubType(String productSubType) {
            this.productSubType = productSubType;
        }

        public Integer getTotalHoldDays() {
            return totalHoldDays;
        }

        public void setTotalHoldDays(Integer totalHoldDays) {
            this.totalHoldDays = totalHoldDays;
        }

        public String getAccumIncomeRate() {
            return accumIncomeRate;
        }

        public void setAccumIncomeRate(String accumIncomeRate) {
            this.accumIncomeRate = accumIncomeRate;
        }

        public String getIncomeStatus() {
            return incomeStatus;
        }

        public void setIncomeStatus(String incomeStatus) {
            this.incomeStatus = incomeStatus;
        }
    }
}
