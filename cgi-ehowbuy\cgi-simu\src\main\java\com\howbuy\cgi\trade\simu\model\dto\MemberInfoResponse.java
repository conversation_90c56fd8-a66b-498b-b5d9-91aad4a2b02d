/**
 * Copyright (c) 2025, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.cgi.trade.simu.model.dto;

/**
 * @description: 会员信息数据传输对象
 * <AUTHOR>
 * @date 2025/9/9 15:20
 * @since JDK 1.8
 */
public class MemberInfoResponse {

    /**
     * 用户的会员等级
     * 23301臻享会员
     * 23302私享会员
     * 23303尊享会员
     */
    private String memberLevel;

    /**
     * 会员背景色字段
     * 1.红色
     * 2.黑色
     */
    private String memberBgColor;

    public String getMemberLevel() {
        return memberLevel;
    }

    public void setMemberLevel(String memberLevel) {
        this.memberLevel = memberLevel;
    }

    public String getMemberBgColor() {
        return memberBgColor;
    }

    public void setMemberBgColor(String memberBgColor) {
        this.memberBgColor = memberBgColor;
    }
}