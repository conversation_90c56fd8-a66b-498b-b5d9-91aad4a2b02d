package com.howbuy.cgi.trade.simu.service.task;

import com.howbuy.cgi.trade.simu.model.vo.SiMuIndexVo;
import com.howbuy.cgi.trade.simu.service.QueryAcctOtherInfoService;
import lombok.Data;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

/**
 * @Description:设置收益分析理财分析入口字段
 * @Author: yun.lu
 * Date: 2023/11/14 14:09
 */
@Data
public class SetShouYiLiCaiTask extends HowbuyBaseTask {
    private static final Logger log = LogManager.getLogger(SetShouYiLiCaiTask.class);

    private QueryAcctOtherInfoService queryAcctOtherInfoService;
    private String hbOneNo;
    private SiMuIndexVo siMuIndexVo;

    public SetShouYiLiCaiTask() {
    }

    @Override
    protected void callTask() {
        String needShowLiCai = queryAcctOtherInfoService.getNeedShowLiCai(hbOneNo);
        siMuIndexVo.setShowLiCai(needShowLiCai);
        String needShowShouYi = queryAcctOtherInfoService.getNeedShowShouYi(hbOneNo);
        siMuIndexVo.setShowShouYi(needShowShouYi);
    }
}
