package com.howbuy.cgi.trade.simu.service.task;

import com.howbuy.cgi.trade.simu.model.dto.QueryBalanceContext;
import com.howbuy.interlayer.product.model.HighProductStatInfoModel;
import com.howbuy.interlayer.product.service.HighProductService;
import org.apache.commons.collections.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description:查询产品状态任务
 * @Author: yun.lu
 * Date: 2025/8/4 11:11
 */
public class QueryProductStatusTask extends HowbuyBaseTask {
    private QueryBalanceContext queryBalanceContext;
    private HighProductService highProductService;
    private List<String> productCodeList;

    @Override
    protected void callTask() {
        List<HighProductStatInfoModel> highProductStatInfoModelList = highProductService.getProductStatInfo(productCodeList, null);
        Map<String, HighProductStatInfoModel> productStatMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(highProductStatInfoModelList)) {
            for (HighProductStatInfoModel highProductStatInfoModel : highProductStatInfoModelList) {
                productStatMap.put(highProductStatInfoModel.getFundCode(), highProductStatInfoModel);
            }
        }
        queryBalanceContext.setProductStatMap(productStatMap);
    }

    public QueryBalanceContext getQueryBalanceContext() {
        return queryBalanceContext;
    }

    public void setQueryBalanceContext(QueryBalanceContext queryBalanceContext) {
        this.queryBalanceContext = queryBalanceContext;
    }

    public HighProductService getHighProductService() {
        return highProductService;
    }

    public void setHighProductService(HighProductService highProductService) {
        this.highProductService = highProductService;
    }

    public List<String> getProductCodeList() {
        return productCodeList;
    }

    public void setProductCodeList(List<String> productCodeList) {
        this.productCodeList = productCodeList;
    }

    public QueryProductStatusTask(QueryBalanceContext queryBalanceContext, HighProductService highProductService, List<String> productCodeList) {
        this.queryBalanceContext = queryBalanceContext;
        this.highProductService = highProductService;
        this.productCodeList = productCodeList;
    }
}
