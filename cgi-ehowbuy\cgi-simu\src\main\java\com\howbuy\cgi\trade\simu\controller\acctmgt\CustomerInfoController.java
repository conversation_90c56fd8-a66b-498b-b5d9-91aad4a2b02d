package com.howbuy.cgi.trade.simu.controller.acctmgt;

import com.howbuy.cgi.aspect.controller.AbstractCGIController;
import com.howbuy.cgi.common.CGIConstants;
import com.howbuy.cgi.trade.simu.model.vo.CustomerRiskInfoVo;
import com.howbuy.cgi.trade.simu.service.relatedaccount.AccCenterService;
import com.howbuy.trade.common.session.model.TradeSession;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

/**
 * @Description:用户信息
 * @Author: yun.lu
 * Date: 2023/12/27 9:29
 */
@Controller
public class CustomerInfoController extends AbstractCGIController {
    @Autowired
    private AccCenterService accCenterService;

    /**
     * @api {GET} /simu/customerInfo/riskInfo.htm riskInfo
     * @apiVersion 1.0.0
     * @apiGroup CustomerInfoController
     * @apiName riskInfo
     * @apiDescription 风测融合页信息
     * @apiParam (请求参数) {String} dataTypeList 数据类型字段，JSON数组格式，如["HB000A001","HZ000N001"]，可选参数
     * @apiSuccess (响应结果) {Array} riskInfoDtoList 风险测评信息
     * @apiSuccess (响应结果) {String} riskInfoDtoList.disCode 分销渠道
     * @apiSuccess (响应结果) {String} riskInfoDtoList.investorType 1 普通投资者，2 专业投资者
     * @apiSuccess (响应结果) {String} riskInfoDtoList.custRiskLevel 客户风险等级
     * @apiSuccess (响应结果) {String} riskInfoDtoList.custRiskLevelStr 客户风险等级描述
     * @apiSuccess (响应结果) {String} riskInfoDtoList.custRiskStatus 风测状态
     * @apiSuccess (响应结果) {String} riskInfoDtoList.riskToleranceTerm 风测到期时间 yyyymmdd
     * @apiSuccessExample 响应结果示例
     * {"riskInfoDtoList":[{"custRiskStatus":"ZjyHjf4m","custRiskLevelStr":"yWMz5Ehs","custRiskLevel":"AMKSTATT","disCode":"IoM","riskToleranceTerm":"P9NZD72f","investorType":"xdhf"}]}
     */
    @RequestMapping("/simu/customerInfo/riskInfo.htm")
    public void riskInfo(HttpServletRequest request, HttpServletResponse response) throws Exception {
        TradeSession loginInfo = this.getCustSession();

        // 获取dataTypeList参数
        String[] dataTypeLists = request.getParameterValues("dataTypeList");
        List<String> dataTypeList = null;
        if (dataTypeLists != null && dataTypeLists.length > 0) {
            dataTypeList = Arrays.asList(dataTypeLists);
        }

        CustomerRiskInfoVo customerRiskInfoVo = accCenterService.queryCustomerRiskInfo(loginInfo.getUser().getHboneNo(), dataTypeList);
        write(customerRiskInfoVo, CGIConstants.RESPONSE_CONTENT_TYPE_CIPHERTEXT, response);
    }


}
