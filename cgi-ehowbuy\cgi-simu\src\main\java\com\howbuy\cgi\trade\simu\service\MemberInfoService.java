/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.cgi.trade.simu.service;

import com.howbuy.cgi.common.enums.BizErrorEnum;
import com.howbuy.cgi.common.exception.BizException;
import com.howbuy.cgi.trade.simu.model.dto.MemberInfoResponse;
import com.howbuy.tms.high.orders.facade.search.querymemberinfo.QueryMemberInfoFacade;
import com.howbuy.tms.high.orders.facade.search.querymemberinfo.QueryMemberInfoRequest;
import com.howbuy.tms.high.orders.facade.search.querymemberinfo.QueryMemberInfoResponse;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @description: 会员信息服务类
 * <AUTHOR>
 * @date 2025/9/9 15:25
 * @since JDK 1.8
 */
@Service
public class MemberInfoService {

    private static final Logger LOG = LogManager.getLogger(MemberInfoService.class);

    @Resource
    private QueryMemberInfoFacade queryMemberInfoFacade;

    /**
     * 查询会员信息
     *
     * @param txAcctNo 交易账号
     * @param hboneNo 一账通号
     * @return 会员信息
     */
    public MemberInfoResponse queryMemberInfo(String txAcctNo, String hboneNo,String pageSource) {
        LOG.info("查询会员信息开始，txAcctNo={}, hboneNo={}, pageSource={}", txAcctNo, hboneNo,pageSource);

        try {
            MemberInfoResponse result = new MemberInfoResponse();
            QueryMemberInfoRequest request = new QueryMemberInfoRequest();
            request.setTxAcctNo(txAcctNo);
            request.setHbOneNo(hboneNo);
            request.setPageSource(pageSource);
            QueryMemberInfoResponse response = queryMemberInfoFacade.execute(request);

            result.setMemberLevel(response.getMemberInfo().getMemberLevel()); // 臻享会员
            result.setMemberBgColor(response.getMemberInfo().getBackgroundColor());

            return result;

        } catch (Exception e) {
            LOG.error("查询会员信息异常", e);
            throw new BizException(BizErrorEnum.SYSTEM_ERROR.getCode(), BizErrorEnum.SYSTEM_ERROR.getDesc());
        }
    }
}