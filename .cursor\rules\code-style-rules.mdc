---
description: Code Style and Structure 编码规范和结构
globs: *.java
alwaysApply: false
---

# Your rule content

- You can @ files here
- You can use markdown but dont have to
代码风格:
  命名约定:
    - 变量: camelCase  # 变量名使用小驼峰
    - 方法: camelCase  # 方法名使用小驼峰
    - 类: PascalCase    # 类名使用大驼峰
    - 常量: UPPERCASE_WITH_UNDERSCORES  # 常量使用全大写+下划线分隔
  缩进: 4_spaces  # 使用 4 个空格进行缩进
  每行最大长度: 120  # 每行代码不得超过 120 个字符
  代码规范:
    - 使用阿里巴巴代码规范
    - 使用lombok注解
    - 使用dubbo注解

  导包顺序:
    - 静态导入优先: true
    - 包顺序:
        - java
        - javax
        - org
        - com
  注释要求:
    - Controller层、Service层、repository层和关键业务逻辑必须添加 Javadoc 注释
    - @RequestMapping/@GetMapping 等注解需说明 API 的用途及注意事项
    - 类和方法注释需说明其功能和使用场景
    - 注释需清晰、简洁、准确，避免冗余和模糊
    - 注释需使用中文
    - Dubbo接口注释需说明其功能和使用场景
    - 方法注释规范
      - @description
      - @param
      - @return
      - <AUTHOR> @date

### 注意点
1.不要有魔法值,要么在本类中加静态常量,要么使用枚举类,如果是公共的一些值,可以放在枚举类中,比如:com.howbuy.cgi.trade.simu.common.CGISimuConstants
2.所有的实现逻辑中,不要定义无用的变量,如果这个变量不用,就不要定义
3.还有,如果限定了修改范围,不要乱改,看看是否在需求范围内
4.不要用beanCopy的拷贝,而是用对象赋值，可以抽一个方法做赋值,如果改动的时候遇到beanCopy也改下
### 事件
事件,与事件处理类,注意事件都是继承com.howbuy.tms.high.orders.service.event.HighEvent
处理类都需要继承:com.howbuy.tms.high.orders.service.event.HighEventListener
事件发送:com.howbuy.tms.high.orders.service.event.HighEventPublisher
