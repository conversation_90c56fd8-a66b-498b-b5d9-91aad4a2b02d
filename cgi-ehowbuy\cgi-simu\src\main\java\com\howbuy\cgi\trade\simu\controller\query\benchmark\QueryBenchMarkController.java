/**
 * Copyright (c) 2025, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.cgi.trade.simu.controller.query.benchmark;

import com.howbuy.cgi.trade.simu.controller.AbstractSimuCGIController;

/**
 * 查询基准相关信息
 * <AUTHOR>
 * @date 2025/9/1 15:39
 * @since JDK 1.8
 */
public class QueryBenchMarkController extends AbstractSimuCGIController {

    /**
     * @api {GET} /simu/query/benchmark/querybenchmarkclosepricetrend.htm 查询基准收盘价走势
     * @apiVersion 1.0.0
     * @apiGroup benchmark
     * @apiName queryBenchmarkClosePriceTrend
     * @apiDescription 查询基准收盘价走势
     * @apiParam (请求参数) {String} startDt 开始日期(必填,格式:yyyyMMdd)
     * @apiParam (请求参数) {String} endDt 结束日期(必填,格式:yyyyMMdd)
     * @apiParam (请求参数) {String} benchmarkCode 基准代码(必填)
     * @apiParamExample 请求参数示例
     * startDt=20240101&endDt=20241201&benchmarkCode=000300
     * @apiSuccess (响应结果) {String} code 结果码 0000
     * @apiSuccess (响应结果) {String} desc 结果码描述 成功
     * @apiSuccess (响应结果) {Array} body 指数回报走势图信息列表
     * @apiSuccess (响应结果) {String} body.dt 日期(yyyyMMdd)
     * @apiSuccess (响应结果) {String} body.closingPrice 收盘价格
     * @apiSuccess (响应结果) {String} timestampServer 服务器时间戳
     * @apiSuccessExample 响应结果示例
     * {
     *   "code": "0000",
     *   "desc": "成功",
     *   "body": [
     *     {
     *       "dt": "20241201",
     *       "closingPrice": "3500.25"
     *     },
     *     {
     *       "dt": "20241202",
     *       "closingPrice": "3510.50"
     *     }
     *   ],
     *   "timestampServer": "1756711162758"
     * }
     */

    /**
     * @api {GET} /simu/query/benchmark/querybenchmarkratetrend.htm 查询基准跌涨幅率走势
     * @apiVersion 1.0.0
     * @apiGroup benchmark
     * @apiName queryBenchmarkRateTrend
     * @apiDescription 查询基准跌涨幅率走势
     * @apiParam (请求参数) {String} startDt 开始日期(必填,格式:yyyyMMdd)
     * @apiParam (请求参数) {String} endDt 结束日期(必填,格式:yyyyMMdd)
     * @apiParam (请求参数) {String} benchmarkCode 基准代码(必填)
     * @apiParamExample 请求参数示例
     * startDt=20240101&endDt=20241201&benchmarkCode=000300
     * @apiSuccess (响应结果) {String} code 结果码 0000
     * @apiSuccess (响应结果) {String} desc 结果码描述 成功
     * @apiSuccess (响应结果) {Array} body 指数跌涨幅率走势图信息列表
     * @apiSuccess (响应结果) {String} body.dt 日期(yyyyMMdd)
     * @apiSuccess (响应结果) {String} body.rate 跌涨幅率(百分之)
     * @apiSuccess (响应结果) {String} timestampServer 服务器时间戳
     * @apiSuccessExample 响应结果示例
     * {
     *   "code": "0000",
     *   "desc": "成功",
     *   "body": [
     *     {
     *       "dt": "20241201",
     *       "rate": "5.23"
     *     },
     *     {
     *       "dt": "20241202",
     *       "rate": "1.12"
     *     }
     *   ],
     *   "timestampServer": "1756711162758"
     * }
     */

}
