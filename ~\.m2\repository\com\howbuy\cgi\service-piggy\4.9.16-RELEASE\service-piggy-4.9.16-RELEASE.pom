<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.howbuy.cgi</groupId>
		<artifactId>cgi-service</artifactId>
		<version>4.9.16-RELEASE</version>
	</parent>

	<name>service-piggy</name>
	<artifactId>service-piggy</artifactId>
	<version>4.9.16-RELEASE</version>

	<dependencies>
		<dependency>
			<groupId>com.howbuy</groupId>
			<artifactId>howbuy-fund-client</artifactId>
		</dependency>
		
		<dependency>
			<groupId>com.howbuy.cgi</groupId>
			<artifactId>service-kyc</artifactId>
		</dependency>

		<dependency>
			<groupId>com.howbuy.cgi</groupId>
			<artifactId>service-account</artifactId>
		</dependency>

		<dependency>
			<groupId>com.howbuy.cgi</groupId>
			<artifactId>service-common</artifactId>
		</dependency>


		<dependency>
			<groupId>com.howbuy.common</groupId>
			<artifactId>common-facade</artifactId>
		</dependency>

		<dependency>
			<groupId>com.howbuy.tms</groupId>
			<artifactId>elasticsearch-center-client</artifactId>
		</dependency>

		<dependency>
			<groupId>com.howbuy.tms</groupId>
			<artifactId>tms-common-message-service</artifactId>
		</dependency>

		<dependency>
			<groupId>com.howbuy.tms</groupId>
			<artifactId>tms-common-enums</artifactId>
		</dependency>

        <dependency>
            <groupId>com.howbuy</groupId>
            <artifactId>ftx-order-facade</artifactId>
        </dependency>

    </dependencies>

</project>